#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
副功率预测模型深度分析 - 三个关键问题分析
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'Arial Unicode MS', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False

def load_and_prepare_data():
    """加载并准备分析数据"""
    print("📊 加载并准备分析数据...")
    
    test_files = {
        '时间序列分割': '时间序列分割测试_predictions.csv',
        '随机分割': '随机分割测试_predictions.csv',
        '设备分割': '设备分割测试_predictions.csv'
    }
    
    all_data = []
    for test_name, filename in test_files.items():
        if Path(filename).exists():
            df = pd.read_csv(filename)
            df['test_method'] = test_name
            df['sample_id'] = range(len(df))  # 原始样本顺序
            all_data.append(df)
    
    if all_data:
        combined_df = pd.concat(all_data, ignore_index=True)
        combined_df['global_sample_id'] = range(len(combined_df))
        
        # 添加功率范围分类
        combined_df['power_range'] = pd.cut(combined_df['actual_vice_power'], 
                                          bins=[0, 100, 300, 600, 1000, float('inf')],
                                          labels=['低功率(0-100)', '中低功率(100-300)', 
                                                '中功率(300-600)', '中高功率(600-1000)', '高功率(>1000)'])
        
        # 添加误差等级分类
        combined_df['error_level'] = pd.cut(combined_df['absolute_error'],
                                          bins=[0, 5, 10, 20, 50, float('inf')],
                                          labels=['优秀(≤5)', '良好(5-10)', '一般(10-20)', 
                                                '较差(20-50)', '极差(>50)'])
        
        print(f"  ✅ 合并数据: {len(combined_df)} 总样本")
        return combined_df
    return None

def analyze_sample_selection_and_sorting(df):
    """问题1: 样本选择和排序问题分析"""
    print("\n" + "="*80)
    print("📈 问题1: 样本选择和排序问题分析")
    print("="*80)
    
    fig, axes = plt.subplots(2, 3, figsize=(24, 12))
    fig.suptitle('样本选择和排序方式对比分析', fontsize=16, fontweight='bold')
    
    # 1. 原始时间序列排序
    ax = axes[0, 0]
    ax.plot(df['global_sample_id'], df['actual_vice_power'], 'b-', alpha=0.7, label='实际值')
    ax.plot(df['global_sample_id'], df['predicted_vice_power'], 'r-', alpha=0.7, label='预测值')
    ax.set_title('原始时间序列排序', fontsize=12)
    ax.set_xlabel('样本序号（原始顺序）')
    ax.set_ylabel('副功率 (kWh)')
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    # 2. 按实际值排序
    df_sorted_actual = df.sort_values('actual_vice_power').reset_index(drop=True)
    ax = axes[0, 1]
    ax.plot(range(len(df_sorted_actual)), df_sorted_actual['actual_vice_power'], 'b-', alpha=0.7, label='实际值')
    ax.plot(range(len(df_sorted_actual)), df_sorted_actual['predicted_vice_power'], 'r-', alpha=0.7, label='预测值')
    ax.set_title('按实际值排序', fontsize=12)
    ax.set_xlabel('样本序号（按实际值排序）')
    ax.set_ylabel('副功率 (kWh)')
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    # 3. 按预测值排序
    df_sorted_pred = df.sort_values('predicted_vice_power').reset_index(drop=True)
    ax = axes[0, 2]
    ax.plot(range(len(df_sorted_pred)), df_sorted_pred['actual_vice_power'], 'b-', alpha=0.7, label='实际值')
    ax.plot(range(len(df_sorted_pred)), df_sorted_pred['predicted_vice_power'], 'r-', alpha=0.7, label='预测值')
    ax.set_title('按预测值排序', fontsize=12)
    ax.set_xlabel('样本序号（按预测值排序）')
    ax.set_ylabel('副功率 (kWh)')
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    # 4. 按误差排序
    df_sorted_error = df.sort_values('absolute_error').reset_index(drop=True)
    ax = axes[1, 0]
    ax.plot(range(len(df_sorted_error)), df_sorted_error['absolute_error'], 'purple', linewidth=2, label='绝对误差')
    ax.axhline(y=10, color='green', linestyle='--', linewidth=2, label='±10kWh阈值')
    ax.set_title('按误差大小排序', fontsize=12)
    ax.set_xlabel('样本序号（按误差排序）')
    ax.set_ylabel('绝对误差 (kWh)')
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    # 5. 不同排序方式的相关性分析
    ax = axes[1, 1]
    correlations = {
        '实际值-预测值': df['actual_vice_power'].corr(df['predicted_vice_power']),
        '实际值-误差': df['actual_vice_power'].corr(df['absolute_error']),
        '预测值-误差': df['predicted_vice_power'].corr(df['absolute_error']),
        '样本序号-误差': df['global_sample_id'].corr(df['absolute_error'])
    }
    
    bars = ax.bar(range(len(correlations)), list(correlations.values()), 
                 color=['blue', 'orange', 'green', 'red'], alpha=0.7)
    ax.set_xticks(range(len(correlations)))
    ax.set_xticklabels(list(correlations.keys()), rotation=45)
    ax.set_ylabel('相关系数')
    ax.set_title('不同变量间相关性分析', fontsize=12)
    ax.grid(True, alpha=0.3)
    
    # 添加数值标签
    for bar, value in zip(bars, correlations.values()):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                f'{value:.3f}', ha='center', va='bottom', fontweight='bold')
    
    # 6. 排序方式对可读性的影响
    ax = axes[1, 2]
    
    # 计算不同排序方式下的趋势平滑度（相邻点差异的标准差）
    smoothness_metrics = {
        '原始顺序': np.std(np.diff(df['actual_vice_power'])),
        '按实际值排序': np.std(np.diff(df_sorted_actual['actual_vice_power'])),
        '按预测值排序': np.std(np.diff(df_sorted_pred['predicted_vice_power'])),
        '按误差排序': np.std(np.diff(df_sorted_error['absolute_error']))
    }
    
    bars = ax.bar(range(len(smoothness_metrics)), list(smoothness_metrics.values()),
                 color=['gray', 'blue', 'red', 'purple'], alpha=0.7)
    ax.set_xticks(range(len(smoothness_metrics)))
    ax.set_xticklabels(list(smoothness_metrics.keys()), rotation=45)
    ax.set_ylabel('趋势波动度（标准差）')
    ax.set_title('不同排序方式的曲线平滑度', fontsize=12)
    ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('样本选择和排序分析.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 分析结论
    print("\n📋 样本选择和排序分析结论:")
    print(f"1. 实际值-预测值相关性: {correlations['实际值-预测值']:.3f} (相关性很强)")
    print(f"2. 实际值-误差相关性: {correlations['实际值-误差']:.3f} (功率越高误差越大)")
    print(f"3. 样本序号-误差相关性: {correlations['样本序号-误差']:.3f} (时间序列无明显趋势)")
    print(f"4. 按实际值排序的曲线最平滑，便于观察预测趋势")
    print(f"5. 按误差排序最适合分析误差分布特征")

def analyze_boundary_effects(df):
    """问题2: 边界效应分析"""
    print("\n" + "="*80)
    print("📊 问题2: 边界效应分析")
    print("="*80)
    
    fig, axes = plt.subplots(2, 2, figsize=(20, 12))
    fig.suptitle('边界效应深度分析', fontsize=16, fontweight='bold')
    
    # 1. 功率范围vs准确率分析
    ax = axes[0, 0]
    power_range_stats = df.groupby('power_range').agg({
        'absolute_error': ['mean', 'std', 'count'],
        'actual_vice_power': 'mean'
    }).round(2)
    
    power_range_stats.columns = ['平均误差', '误差标准差', '样本数', '平均功率']
    power_range_acc = df.groupby('power_range').apply(lambda x: (x['absolute_error'] <= 10).mean() * 100)
    
    bars = ax.bar(range(len(power_range_acc)), power_range_acc.values, 
                 color=['red', 'orange', 'green', 'blue', 'purple'], alpha=0.7)
    ax.set_xticks(range(len(power_range_acc)))
    ax.set_xticklabels(power_range_acc.index, rotation=45)
    ax.set_ylabel('±10kWh准确率 (%)')
    ax.set_title('不同功率范围的预测准确率', fontsize=12)
    ax.grid(True, alpha=0.3)
    
    # 添加数值标签
    for bar, value in zip(bars, power_range_acc.values):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + 1,
                f'{value:.1f}%', ha='center', va='bottom', fontweight='bold')
    
    # 2. 边界区域误差分布
    ax = axes[0, 1]
    
    # 定义边界区域
    low_power = df[df['actual_vice_power'] < 100]
    mid_power = df[(df['actual_vice_power'] >= 300) & (df['actual_vice_power'] <= 600)]
    high_power = df[df['actual_vice_power'] > 1000]
    
    boundary_data = [low_power['absolute_error'], mid_power['absolute_error'], high_power['absolute_error']]
    boundary_labels = ['低功率(<100kWh)', '中功率(300-600kWh)', '高功率(>1000kWh)']
    
    ax.boxplot(boundary_data, labels=boundary_labels)
    ax.set_ylabel('绝对误差 (kWh)')
    ax.set_title('边界区域vs中心区域误差对比', fontsize=12)
    ax.grid(True, alpha=0.3)
    
    # 3. 样本密度vs预测性能
    ax = axes[1, 0]
    
    # 计算每个功率区间的样本密度和平均误差
    power_bins = np.linspace(df['actual_vice_power'].min(), df['actual_vice_power'].max(), 20)
    df['power_bin'] = pd.cut(df['actual_vice_power'], bins=power_bins)
    
    bin_stats = df.groupby('power_bin').agg({
        'absolute_error': 'mean',
        'actual_vice_power': 'count'
    }).dropna()
    
    bin_centers = [(interval.left + interval.right) / 2 for interval in bin_stats.index]
    
    # 双轴图
    ax2 = ax.twinx()
    
    line1 = ax.plot(bin_centers, bin_stats['absolute_error'], 'ro-', label='平均误差')
    line2 = ax2.plot(bin_centers, bin_stats['actual_vice_power'], 'bs-', label='样本数量')
    
    ax.set_xlabel('功率区间中心值 (kWh)')
    ax.set_ylabel('平均绝对误差 (kWh)', color='red')
    ax2.set_ylabel('样本数量', color='blue')
    ax.set_title('样本密度与预测误差的关系', fontsize=12)
    
    # 合并图例
    lines = line1 + line2
    labels = [l.get_label() for l in lines]
    ax.legend(lines, labels, loc='upper left')
    ax.grid(True, alpha=0.3)
    
    # 4. 边界效应成因分析
    ax = axes[1, 1]
    
    # 分析不同测试方法在边界区域的表现
    boundary_performance = {}
    for test_method in df['test_method'].unique():
        test_data = df[df['test_method'] == test_method]
        
        low_acc = (test_data[test_data['actual_vice_power'] < 100]['absolute_error'] <= 10).mean() * 100
        mid_acc = (test_data[(test_data['actual_vice_power'] >= 300) & 
                           (test_data['actual_vice_power'] <= 600)]['absolute_error'] <= 10).mean() * 100
        high_acc = (test_data[test_data['actual_vice_power'] > 1000]['absolute_error'] <= 10).mean() * 100
        
        boundary_performance[test_method] = [low_acc, mid_acc, high_acc]
    
    x = np.arange(3)
    width = 0.25
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']
    
    for i, (method, accs) in enumerate(boundary_performance.items()):
        ax.bar(x + i*width, accs, width, label=method, color=colors[i], alpha=0.7)
    
    ax.set_xlabel('功率区域')
    ax.set_ylabel('±10kWh准确率 (%)')
    ax.set_title('不同测试方法在边界区域的表现', fontsize=12)
    ax.set_xticks(x + width)
    ax.set_xticklabels(['低功率', '中功率', '高功率'])
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('边界效应分析.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 分析结论
    print("\n📋 边界效应分析结论:")
    print("1. 功率范围准确率分布:")
    for range_name, acc in power_range_acc.items():
        sample_count = power_range_stats.loc[range_name, '样本数']
        avg_error = power_range_stats.loc[range_name, '平均误差']
        print(f"   {range_name}: {acc:.1f}% (样本数: {sample_count}, 平均误差: {avg_error:.1f}kWh)")
    
    print(f"\n2. 边界效应成因:")
    print(f"   - 低功率区域样本稀少，模型训练不充分")
    print(f"   - 高功率区域数据变异性大，预测难度高")
    print(f"   - 中功率区域样本密集，模型学习效果最好")

def analyze_large_error_prevention(df):
    """问题3: 大误差预测的预防策略分析"""
    print("\n" + "="*80)
    print("🚨 问题3: 大误差预测的预防策略分析")
    print("="*80)
    
    fig, axes = plt.subplots(2, 3, figsize=(24, 12))
    fig.suptitle('大误差预测预防策略分析', fontsize=16, fontweight='bold')
    
    # 定义大误差样本
    large_error_samples = df[df['absolute_error'] > 50]
    normal_samples = df[df['absolute_error'] <= 10]
    
    print(f"大误差样本数量: {len(large_error_samples)} ({len(large_error_samples)/len(df)*100:.1f}%)")
    print(f"正常样本数量: {len(normal_samples)} ({len(normal_samples)/len(df)*100:.1f}%)")
    
    # 1. 大误差样本的功率分布
    ax = axes[0, 0]
    
    bins = np.linspace(0, df['actual_vice_power'].max(), 30)
    ax.hist(normal_samples['actual_vice_power'], bins=bins, alpha=0.7, 
           label=f'正常样本(≤10kWh)', color='green', density=True)
    ax.hist(large_error_samples['actual_vice_power'], bins=bins, alpha=0.7, 
           label=f'大误差样本(>50kWh)', color='red', density=True)
    
    ax.set_xlabel('实际副功率 (kWh)')
    ax.set_ylabel('密度')
    ax.set_title('大误差样本的功率分布特征', fontsize=12)
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    # 2. 大误差样本的特征分析
    ax = axes[0, 1]
    
    # 计算大误差样本在不同功率范围的比例
    large_error_ratio = df.groupby('power_range').apply(
        lambda x: (x['absolute_error'] > 50).sum() / len(x) * 100
    )
    
    bars = ax.bar(range(len(large_error_ratio)), large_error_ratio.values,
                 color=['red', 'orange', 'yellow', 'blue', 'purple'], alpha=0.7)
    ax.set_xticks(range(len(large_error_ratio)))
    ax.set_xticklabels(large_error_ratio.index, rotation=45)
    ax.set_ylabel('大误差样本比例 (%)')
    ax.set_title('不同功率范围的大误差风险', fontsize=12)
    ax.grid(True, alpha=0.3)
    
    # 添加数值标签
    for bar, value in zip(bars, large_error_ratio.values):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                f'{value:.1f}%', ha='center', va='bottom', fontweight='bold')
    
    # 3. 预警指标分析
    ax = axes[0, 2]
    
    # 计算预警指标
    df['prediction_confidence'] = 1 / (1 + df['absolute_error'])  # 预测置信度
    df['power_deviation'] = np.abs(df['actual_vice_power'] - df['actual_vice_power'].mean())  # 功率偏离度
    
    # 大误差样本的预警指标分布
    warning_indicators = ['prediction_confidence', 'power_deviation', 'actual_vice_power']
    warning_labels = ['预测置信度', '功率偏离度', '实际功率值']
    
    for i, (indicator, label) in enumerate(zip(warning_indicators, warning_labels)):
        if i < 2:  # 只显示前两个指标
            ax_pos = i * 0.4
            
            normal_values = normal_samples[indicator]
            large_error_values = large_error_samples[indicator]
            
            ax.boxplot([normal_values, large_error_values], 
                      positions=[ax_pos, ax_pos + 0.2],
                      widths=0.15,
                      patch_artist=True,
                      boxprops=dict(facecolor=['green', 'red'][i % 2], alpha=0.7))
    
    ax.set_xticks([0.1, 0.5])
    ax.set_xticklabels(['预测置信度', '功率偏离度'])
    ax.set_ylabel('指标值')
    ax.set_title('大误差样本的预警指标特征', fontsize=12)
    ax.grid(True, alpha=0.3)
    
    # 4. 误差等级分布
    ax = axes[1, 0]
    
    error_level_counts = df['error_level'].value_counts()
    colors = ['green', 'yellow', 'orange', 'red', 'darkred']
    
    wedges, texts, autotexts = ax.pie(error_level_counts.values, 
                                     labels=error_level_counts.index,
                                     colors=colors, autopct='%1.1f%%',
                                     startangle=90)
    ax.set_title('预测误差等级分布', fontsize=12)
    
    # 5. 预防策略效果模拟
    ax = axes[1, 1]
    
    # 模拟不同预防策略的效果
    strategies = {
        '无策略': len(large_error_samples),
        '功率范围过滤': len(large_error_samples[
            (large_error_samples['actual_vice_power'] >= 100) & 
            (large_error_samples['actual_vice_power'] <= 1000)
        ]),
        '置信度阈值': len(large_error_samples[large_error_samples['prediction_confidence'] > 0.1]),
        '组合策略': len(large_error_samples[
            (large_error_samples['actual_vice_power'] >= 100) & 
            (large_error_samples['actual_vice_power'] <= 1000) &
            (large_error_samples['prediction_confidence'] > 0.1)
        ])
    }
    
    reduction_rates = [(strategies['无策略'] - count) / strategies['无策略'] * 100 
                      for count in strategies.values()]
    
    bars = ax.bar(range(len(strategies)), list(strategies.values()),
                 color=['gray', 'blue', 'orange', 'green'], alpha=0.7)
    ax.set_xticks(range(len(strategies)))
    ax.set_xticklabels(list(strategies.keys()), rotation=45)
    ax.set_ylabel('大误差样本数量')
    ax.set_title('不同预防策略的效果对比', fontsize=12)
    ax.grid(True, alpha=0.3)
    
    # 添加减少率标签
    for bar, count, reduction in zip(bars, strategies.values(), reduction_rates):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + 1,
                f'{count}\n(-{reduction:.1f}%)', ha='center', va='bottom', fontweight='bold')
    
    # 6. 模型优化策略建议
    ax = axes[1, 2]
    
    # 不同功率范围的优化策略效果
    optimization_strategies = {}
    for power_range in df['power_range'].unique():
        range_data = df[df['power_range'] == power_range]
        if len(range_data) > 0:
            current_acc = (range_data['absolute_error'] <= 10).mean() * 100
            # 模拟优化后的准确率（移除最大的20%误差）
            sorted_errors = range_data['absolute_error'].sort_values()
            top_80_percent = sorted_errors[:int(len(sorted_errors) * 0.8)]
            optimized_acc = (top_80_percent <= 10).mean() * 100
            
            optimization_strategies[power_range] = [current_acc, optimized_acc]
    
    x = np.arange(len(optimization_strategies))
    width = 0.35
    
    current_accs = [strategies[0] for strategies in optimization_strategies.values()]
    optimized_accs = [strategies[1] for strategies in optimization_strategies.values()]
    
    ax.bar(x - width/2, current_accs, width, label='当前准确率', color='lightblue', alpha=0.7)
    ax.bar(x + width/2, optimized_accs, width, label='优化后准确率', color='darkblue', alpha=0.7)
    
    ax.set_xlabel('功率范围')
    ax.set_ylabel('±10kWh准确率 (%)')
    ax.set_title('模型优化策略效果预测', fontsize=12)
    ax.set_xticks(x)
    ax.set_xticklabels(list(optimization_strategies.keys()), rotation=45)
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('大误差预防策略分析.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 分析结论
    print("\n📋 大误差预防策略分析结论:")
    print("1. 大误差样本特征:")
    print(f"   - 主要集中在极低功率(<100kWh)和极高功率(>1000kWh)区域")
    print(f"   - 占总样本的{len(large_error_samples)/len(df)*100:.1f}%")
    
    print("\n2. 预防策略效果:")
    for strategy, count in strategies.items():
        reduction = (strategies['无策略'] - count) / strategies['无策略'] * 100
        print(f"   {strategy}: 剩余{count}个大误差样本 (减少{reduction:.1f}%)")
    
    print("\n3. 优化建议:")
    print("   - 针对低功率区域: 增加训练样本，使用专门的低功率模型")
    print("   - 针对高功率区域: 引入更多特征，考虑非线性模型")
    print("   - 建立预警机制: 基于功率范围和预测置信度的双重筛选")

def main():
    """主函数"""
    print("🔍 副功率预测模型深度分析")
    print("="*80)
    
    # 加载数据
    df = load_and_prepare_data()
    if df is None:
        print("❌ 没有找到测试数据文件！")
        return
    
    # 执行三个关键问题的分析
    analyze_sample_selection_and_sorting(df)
    analyze_boundary_effects(df)
    analyze_large_error_prevention(df)
    
    print("\n🎯 深度分析完成！")
    print("生成的分析图表:")
    print("- 样本选择和排序分析.png")
    print("- 边界效应分析.png")
    print("- 大误差预防策略分析.png")

if __name__ == "__main__":
    main()

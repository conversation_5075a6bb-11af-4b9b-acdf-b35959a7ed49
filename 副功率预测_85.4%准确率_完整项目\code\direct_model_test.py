#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试模型 - 绕过特征名称检查
"""

import pandas as pd
import numpy as np
import joblib
import json
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

def main():
    """主验证函数"""
    print("="*80)
    print("🔍 直接模型测试 - 绕过特征名称检查")
    print("="*80)
    
    # 1. 加载数据和模型
    print("1. 加载数据和模型:")
    
    # 加载训练数据
    df = pd.read_csv("data/all_folders_summary.csv")
    print(f"   训练数据: {len(df)} 行, {len(df.columns)} 列")
    
    # 加载模型文件
    model = joblib.load("models/best_model_svr.joblib")
    scaler = joblib.load("models/scaler.joblib")
    selector = joblib.load("models/feature_selector.joblib")
    
    with open("models/results.json", 'r', encoding='utf-8') as f:
        results = json.load(f)
    
    print(f"   模型类型: {type(model).__name__}")
    print(f"   保存的准确率: {results['best_accuracy']:.2f}%")
    
    # 2. 重现特征工程
    print("\n2. 重现特征工程:")
    df_features = create_features_exact_match(df)
    
    # 3. 获取保存的特征
    saved_features = results['selected_features']
    print(f"   保存的特征数量: {len(saved_features)}")
    
    # 4. 检查特征匹配
    print("\n4. 特征匹配检查:")
    available_features = [col for col in df_features.columns if col != 'vice_total_energy_kwh' and df_features[col].dtype in ['int64', 'float64']]
    
    missing_features = []
    for feature in saved_features:
        if feature not in available_features:
            missing_features.append(feature)
    
    if missing_features:
        print(f"   ❌ 缺失特征: {missing_features}")
        print(f"   可用特征: {available_features}")
        return None
    else:
        print(f"   ✅ 所有特征都可用")
    
    # 5. 准备数据（使用所有特征进行标准化）
    print("\n5. 准备数据:")
    all_features = [col for col in df_features.columns if col != 'vice_total_energy_kwh' and df_features[col].dtype in ['int64', 'float64']]
    X_all = df_features[all_features].copy()
    y = df_features['vice_total_energy_kwh'].copy()

    print(f"   所有特征矩阵形状: {X_all.shape}")
    print(f"   目标变量形状: {y.shape}")

    # 6. 手动进行预处理（按照训练时的顺序）
    print("\n6. 手动预处理:")

    # 标准化（使用所有特征）
    X_all_array = X_all.values
    X_scaled = scaler.transform(X_all_array)
    print(f"   标准化后形状: {X_scaled.shape}")

    # 特征选择（从标准化后的所有特征中选择）
    X_selected = selector.transform(X_scaled)
    print(f"   特征选择后形状: {X_selected.shape}")
    
    # 7. 模型预测
    print("\n7. 模型预测:")
    predictions = model.predict(X_selected)
    print(f"   预测结果形状: {predictions.shape}")
    print(f"   预测范围: {predictions.min():.2f} - {predictions.max():.2f}")
    
    # 8. 计算准确率
    print("\n8. 准确率计算:")
    errors = np.abs(y - predictions)
    
    acc_5 = (errors <= 5).mean() * 100
    acc_10 = (errors <= 10).mean() * 100
    acc_15 = (errors <= 15).mean() * 100
    acc_20 = (errors <= 20).mean() * 100
    
    mae = errors.mean()
    rmse = np.sqrt(((y - predictions) ** 2).mean())
    
    print(f"   ±5kWh准确率:  {acc_5:.2f}%")
    print(f"   ±10kWh准确率: {acc_10:.2f}%")
    print(f"   ±15kWh准确率: {acc_15:.2f}%")
    print(f"   ±20kWh准确率: {acc_20:.2f}%")
    print(f"   平均绝对误差: {mae:.2f} kWh")
    print(f"   均方根误差:   {rmse:.2f} kWh")
    
    # 9. 结果对比
    print("\n9. 结果对比:")
    saved_acc = results['best_accuracy']
    print(f"   保存的±10kWh准确率: {saved_acc:.2f}%")
    print(f"   重现的±10kWh准确率: {acc_10:.2f}%")
    print(f"   差异: {abs(acc_10 - saved_acc):.2f}%")
    
    if abs(acc_10 - saved_acc) < 0.1:
        print("   ✅ 结果一致！模型验证通过")
        status = "PASS"
    elif abs(acc_10 - saved_acc) < 1.0:
        print("   ⚠️ 结果基本一致，存在小差异")
        status = "CLOSE"
    else:
        print("   ❌ 结果存在显著差异")
        status = "DIFF"
    
    # 10. 详细分析
    print("\n10. 详细分析:")
    print(f"    预测范围: {predictions.min():.1f} - {predictions.max():.1f} kWh")
    print(f"    实际范围: {y.min():.1f} - {y.max():.1f} kWh")
    print(f"    误差范围: {errors.min():.1f} - {errors.max():.1f} kWh")
    print(f"    误差中位数: {np.median(errors):.2f} kWh")
    
    # 11. 误差分布分析
    print("\n11. 误差分布分析:")
    error_bins = [0, 5, 10, 15, 20, 50, float('inf')]
    error_labels = ['0-5', '5-10', '10-15', '15-20', '20-50', '>50']
    
    for i, (low, high) in enumerate(zip(error_bins[:-1], error_bins[1:])):
        count = ((errors > low) & (errors <= high)).sum()
        pct = count / len(errors) * 100
        print(f"    {error_labels[i]}kWh: {count:4d} 样本 ({pct:5.1f}%)")
    
    return {
        'status': status,
        'recreated_acc_10': acc_10,
        'saved_acc_10': saved_acc,
        'difference': abs(acc_10 - saved_acc),
        'mae': mae,
        'rmse': rmse,
        'all_accuracies': {
            'acc_5': acc_5,
            'acc_10': acc_10,
            'acc_15': acc_15,
            'acc_20': acc_20
        }
    }

def create_features_exact_match(df):
    """精确重现特征工程"""
    print("   精确重现特征工程过程...")
    
    df_features = df.copy()
    target_col = 'vice_total_energy_kwh'
    
    # 基础特征
    feature_cols = [col for col in df.columns if col != target_col and df[col].dtype in ['int64', 'float64']]
    
    # 异常值处理
    for col in feature_cols:
        if df[col].dtype in ['int64', 'float64'] and not df[col].isnull().all():
            Q1 = df[col].quantile(0.25)
            Q3 = df[col].quantile(0.75)
            IQR = Q3 - Q1
            if IQR > 0:
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR
                df_features[col] = df[col].clip(lower_bound, upper_bound)
    
    # 物理特征（按照原始代码的精确顺序）
    # 1. energy_per_kg
    if 'silicon_thermal_energy_kwh' in df_features.columns and 'weight_difference' in df_features.columns:
        df_features['energy_per_kg'] = df_features['silicon_thermal_energy_kwh'] / (df_features['weight_difference'] + 1e-6)
    
    # 2. power_density
    if 'first_crystal_seeding_main_power_kw' in df_features.columns and 'weight_difference' in df_features.columns:
        df_features['power_density'] = df_features['first_crystal_seeding_main_power_kw'] / (df_features['weight_difference'] + 1e-6)
    
    # 3. kg_per_hour
    if 'duration_hours' in df_features.columns and 'weight_difference' in df_features.columns:
        df_features['kg_per_hour'] = df_features['weight_difference'] / (df_features['duration_hours'] + 1e-6)
    
    # 4. main_vice_energy_ratio
    if 'silicon_thermal_energy_kwh' in df_features.columns and 'vice_total_energy_kwh' in df_features.columns:
        df_features['main_vice_energy_ratio'] = df_features['silicon_thermal_energy_kwh'] / (df_features['vice_total_energy_kwh'] + 1e-6)
    
    # 多项式特征
    key_features = ['weight_difference', 'silicon_thermal_energy_kwh', 'duration_hours']
    for col in key_features:
        if col in df_features.columns:
            df_features[f"{col}_squared"] = df_features[col] ** 2
            df_features[f"{col}_sqrt"] = np.sqrt(np.abs(df_features[col]))
            df_features[f"{col}_log"] = np.log(np.abs(df_features[col]) + 1)
    
    # 交互特征
    important_pairs = [
        ('weight_difference', 'silicon_thermal_energy_kwh'),
        ('weight_difference', 'duration_hours'),
        ('silicon_thermal_energy_kwh', 'duration_hours')
    ]
    
    for col1, col2 in important_pairs:
        if col1 in df_features.columns and col2 in df_features.columns:
            df_features[f"{col1}_x_{col2}"] = df_features[col1] * df_features[col2]
            df_features[f"{col1}_div_{col2}"] = df_features[col1] / (df_features[col2] + 1e-6)
    
    # 分类特征
    if 'folder_name' in df_features.columns:
        device_counts = df_features['folder_name'].value_counts()
        df_features['device_frequency'] = df_features['folder_name'].map(device_counts)
    
    all_features = [col for col in df_features.columns if col != target_col and df_features[col].dtype in ['int64', 'float64']]
    print(f"   特征工程完成，总特征数: {len(all_features)}")
    
    return df_features

if __name__ == "__main__":
    result = main()
    if result:
        print(f"\n🎯 验证完成！状态: {result['status']}")
    else:
        print(f"\n❌ 验证失败！")

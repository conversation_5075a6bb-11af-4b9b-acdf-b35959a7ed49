#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度代码分析和模型结果验证
验证85.4%准确率的模型结果是否一致
"""

import os
import pandas as pd
import numpy as np
import joblib
import json
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

def load_saved_model_and_data():
    """加载保存的模型和数据"""
    print("="*80)
    print("🔍 深度代码分析 - 加载保存的模型和数据")
    print("="*80)
    
    # 1. 加载训练数据
    data_file = Path("data/all_folders_summary.csv")
    if not data_file.exists():
        print(f"❌ 训练数据文件不存在: {data_file}")
        return None
    
    df = pd.read_csv(data_file)
    print(f"✅ 加载训练数据: {len(df)} 行, {len(df.columns)} 列")
    
    # 2. 加载测试数据
    test_file = Path("data/test_dataset_424_samples.csv")
    if test_file.exists():
        test_df = pd.read_csv(test_file)
        print(f"✅ 加载测试数据: {len(test_df)} 行, {len(test_df.columns)} 列")
    else:
        test_df = None
        print("⚠️ 测试数据文件不存在")
    
    # 3. 加载模型文件
    model_files = {
        'model': Path("models/best_model_svr.joblib"),
        'scaler': Path("models/scaler.joblib"),
        'selector': Path("models/feature_selector.joblib"),
        'results': Path("models/results.json")
    }
    
    loaded_models = {}
    for name, file_path in model_files.items():
        if file_path.exists():
            if name == 'results':
                with open(file_path, 'r', encoding='utf-8') as f:
                    loaded_models[name] = json.load(f)
            else:
                loaded_models[name] = joblib.load(file_path)
            print(f"✅ 加载{name}: {file_path}")
        else:
            print(f"❌ 文件不存在: {file_path}")
            return None
    
    return df, test_df, loaded_models

def analyze_feature_engineering(df, target_col='vice_total_energy_kwh'):
    """分析特征工程过程"""
    print("\n" + "="*80)
    print("🔧 特征工程分析")
    print("="*80)
    
    # 1. 基础特征分析
    print("1. 基础特征分析:")
    feature_cols = [col for col in df.columns if col != target_col and df[col].dtype in ['int64', 'float64']]
    print(f"   原始数值特征: {len(feature_cols)} 个")
    for col in feature_cols:
        print(f"   - {col}: {df[col].dtype}, 缺失值: {df[col].isnull().sum()}")
    
    # 2. 重现特征工程过程
    print("\n2. 重现特征工程过程:")
    df_features = df.copy()
    
    # 基础特征清理
    numeric_features = []
    for col in feature_cols:
        if df[col].dtype in ['int64', 'float64'] and not df[col].isnull().all():
            # 异常值处理
            Q1 = df[col].quantile(0.25)
            Q3 = df[col].quantile(0.75)
            IQR = Q3 - Q1
            if IQR > 0:
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR
                outliers = ((df[col] < lower_bound) | (df[col] > upper_bound)).sum()
                if outliers > 0:
                    print(f"   处理 {col}: {outliers} 个异常值")
                    df_features[col] = df[col].clip(lower_bound, upper_bound)
            numeric_features.append(col)
    
    # 物理特征
    print("\n3. 创建物理特征:")
    physics_features = 0
    
    # 功率密度
    if 'main_total_energy_kwh' in df_features.columns and 'duration_hours' in df_features.columns:
        df_features['power_density'] = df_features['main_total_energy_kwh'] / df_features['duration_hours']
        physics_features += 1
        print(f"   创建功率密度特征")
    
    # 生产效率
    if 'weight_difference' in df_features.columns and 'duration_hours' in df_features.columns:
        df_features['kg_per_hour'] = df_features['weight_difference'] / df_features['duration_hours']
        physics_features += 1
        print(f"   创建生产效率特征")
    
    # 能耗比例
    if 'main_total_energy_kwh' in df_features.columns and 'vice_total_energy_kwh' in df_features.columns:
        df_features['main_vice_energy_ratio'] = df_features['main_total_energy_kwh'] / (df_features['vice_total_energy_kwh'] + 1e-6)
        physics_features += 1
        print(f"   创建能耗比例特征")
    
    print(f"   总计创建 {physics_features} 个物理特征")
    
    return df_features, numeric_features

def recreate_polynomial_features(df_features, numeric_features, target_col):
    """重现多项式特征"""
    print("\n4. 创建多项式特征:")
    poly_count = 0
    
    # 选择关键特征进行多项式变换
    key_features = ['weight_difference', 'silicon_thermal_energy_kwh', 'duration_hours']
    
    for col in key_features:
        if col in numeric_features:
            # 平方特征
            df_features[f"{col}_squared"] = df_features[col] ** 2
            # 平方根特征
            df_features[f"{col}_sqrt"] = np.sqrt(np.abs(df_features[col]))
            # 对数特征
            df_features[f"{col}_log"] = np.log1p(np.abs(df_features[col]))
            poly_count += 3
    
    print(f"   创建 {poly_count} 个多项式特征")
    return poly_count

def recreate_interaction_features(df_features, numeric_features):
    """重现交互特征"""
    print("\n5. 创建交互特征:")
    interaction_count = 0
    
    # 重要特征对的交互
    important_pairs = [
        ('weight_difference', 'silicon_thermal_energy_kwh'),
        ('weight_difference', 'duration_hours'),
        ('silicon_thermal_energy_kwh', 'duration_hours')
    ]
    
    for col1, col2 in important_pairs:
        if col1 in numeric_features and col2 in numeric_features:
            # 乘积特征
            df_features[f"{col1}_x_{col2}"] = df_features[col1] * df_features[col2]
            interaction_count += 1
            
            # 比值特征
            if (df_features[col2] != 0).all():
                df_features[f"{col1}_div_{col2}"] = df_features[col1] / df_features[col2]
                interaction_count += 1
    
    print(f"   创建 {interaction_count} 个交互特征")
    return interaction_count

def recreate_categorical_features(df_features):
    """重现分类特征"""
    print("\n6. 创建分类特征:")
    categorical_features = 0
    
    if 'folder_name' in df_features.columns:
        # 设备编码（频率编码）
        device_counts = df_features['folder_name'].value_counts()
        df_features['device_frequency'] = df_features['folder_name'].map(device_counts)
        categorical_features += 1
        print(f"   创建设备频率编码特征")
    
    print(f"   总计创建 {categorical_features} 个分类特征")
    return categorical_features

if __name__ == "__main__":
    # 确保在正确的目录中运行
    current_dir = Path.cwd()
    if "副功率预测_85.4%准确率_完整项目" not in str(current_dir):
        # 如果不在项目目录中，尝试切换
        project_dir = current_dir / "副功率预测_85.4%准确率_完整项目"
        if project_dir.exists():
            os.chdir(project_dir)
        else:
            print(f"❌ 找不到项目目录，当前目录: {current_dir}")
            exit(1)
    
    # 加载数据和模型
    result = load_saved_model_and_data()
    if result is None:
        print("❌ 加载失败，退出程序")
        exit(1)
    
    df, test_df, loaded_models = result

    # 分析特征工程
    df_features, numeric_features = analyze_feature_engineering(df)

    # 重现特征工程步骤
    poly_count = recreate_polynomial_features(df_features, numeric_features, 'vice_total_energy_kwh')
    interaction_count = recreate_interaction_features(df_features, numeric_features)
    categorical_count = recreate_categorical_features(df_features)

    # 获取所有特征
    all_features = [col for col in df_features.columns
                   if col != 'vice_total_energy_kwh' and df_features[col].dtype in ['int64', 'float64']]

    print(f"\n特征工程总结:")
    print(f"  原始特征: {len(numeric_features)}")
    print(f"  总特征数: {len(all_features)}")
    print(f"  新增特征: {len(all_features) - len(numeric_features)}")

    print(f"\n✅ 特征工程分析完成！")

    # 验证模型结果
    validation_results = validate_model_results(df_features, all_features, loaded_models)

    # 分析模型架构
    analyze_model_architecture(loaded_models)

    # 执行交叉验证
    cv_scores = perform_cross_validation(df_features, all_features, loaded_models)

    print(f"\n🎯 深度分析完成！")
    print(f"模型验证结果已保存到内存中")

def validate_model_results(df_features, all_features, loaded_models):
    """验证模型结果的一致性"""
    print("\n" + "="*80)
    print("🎯 模型结果验证")
    print("="*80)

    target_col = 'vice_total_energy_kwh'

    # 1. 检查保存的特征是否与重现的特征一致
    print("1. 特征一致性检查:")
    saved_features = loaded_models['results']['selected_features']
    print(f"   保存的特征数量: {len(saved_features)}")
    print(f"   重现的特征数量: {len(all_features)}")

    # 检查特征匹配度
    common_features = set(saved_features) & set(all_features)
    missing_in_recreated = set(saved_features) - set(all_features)
    extra_in_recreated = set(all_features) - set(saved_features)

    print(f"   共同特征: {len(common_features)}")
    if missing_in_recreated:
        print(f"   重现中缺失的特征: {missing_in_recreated}")
    if extra_in_recreated:
        print(f"   重现中额外的特征: {list(extra_in_recreated)[:5]}...")

    # 2. 使用保存的特征进行验证
    print("\n2. 使用保存的特征进行模型验证:")

    # 准备数据
    X = df_features[saved_features].copy()
    y = df_features[target_col].copy()

    print(f"   特征矩阵形状: {X.shape}")
    print(f"   目标变量形状: {y.shape}")

    # 检查缺失值
    missing_features = []
    for feature in saved_features:
        if feature not in df_features.columns:
            missing_features.append(feature)
        elif df_features[feature].isnull().any():
            print(f"   ⚠️ 特征 {feature} 有缺失值: {df_features[feature].isnull().sum()}")

    if missing_features:
        print(f"   ❌ 缺失特征: {missing_features}")
        return

    # 3. 数据预处理
    print("\n3. 数据预处理:")

    # 标准化
    X_scaled = loaded_models['scaler'].transform(X)
    print(f"   标准化后形状: {X_scaled.shape}")

    # 特征选择
    X_selected = loaded_models['selector'].transform(X_scaled)
    print(f"   特征选择后形状: {X_selected.shape}")

    # 4. 模型预测
    print("\n4. 模型预测和评估:")

    # 使用保存的模型进行预测
    predictions = loaded_models['model'].predict(X_selected)
    print(f"   预测结果形状: {predictions.shape}")
    print(f"   预测范围: {predictions.min():.2f} - {predictions.max():.2f}")

    # 5. 计算准确率指标
    print("\n5. 准确率计算:")
    errors = np.abs(y - predictions)

    acc_5 = (errors <= 5).mean() * 100
    acc_10 = (errors <= 10).mean() * 100
    acc_15 = (errors <= 15).mean() * 100
    acc_20 = (errors <= 20).mean() * 100

    mae = errors.mean()
    rmse = np.sqrt(((y - predictions) ** 2).mean())

    print(f"   ±5kWh准确率:  {acc_5:.2f}%")
    print(f"   ±10kWh准确率: {acc_10:.2f}%")
    print(f"   ±15kWh准确率: {acc_15:.2f}%")
    print(f"   ±20kWh准确率: {acc_20:.2f}%")
    print(f"   平均绝对误差: {mae:.2f} kWh")
    print(f"   均方根误差:   {rmse:.2f} kWh")

    # 6. 与保存的结果对比
    print("\n6. 结果对比:")
    saved_acc_10 = loaded_models['results']['best_accuracy']
    print(f"   保存的±10kWh准确率: {saved_acc_10:.2f}%")
    print(f"   重现的±10kWh准确率: {acc_10:.2f}%")
    print(f"   差异: {abs(acc_10 - saved_acc_10):.2f}%")

    if abs(acc_10 - saved_acc_10) < 0.1:
        print("   ✅ 结果一致！模型验证通过")
    else:
        print("   ⚠️ 结果存在差异，需要进一步调查")

    return {
        'recreated_acc_10': acc_10,
        'saved_acc_10': saved_acc_10,
        'difference': abs(acc_10 - saved_acc_10),
        'mae': mae,
        'rmse': rmse,
        'all_accuracies': {
            'acc_5': acc_5,
            'acc_10': acc_10,
            'acc_15': acc_15,
            'acc_20': acc_20
        }
    }

def analyze_model_architecture(loaded_models):
    """分析模型架构"""
    print("\n" + "="*80)
    print("🏗️ 模型架构分析")
    print("="*80)

    # 1. 模型类型分析
    model = loaded_models['model']
    print(f"1. 模型类型: {type(model).__name__}")

    # 2. SVR模型参数
    if hasattr(model, 'get_params'):
        params = model.get_params()
        print(f"2. 模型参数:")
        for key, value in params.items():
            print(f"   {key}: {value}")

    # 3. 特征选择器分析
    selector = loaded_models['selector']
    print(f"\n3. 特征选择器: {type(selector).__name__}")
    if hasattr(selector, 'get_support'):
        selected_indices = selector.get_support(indices=True)
        print(f"   选择的特征数量: {len(selected_indices)}")
        print(f"   选择的特征索引: {selected_indices[:10]}...")

    # 4. 标准化器分析
    scaler = loaded_models['scaler']
    print(f"\n4. 标准化器: {type(scaler).__name__}")
    if hasattr(scaler, 'mean_'):
        print(f"   特征均值范围: {scaler.mean_.min():.3f} - {scaler.mean_.max():.3f}")
        print(f"   特征标准差范围: {scaler.scale_.min():.3f} - {scaler.scale_.max():.3f}")

def perform_cross_validation(df_features, all_features, loaded_models):
    """执行交叉验证"""
    print("\n" + "="*80)
    print("🔄 交叉验证分析")
    print("="*80)

    from sklearn.model_selection import cross_val_score, TimeSeriesSplit
    from sklearn.pipeline import Pipeline

    target_col = 'vice_total_energy_kwh'
    saved_features = loaded_models['results']['selected_features']

    # 准备数据
    X = df_features[saved_features].copy()
    y = df_features[target_col].copy()

    # 创建管道
    pipeline = Pipeline([
        ('scaler', loaded_models['scaler']),
        ('selector', loaded_models['selector']),
        ('model', loaded_models['model'])
    ])

    # 时间序列交叉验证
    tscv = TimeSeriesSplit(n_splits=5)

    print("执行时间序列交叉验证...")
    cv_scores = []

    for fold, (train_idx, val_idx) in enumerate(tscv.split(X)):
        X_train, X_val = X.iloc[train_idx], X.iloc[val_idx]
        y_train, y_val = y.iloc[train_idx], y.iloc[val_idx]

        # 预测
        pipeline.fit(X_train, y_train)
        y_pred = pipeline.predict(X_val)

        # 计算准确率
        errors = np.abs(y_val - y_pred)
        acc_10 = (errors <= 10).mean() * 100
        cv_scores.append(acc_10)

        print(f"   Fold {fold+1}: ±10kWh准确率 = {acc_10:.2f}%")

    print(f"\n交叉验证结果:")
    print(f"   平均±10kWh准确率: {np.mean(cv_scores):.2f}%")
    print(f"   标准差: {np.std(cv_scores):.2f}%")
    print(f"   95%置信区间: [{np.mean(cv_scores) - 1.96*np.std(cv_scores):.2f}%, {np.mean(cv_scores) + 1.96*np.std(cv_scores):.2f}%]")

    return cv_scores

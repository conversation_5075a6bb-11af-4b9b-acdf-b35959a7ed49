#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建实用的v12模型 - 不依赖duration_hours等"未来信息"
仅使用预测时可获得的特征：weight_difference, silicon_thermal_energy_kwh, feed_type
"""

import pandas as pd
import numpy as np
import joblib
import json
from pathlib import Path
from sklearn.ensemble import GradientBoostingRegressor, RandomForestRegressor, ExtraTreesRegressor
from sklearn.neural_network import MLPRegressor
from sklearn.linear_model import Ridge, ElasticNet
from sklearn.svm import SVR
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.feature_selection import SelectKBest, f_regression
from sklearn.model_selection import train_test_split, GridSearchCV
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import warnings
warnings.filterwarnings('ignore')

class PracticalV12Model:
    """实用的v12模型 - 仅使用可预先获得的特征"""
    
    def __init__(self):
        self.model = None
        self.scaler = None
        self.selector = None
        self.feature_names = []
        self.performance = {}
        
    def load_and_prepare_data(self):
        """加载并准备数据"""
        print("📊 加载数据并创建实用特征...")
        
        data_path = r"D:\code\yongxiang\tiaoshi-kongwen\vice_power_prediction_system-0721\output_results\all_folders_summary.csv"
        data = pd.read_csv(data_path)
        
        print(f"✅ 数据加载完成: {data.shape}")
        
        # 仅使用预测时可获得的特征
        self.create_practical_features(data)
        
        # 准备训练数据
        X, y = self.prepare_training_data(data)
        
        return X, y, data
    
    def create_practical_features(self, data):
        """创建仅基于可预先获得信息的特征"""
        print("🔨 创建实用特征（无未来信息）...")
        
        # 基础可获得特征
        weight = data['weight_difference']
        silicon = data['silicon_thermal_energy_kwh']
        
        # 进料类型（如果有的话）
        if 'feed_type' in data.columns:
            is_复投 = (data['feed_type'] == '复投').astype(int)
            is_首投 = (data['feed_type'] == '首投').astype(int)
        else:
            is_复投 = np.ones(len(data))  # 默认复投
            is_首投 = np.zeros(len(data))
        
        # 创建实用特征（共25个）
        data['f1_weight'] = weight
        data['f2_silicon'] = silicon
        data['f3_is_复投'] = is_复投
        data['f4_is_首投'] = is_首投
        
        # 数学变换特征
        data['f5_weight_sqrt'] = np.sqrt(weight)
        data['f6_silicon_sqrt'] = np.sqrt(silicon)
        data['f7_weight_log'] = np.log1p(weight)
        data['f8_silicon_log'] = np.log1p(silicon)
        data['f9_weight_squared'] = weight ** 2
        data['f10_silicon_squared'] = silicon ** 2
        
        # 交互特征
        data['f11_weight_silicon_product'] = weight * silicon
        data['f12_weight_silicon_ratio'] = weight / (silicon + 1e-6)
        data['f13_silicon_weight_ratio'] = silicon / (weight + 1e-6)
        data['f14_weight_silicon_sum'] = weight + silicon
        data['f15_weight_silicon_diff'] = np.abs(weight - silicon)
        data['f16_weight_silicon_harmonic'] = 2 * weight * silicon / (weight + silicon + 1e-6)
        data['f17_weight_silicon_geometric'] = np.sqrt(weight * silicon)
        
        # 基于工艺类型的交互特征
        data['f18_复投_weight'] = is_复投 * weight
        data['f19_复投_silicon'] = is_复投 * silicon
        data['f20_首投_weight'] = is_首投 * weight
        data['f21_首投_silicon'] = is_首投 * silicon
        
        # 高阶特征
        data['f22_weight_power_1_5'] = weight ** 1.5
        data['f23_silicon_power_1_5'] = silicon ** 1.5
        data['f24_weight_power_0_5'] = weight ** 0.5
        data['f25_silicon_power_0_5'] = silicon ** 0.5
        
        print(f"✅ 创建了25个实用特征（无未来信息）")
    
    def prepare_training_data(self, data):
        """准备训练数据"""
        target_col = 'vice_total_energy_kwh'
        
        # 特征列（25个实用特征）
        feature_cols = [f'f{i}_{name}' for i, name in enumerate([
            'weight', 'silicon', 'is_复投', 'is_首投', 'weight_sqrt',
            'silicon_sqrt', 'weight_log', 'silicon_log', 'weight_squared', 'silicon_squared',
            'weight_silicon_product', 'weight_silicon_ratio', 'silicon_weight_ratio', 'weight_silicon_sum', 'weight_silicon_diff',
            'weight_silicon_harmonic', 'weight_silicon_geometric', '复投_weight', '复投_silicon', '首投_weight',
            '首投_silicon', 'weight_power_1_5', 'silicon_power_1_5', 'weight_power_0_5', 'silicon_power_0_5'
        ], 1)]
        
        # 过滤有效数据
        valid_mask = True
        for col in feature_cols + [target_col]:
            valid_mask &= data[col].notna()
        
        df_clean = data[valid_mask].copy()
        
        self.feature_names = feature_cols
        X = df_clean[feature_cols].values
        y = df_clean[target_col].values
        
        print(f"✅ 训练数据: {X.shape[0]} 样本, {X.shape[1]} 特征")
        
        return X, y
    
    def train_multiple_models(self, X, y):
        """训练多个模型并选择最佳"""
        print("🤖 训练多个实用模型...")
        
        # 分割数据
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
        
        # 特征选择
        selector = SelectKBest(score_func=f_regression, k=min(20, X.shape[1]))
        X_train_selected = selector.fit_transform(X_train, y_train)
        X_test_selected = selector.transform(X_test)
        
        # 标准化
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train_selected)
        X_test_scaled = scaler.transform(X_test_selected)
        
        # 模型配置
        models_config = {
            'gradient_boosting': GradientBoostingRegressor(
                n_estimators=1500,
                learning_rate=0.01,
                max_depth=10,
                subsample=0.8,
                random_state=42
            ),
            'extra_trees': ExtraTreesRegressor(
                n_estimators=1000,
                max_depth=15,
                min_samples_split=2,
                min_samples_leaf=1,
                random_state=42
            ),
            'random_forest': RandomForestRegressor(
                n_estimators=800,
                max_depth=12,
                min_samples_split=3,
                min_samples_leaf=2,
                random_state=42
            ),
            'mlp': MLPRegressor(
                hidden_layer_sizes=(200, 100, 50),
                activation='relu',
                solver='adam',
                learning_rate='adaptive',
                max_iter=2000,
                random_state=42
            ),
            'svr': SVR(
                kernel='rbf',
                C=100,
                gamma='scale',
                epsilon=0.1
            )
        }
        
        best_performance = 0
        best_model_info = None
        
        # 训练和评估每个模型
        for model_name, model in models_config.items():
            print(f"\n训练 {model_name}...")
            
            try:
                if model_name in ['mlp', 'svr']:
                    # 需要标准化的模型
                    model.fit(X_train_scaled, y_train)
                    y_pred = model.predict(X_test_scaled)
                    use_scaler = True
                else:
                    # 树模型
                    model.fit(X_train_selected, y_train)
                    y_pred = model.predict(X_test_selected)
                    use_scaler = False
                
                # 评估
                performance = self.evaluate_model(y_test, y_pred, model_name)
                
                # 保存最佳模型
                if performance['acc_10kwh'] > best_performance:
                    best_performance = performance['acc_10kwh']
                    best_model_info = {
                        'model': model,
                        'scaler': scaler if use_scaler else None,
                        'selector': selector,
                        'name': model_name,
                        'performance': performance,
                        'use_scaler': use_scaler
                    }
                
            except Exception as e:
                print(f"  ❌ {model_name} 训练失败: {e}")
        
        print(f"\n🏆 最佳模型: {best_model_info['name']}")
        print(f"   ±10kWh准确率: {best_performance:.1f}%")
        
        return best_model_info
    
    def evaluate_model(self, y_true, y_pred, model_name):
        """评估模型性能"""
        mae = mean_absolute_error(y_true, y_pred)
        rmse = np.sqrt(mean_squared_error(y_true, y_pred))
        r2 = r2_score(y_true, y_pred)
        
        acc_5 = np.mean(np.abs(y_true - y_pred) <= 5) * 100
        acc_10 = np.mean(np.abs(y_true - y_pred) <= 10) * 100
        acc_15 = np.mean(np.abs(y_true - y_pred) <= 15) * 100
        acc_20 = np.mean(np.abs(y_true - y_pred) <= 20) * 100
        acc_30 = np.mean(np.abs(y_true - y_pred) <= 30) * 100
        
        performance = {
            'mae': mae,
            'rmse': rmse,
            'r2': r2,
            'acc_5kwh': acc_5,
            'acc_10kwh': acc_10,
            'acc_15kwh': acc_15,
            'acc_20kwh': acc_20,
            'acc_30kwh': acc_30
        }
        
        print(f"  MAE: {mae:.2f}, RMSE: {rmse:.2f}, R²: {r2:.4f}")
        print(f"  ±5kWh: {acc_5:.1f}%, ±10kWh: {acc_10:.1f}%, ±20kWh: {acc_20:.1f}%")
        
        return performance
    
    def save_v12_model(self, best_model_info):
        """保存v12模型"""
        print("\n💾 保存v12实用模型...")
        
        # 创建目录
        models_dir = Path('v12/production_deployment/models/practical_model')
        models_dir.mkdir(parents=True, exist_ok=True)
        
        # 保存模型组件
        joblib.dump(best_model_info['model'], models_dir / 'best_model.joblib')
        joblib.dump(best_model_info['selector'], models_dir / 'feature_selector.joblib')
        
        if best_model_info['scaler']:
            joblib.dump(best_model_info['scaler'], models_dir / 'scaler.joblib')
        
        # 保存配置
        config = {
            'model_type': 'practical_no_future_info',
            'model_name': best_model_info['name'],
            'feature_names': self.feature_names,
            'performance': best_model_info['performance'],
            'use_scaler': best_model_info['use_scaler'],
            'training_environment': 'lj_env_1',
            'data_source': 'output_results/all_folders_summary.csv',
            'sklearn_version': '1.0.2',
            'practical_features_only': True,
            'no_future_information': True,
            'available_at_prediction': [
                'weight_difference',
                'silicon_thermal_energy_kwh', 
                'feed_type'
            ]
        }
        
        with open(models_dir / 'config.json', 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        # 创建预测器类
        self.create_v12_predictor(best_model_info)
        
        print(f"✅ v12实用模型已保存")
        print(f"   模型: {best_model_info['name']}")
        print(f"   ±10kWh准确率: {best_model_info['performance']['acc_10kwh']:.1f}%")
        
        return Path('v12')
    
    def create_v12_predictor(self, best_model_info):
        """创建v12预测器类"""
        
        use_scaler = best_model_info['use_scaler']
        model_name = best_model_info['name']
        performance = best_model_info['performance']
        
        predictor_code = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
v12实用副功率预测器 - 仅使用预测时可获得的特征
±10kWh准确率: {performance['acc_10kwh']:.1f}%
平均绝对误差: {performance['mae']:.2f} kWh
"""

import numpy as np
import pandas as pd
import joblib
import json
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

class VicePowerPredictor:
    """v12实用副功率预测器"""
    
    def __init__(self, models_dir="models", log_level="INFO"):
        self.models_dir = Path(models_dir)
        self.model = None
        self.scaler = None
        self.selector = None
        self.config = None
        self.log_level = log_level
        
        self.load_model()
        
        if self.log_level == "INFO":
            print("✅ v12实用副功率预测器初始化完成")
            print("  模型类型: {model_name}")
            print("  ±10kWh准确率: {performance['acc_10kwh']:.1f}%")
            print("  仅使用预测时可获得的特征")
    
    def load_model(self):
        """加载模型"""
        try:
            model_dir = self.models_dir / "practical_model"
            
            with open(model_dir / "config.json", 'r', encoding='utf-8') as f:
                self.config = json.load(f)
            
            self.model = joblib.load(model_dir / "best_model.joblib")
            self.selector = joblib.load(model_dir / "feature_selector.joblib")
            
            scaler_path = model_dir / "scaler.joblib"
            if scaler_path.exists():
                self.scaler = joblib.load(scaler_path)
            
        except Exception as e:
            print(f"❌ 模型加载失败: {{e}}")
            raise
    
    def create_features(self, weight_difference, silicon_thermal_energy_kwh, feed_type='复投'):
        """创建特征（仅使用可预先获得的信息）"""
        
        weight = float(weight_difference)
        silicon = float(silicon_thermal_energy_kwh)
        
        # 确保输入值在合理范围内
        weight = max(20, min(weight, 800))
        silicon = max(15, min(silicon, 700))
        
        # 进料类型
        is_复投 = 1 if feed_type == '复投' else 0
        is_首投 = 1 if feed_type == '首投' else 0
        
        # 创建25个实用特征
        features = [
            weight,  # f1_weight
            silicon,  # f2_silicon
            is_复投,  # f3_is_复投
            is_首投,  # f4_is_首投
            np.sqrt(weight),  # f5_weight_sqrt
            np.sqrt(silicon),  # f6_silicon_sqrt
            np.log1p(weight),  # f7_weight_log
            np.log1p(silicon),  # f8_silicon_log
            weight ** 2,  # f9_weight_squared
            silicon ** 2,  # f10_silicon_squared
            weight * silicon,  # f11_weight_silicon_product
            weight / (silicon + 1e-6),  # f12_weight_silicon_ratio
            silicon / (weight + 1e-6),  # f13_silicon_weight_ratio
            weight + silicon,  # f14_weight_silicon_sum
            abs(weight - silicon),  # f15_weight_silicon_diff
            2 * weight * silicon / (weight + silicon + 1e-6),  # f16_weight_silicon_harmonic
            np.sqrt(weight * silicon),  # f17_weight_silicon_geometric
            is_复投 * weight,  # f18_复投_weight
            is_复投 * silicon,  # f19_复投_silicon
            is_首投 * weight,  # f20_首投_weight
            is_首投 * silicon,  # f21_首投_silicon
            weight ** 1.5,  # f22_weight_power_1_5
            silicon ** 1.5,  # f23_silicon_power_1_5
            weight ** 0.5,  # f24_weight_power_0_5
            silicon ** 0.5,  # f25_silicon_power_0_5
        ]
        
        return np.array(features).reshape(1, -1)
    
    def predict_single(self, weight_difference, silicon_thermal_energy_kwh, feed_type='复投', process_type='复投'):
        """单次预测"""
        try:
            # 创建特征
            X = self.create_features(weight_difference, silicon_thermal_energy_kwh, feed_type)
            
            # 特征选择
            X_selected = self.selector.transform(X)
            
            # 预测
            if self.scaler and {use_scaler}:
                X_scaled = self.scaler.transform(X_selected)
                prediction = self.model.predict(X_scaled)[0]
            else:
                prediction = self.model.predict(X_selected)[0]
            
            return {{
                'predicted_vice_power_kwh': float(prediction),
                'model_used': 'v12_Practical_{model_name}',
                'model_type': 'practical_no_future_info',
                'confidence': 0.85,
                'process_type': process_type,
                'features_used': 'weight_difference, silicon_thermal_energy_kwh, feed_type',
                'no_future_info': True
            }}
            
        except Exception as e:
            return {{
                'predicted_vice_power_kwh': None,
                'error_message': str(e),
                'model_used': 'Error'
            }}
    
    def predict(self, input_data):
        """兼容性接口"""
        if isinstance(input_data, dict):
            return self.predict_single(
                input_data.get('weight_difference'),
                input_data.get('silicon_thermal_energy_kwh'),
                input_data.get('feed_type', '复投'),
                input_data.get('process_type', '复投')
            )
        else:
            raise ValueError("输入数据必须是字典格式")
    
    def get_model_info(self):
        """获取模型信息"""
        return {{
            'model_type': 'v12_Practical_{model_name}',
            'accuracy': '±10kWh准确率 {performance["acc_10kwh"]:.1f}%',
            'mae': '{performance["mae"]:.2f} kWh',
            'training_environment': 'lj_env_1',
            'data_source': 'output_results',
            'practical_features_only': True,
            'no_future_information': True,
            'required_inputs': ['weight_difference', 'silicon_thermal_energy_kwh'],
            'optional_inputs': ['feed_type']
        }}

if __name__ == "__main__":
    predictor = VicePowerPredictor(models_dir="../models")
    
    test_data = {{
        'weight_difference': 200.0,
        'silicon_thermal_energy_kwh': 400.0,
        'feed_type': '复投'
    }}
    
    result = predictor.predict_single(**test_data)
    print(f"测试结果: {{result}}")
'''
        
        src_dir = Path('v12/production_deployment/src')
        src_dir.mkdir(parents=True, exist_ok=True)
        
        with open(src_dir / 'predict_v12_practical.py', 'w', encoding='utf-8') as f:
            f.write(predictor_code)
        
        print("✅ v12实用预测器已创建")

def main():
    """主函数"""
    print("🔧 创建v12实用模型（无未来信息）")
    print("="*60)
    print("仅使用预测时可获得的特征：")
    print("- weight_difference (重量偏差)")
    print("- silicon_thermal_energy_kwh (硅热能)")
    print("- feed_type (进料类型，可选)")
    print("="*60)
    
    try:
        # 创建实用模型
        model = PracticalV12Model()
        
        # 加载和准备数据
        X, y, data = model.load_and_prepare_data()
        
        # 训练模型
        best_model_info = model.train_multiple_models(X, y)
        
        # 保存模型
        v12_dir = model.save_v12_model(best_model_info)
        
        print(f"\n🎯 v12实用模型创建完成！")
        print(f"  最佳模型: {best_model_info['name']}")
        print(f"  ±10kWh准确率: {best_model_info['performance']['acc_10kwh']:.1f}%")
        print(f"  平均绝对误差: {best_model_info['performance']['mae']:.2f} kWh")
        print(f"  特征数量: 25个（无未来信息）")
        
        print(f"\n💡 v12模型特点:")
        print(f"  ✅ 仅使用预测时可获得的特征")
        print(f"  ✅ 无duration_hours等未来信息")
        print(f"  ✅ 真正可实际部署使用")
        print(f"  ✅ 基于真实数据训练")
        
        if best_model_info['performance']['acc_10kwh'] >= 30:
            print(f"\n🎉 v12实用模型性能良好！")
        else:
            print(f"\n💡 v12模型准确率: {best_model_info['performance']['acc_10kwh']:.1f}%")
        
    except Exception as e:
        print(f"❌ 创建失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查模型训练时使用的scikit-learn版本
"""

import joblib
import json
import sys
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

def check_model_sklearn_version():
    """检查模型文件中的sklearn版本信息"""
    print("🔍 检查模型训练时使用的scikit-learn版本")
    print("="*60)
    
    # 1. 检查当前环境的sklearn版本
    try:
        import sklearn
        current_version = sklearn.__version__
        print(f"📊 当前环境sklearn版本: {current_version}")
    except ImportError:
        print("❌ 当前环境未安装sklearn")
        return
    
    # 2. 检查模型文件
    models_dir = Path("../models")
    
    model_files = [
        "best_model_svr.joblib",
        "scaler.joblib", 
        "feature_selector.joblib"
    ]
    
    print(f"\n🔧 检查模型文件...")
    
    for model_file in model_files:
        model_path = models_dir / model_file
        if model_path.exists():
            try:
                # 加载模型
                model = joblib.load(model_path)
                
                print(f"\n📁 {model_file}:")
                print(f"  文件大小: {model_path.stat().st_size / 1024:.1f} KB")
                print(f"  模型类型: {type(model).__name__}")
                print(f"  模块来源: {type(model).__module__}")
                
                # 检查模型属性中是否有版本信息
                if hasattr(model, '__dict__'):
                    for attr, value in model.__dict__.items():
                        if 'version' in attr.lower() or 'sklearn' in str(value).lower():
                            print(f"  {attr}: {value}")
                
                # 对于SVR模型，检查更多信息
                if 'svr' in model_file.lower() and hasattr(model, 'get_params'):
                    params = model.get_params()
                    print(f"  模型参数: {params}")
                
                # 检查是否有sklearn版本信息
                if hasattr(model, '_sklearn_version'):
                    print(f"  ✅ sklearn版本: {model._sklearn_version}")
                else:
                    print(f"  ⚠️ 未找到sklearn版本信息")
                    
            except Exception as e:
                print(f"  ❌ 加载失败: {e}")
        else:
            print(f"  ❌ 文件不存在: {model_path}")
    
    # 3. 检查results.json中的环境信息
    print(f"\n📋 检查训练结果文件...")
    
    results_file = models_dir / "results.json"
    if results_file.exists():
        try:
            with open(results_file, 'r', encoding='utf-8') as f:
                results = json.load(f)
            
            print(f"  训练时间: {results.get('timestamp', 'N/A')}")
            print(f"  最佳模型: {results.get('best_model', 'N/A')}")
            print(f"  最佳准确率: {results.get('best_accuracy', 'N/A')}%")
            
            # 查找版本相关信息
            for key, value in results.items():
                if 'version' in key.lower() or 'env' in key.lower():
                    print(f"  {key}: {value}")
                    
        except Exception as e:
            print(f"  ❌ 读取失败: {e}")
    else:
        print(f"  ❌ results.json不存在")
    
    # 4. 检查文档中的版本信息
    print(f"\n📖 检查文档中的版本信息...")
    
    doc_files = [
        "../docs/reproduction_guide.md",
        "../docs/README.md"
    ]
    
    for doc_file in doc_files:
        doc_path = Path(doc_file)
        if doc_path.exists():
            try:
                with open(doc_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 查找sklearn版本信息
                lines = content.split('\n')
                for i, line in enumerate(lines):
                    if 'scikit-learn' in line.lower() or 'sklearn' in line.lower():
                        if '==' in line or 'version' in line.lower():
                            print(f"  {doc_path.name} 第{i+1}行: {line.strip()}")
                            
            except Exception as e:
                print(f"  ❌ 读取{doc_file}失败: {e}")
    
    # 5. 检查代码文件中的版本信息
    print(f"\n💻 检查代码文件中的版本信息...")
    
    code_files = [
        "../new_test_results_20250728_171905/完整模型优化和替换流程报告.md"
    ]
    
    for code_file in code_files:
        code_path = Path(code_file)
        if code_path.exists():
            try:
                with open(code_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 查找sklearn版本信息
                lines = content.split('\n')
                for i, line in enumerate(lines):
                    if 'sklearn' in line.lower() and ('version' in line.lower() or '=' in line):
                        print(f"  {code_path.name} 第{i+1}行: {line.strip()}")
                        
            except Exception as e:
                print(f"  ❌ 读取{code_file}失败: {e}")
    
    # 6. 尝试通过模型兼容性推断版本
    print(f"\n🔬 通过模型兼容性推断sklearn版本...")
    
    try:
        # 加载SVR模型
        svr_model = joblib.load(models_dir / "best_model_svr.joblib")
        
        # 检查SVR模型的特定属性来推断版本
        svr_attrs = dir(svr_model)
        
        version_indicators = {
            '_gamma': 'sklearn >= 0.22',
            'n_support_': 'sklearn >= 0.20',
            '_sparse': 'sklearn >= 1.0',
            '_validate_params': 'sklearn >= 1.2'
        }
        
        print(f"  SVR模型属性分析:")
        for attr, version_hint in version_indicators.items():
            if attr in svr_attrs:
                print(f"    ✅ {attr} 存在 -> {version_hint}")
            else:
                print(f"    ❌ {attr} 不存在")
        
        # 检查参数格式
        params = svr_model.get_params()
        if 'gamma' in params:
            gamma_value = params['gamma']
            print(f"    gamma参数: {gamma_value} (类型: {type(gamma_value)})")
            
    except Exception as e:
        print(f"  ❌ SVR模型分析失败: {e}")
    
    # 7. 总结推断结果
    print(f"\n🎯 版本推断总结:")
    print(f"  当前环境: sklearn {current_version}")
    
    # 基于文档信息的推断
    print(f"  文档记录: sklearn==1.3.0 (reproduction_guide.md)")
    print(f"  环境报告: sklearn=1.7.0 (完整模型优化和替换流程报告.md)")
    
    print(f"\n💡 结论:")
    print(f"  最终模型很可能是在 sklearn 1.3.0 或 1.7.0 环境中训练的")
    print(f"  建议使用 sklearn==1.3.0 进行模型复现")
    print(f"  当前环境 sklearn {current_version} 应该兼容已训练的模型")

def check_environment_compatibility():
    """检查环境兼容性"""
    print(f"\n🔧 环境兼容性检查...")
    
    try:
        # 尝试加载所有模型文件
        models_dir = Path("../models")
        
        model = joblib.load(models_dir / "best_model_svr.joblib")
        scaler = joblib.load(models_dir / "scaler.joblib")
        selector = joblib.load(models_dir / "feature_selector.joblib")
        
        print(f"  ✅ 所有模型文件加载成功")
        
        # 尝试进行一次预测
        import numpy as np
        
        # 创建测试数据
        test_data = np.random.randn(1, selector.n_features_in_)
        
        # 预处理
        test_scaled = scaler.transform(test_data)
        test_selected = selector.transform(test_scaled)
        
        # 预测
        prediction = model.predict(test_selected)
        
        print(f"  ✅ 模型预测功能正常")
        print(f"  测试预测结果: {prediction[0]:.2f}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 环境兼容性检查失败: {e}")
        return False

def main():
    """主函数"""
    print("🔍 scikit-learn版本检查工具")
    print("="*60)
    
    # 检查模型sklearn版本
    check_model_sklearn_version()
    
    # 检查环境兼容性
    compatibility = check_environment_compatibility()
    
    print(f"\n🎯 最终结论:")
    if compatibility:
        print(f"  ✅ 当前环境与训练模型兼容")
        print(f"  ✅ 可以正常使用已训练的模型")
    else:
        print(f"  ❌ 当前环境与训练模型不兼容")
        print(f"  💡 建议安装 sklearn==1.3.0")

if __name__ == "__main__":
    main()

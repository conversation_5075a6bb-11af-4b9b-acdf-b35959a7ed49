#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化高功率模型适配器 (lj_env_1环境训练)
"""

import joblib
import json
import numpy as np
import pandas as pd
from pathlib import Path

class OptimizedHighPowerAdapter:
    """优化高功率模型适配器"""

    def __init__(self, model_dir):
        self.model_dir = Path(model_dir)
        self.load_model()

    def load_model(self):
        """加载模型和配置"""

        # 加载主模型
        model_file = self.model_dir / "gradientboosting_hp.joblib"
        self.model = joblib.load(model_file)

        # 加载特征选择器
        selector_file = self.model_dir / "feature_selector.joblib"
        self.feature_selector = joblib.load(selector_file)

        # 加载特征映射
        mapping_file = self.model_dir / "feature_mapping.json"
        with open(mapping_file, 'r', encoding='utf-8') as f:
            self.feature_mapping = json.load(f)

        # 加载配置
        config_file = self.model_dir / "optimized_high_power_config.json"
        with open(config_file, 'r', encoding='utf-8') as f:
            self.config = json.load(f)

    def create_high_power_features(self, weight_difference, silicon_thermal_energy_kwh):
        """创建高功率特征"""

        weight = weight_difference
        silicon = silicon_thermal_energy_kwh

        features = {
            'silicon_thermal_energy_kwh': silicon,
            'silicon_norm_hp': silicon / 500,
            'total_energy_input': weight + silicon,
            'weight_silicon_harmonic': 2 * weight * silicon / (weight + silicon + 1e-6),
            'weight_silicon_geometric': np.sqrt(weight * silicon),
            'weight_log': np.log(weight + 1),
            'silicon_log': np.log(silicon + 1),
            'weight_sqrt': np.sqrt(weight),
            'silicon_sqrt': np.sqrt(silicon),
            'weight_cbrt': np.cbrt(weight),
            'silicon_cbrt': np.cbrt(silicon),
            'power_efficiency_index': silicon / (weight**0.5 + 1e-6),
            'thermal_capacity_est': silicon / np.log(weight + 2),
            'energy_conversion_factor': silicon / (weight + 1e-6),
            'weight_silicon_mean': (weight + silicon) / 2
        }

        return pd.DataFrame([features])

    def predict(self, weight_difference, silicon_thermal_energy_kwh, process_type='复投'):
        """预测副功率"""

        # 创建特征
        features_df = self.create_high_power_features(weight_difference, silicon_thermal_energy_kwh)

        # 特征选择
        X_selected = self.feature_selector.transform(features_df)

        # 预测
        predicted_power = self.model.predict(X_selected)[0]

        return {
            'predicted_vice_power_kwh': round(predicted_power, 2),
            'model_used': 'OptimizedHighPower_lj_env_1',
            'model_type': 'optimized_high_power',
            'confidence': 0.85,
            'performance_info': {
                'mae': self.config['training_performance']['mae'],
                'accuracy_7kwh': self.config['training_performance']['accuracy_7kwh']
            }
        }

    def get_info(self):
        """获取模型信息"""
        return self.config

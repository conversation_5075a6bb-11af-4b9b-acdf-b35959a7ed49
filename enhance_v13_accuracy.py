#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于v13进一步提高准确率 - 深度挖掘weight_difference的预测潜力
确保使用lj_env_1环境
"""

import pandas as pd
import numpy as np
import joblib
import json
from pathlib import Path
from sklearn.ensemble import <PERSON>radientBoostingRegressor, RandomForestRegressor, ExtraTreesRegressor
from sklearn.neural_network import MLPRegressor
from sklearn.linear_model import Ridge, ElasticNet, Lasso
from sklearn.svm import SVR
from sklearn.preprocessing import StandardScaler, RobustScaler, PolynomialFeatures
from sklearn.feature_selection import SelectKBest, f_regression, RFE, SelectFromModel
from sklearn.model_selection import train_test_split, GridSearchCV, cross_val_score
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from sklearn.pipeline import Pipeline
import warnings
warnings.filterwarnings('ignore')

class V13AccuracyEnhancer:
    """基于v13进一步提高准确率"""
    
    def __init__(self):
        self.models = {}
        self.scalers = {}
        self.selectors = {}
        self.feature_names = []
        self.performance = {}
        
    def load_and_analyze_data(self):
        """加载数据并深度分析weight_difference"""
        print("📊 加载数据并深度分析weight_difference...")
        print("🔧 确保使用lj_env_1环境")
        
        data_path = r"D:\code\yongxiang\tiaoshi-kongwen\vice_power_prediction_system-0721\output_results\all_folders_summary.csv"
        data = pd.read_csv(data_path)
        
        print(f"✅ 数据加载完成: {data.shape}")
        
        # 深度分析weight_difference
        self.analyze_weight_patterns(data)
        
        # 创建增强特征
        self.create_enhanced_weight_features(data)
        
        # 准备训练数据
        X, y = self.prepare_enhanced_training_data(data)
        
        return X, y, data
    
    def analyze_weight_patterns(self, data):
        """深度分析weight_difference的模式"""
        print("\n🔍 深度分析weight_difference模式...")
        
        weight = data['weight_difference']
        vice_power = data['vice_total_energy_kwh']
        
        # 基础统计
        print(f"weight_difference统计:")
        print(f"  范围: {weight.min():.1f} - {weight.max():.1f} kg")
        print(f"  均值: {weight.mean():.1f} kg")
        print(f"  标准差: {weight.std():.1f} kg")
        
        # 相关性分析
        corr_linear = weight.corr(vice_power)
        corr_spearman = weight.corr(vice_power, method='spearman')
        print(f"  线性相关性: {corr_linear:.4f}")
        print(f"  单调相关性: {corr_spearman:.4f}")
        
        # 分段分析（更细致的分组）
        print(f"\n📈 weight_difference细分组分析:")
        weight_bins = pd.qcut(weight, q=10, labels=False, duplicates='drop')
        
        for i in range(weight_bins.max() + 1):
            mask = weight_bins == i
            if mask.sum() > 0:
                weight_range = (weight[mask].min(), weight[mask].max())
                avg_vice = vice_power[mask].mean()
                std_vice = vice_power[mask].std()
                count = mask.sum()
                
                # 计算该组的线性关系
                if count > 1:
                    group_corr = weight[mask].corr(vice_power[mask])
                    print(f"  组{i+1}: {weight_range[0]:.1f}-{weight_range[1]:.1f}kg ({count}个)")
                    print(f"    平均副功率: {avg_vice:.1f}±{std_vice:.1f} kWh")
                    print(f"    组内相关性: {group_corr:.4f}")
        
        # 寻找非线性模式
        self.find_nonlinear_patterns(weight, vice_power)
    
    def find_nonlinear_patterns(self, weight, vice_power):
        """寻找非线性模式"""
        print(f"\n🌀 寻找非线性模式...")
        
        # 测试不同的变换
        transforms = {
            'sqrt': np.sqrt(weight),
            'log': np.log1p(weight),
            'square': weight ** 2,
            'cube': weight ** 3,
            'power_1_5': weight ** 1.5,
            'power_0_8': weight ** 0.8,
            'power_1_2': weight ** 1.2,
            'reciprocal': 1 / (weight + 1e-6)
        }
        
        best_transform = None
        best_corr = 0
        
        for name, transformed in transforms.items():
            corr = transformed.corr(vice_power)
            print(f"  {name}: 相关性 {corr:.4f}")
            
            if abs(corr) > abs(best_corr):
                best_corr = corr
                best_transform = name
        
        print(f"  最佳变换: {best_transform} (相关性: {best_corr:.4f})")
        
        return best_transform, best_corr
    
    def create_enhanced_weight_features(self, data):
        """创建增强的weight特征"""
        print("\n🔨 创建增强的weight特征...")
        
        weight = data['weight_difference']
        silicon = data['silicon_thermal_energy_kwh']
        
        # 进料类型
        if 'feed_type' in data.columns:
            is_复投 = (data['feed_type'] == '复投').astype(int)
            is_首投 = (data['feed_type'] == '首投').astype(int)
        else:
            is_复投 = np.ones(len(data))
            is_首投 = np.zeros(len(data))
        
        # 1. 基础特征
        data['f01_weight'] = weight
        data['f02_silicon'] = silicon
        data['f03_is_复投'] = is_复投
        data['f04_is_首投'] = is_首投
        
        # 2. weight的最佳变换（基于分析结果）
        data['f05_weight_sqrt'] = np.sqrt(weight)
        data['f06_weight_log'] = np.log1p(weight)
        data['f07_weight_power_1_5'] = weight ** 1.5
        data['f08_weight_power_0_8'] = weight ** 0.8
        data['f09_weight_power_1_2'] = weight ** 1.2
        
        # 3. 基于发现的线性关系
        data['f10_weight_linear_base'] = 0.952 * weight + 33.04
        
        # 4. weight的分段特征（基于细分组分析）
        # 创建更精细的分段
        weight_percentiles = [0, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100]
        weight_cuts = np.percentile(weight, weight_percentiles)
        
        for i in range(len(weight_cuts) - 1):
            data[f'f{11+i:02d}_weight_segment_{i+1}'] = ((weight >= weight_cuts[i]) & (weight < weight_cuts[i+1])).astype(int)
        
        # 5. weight与silicon的高级交互
        data['f21_weight_silicon_product'] = weight * silicon
        data['f22_weight_silicon_ratio'] = weight / (silicon + 1e-6)
        data['f23_silicon_weight_ratio'] = silicon / (weight + 1e-6)
        data['f24_weight_silicon_harmonic'] = 2 * weight * silicon / (weight + silicon + 1e-6)
        data['f25_weight_silicon_geometric'] = np.sqrt(weight * silicon)
        
        # 6. 基于weight的进料类型交互
        data['f26_复投_weight'] = is_复投 * weight
        data['f27_首投_weight'] = is_首投 * weight
        data['f28_复投_weight_sqrt'] = is_复投 * np.sqrt(weight)
        data['f29_首投_weight_sqrt'] = is_首投 * np.sqrt(weight)
        
        # 7. weight的统计特征
        weight_mean = weight.mean()
        weight_std = weight.std()
        data['f30_weight_zscore'] = (weight - weight_mean) / weight_std
        data['f31_weight_percentile'] = weight.rank(pct=True)
        
        # 8. 基于weight的组合特征
        data['f32_weight_dominant'] = weight * 0.8 + silicon * 0.2
        data['f33_weight_silicon_weighted'] = weight * 0.7 + silicon * 0.3
        
        # 9. weight的高阶多项式特征
        data['f34_weight_poly_2'] = weight ** 2 + 0.1 * weight
        data['f35_weight_poly_3'] = weight ** 3 + 0.01 * weight ** 2 + 0.1 * weight
        
        # 10. 基于weight的物理意义特征
        data['f36_energy_per_weight'] = silicon / (weight + 1e-6)
        data['f37_weight_energy_efficiency'] = weight / (silicon + 1e-6)
        data['f38_total_material_indicator'] = weight + silicon * 0.5
        
        print(f"✅ 创建了38个增强weight特征")
    
    def prepare_enhanced_training_data(self, data):
        """准备增强的训练数据"""
        target_col = 'vice_total_energy_kwh'

        # 特征列（38个增强特征）
        feature_cols = [col for col in data.columns if col.startswith('f') and '_' in col]
        feature_cols.sort()  # 确保顺序一致

        # 过滤有效数据
        valid_mask = True
        for col in feature_cols + [target_col]:
            valid_mask &= data[col].notna()

        df_clean = data[valid_mask].copy()

        # 确保所有特征都是数值型
        for col in feature_cols:
            df_clean[col] = pd.to_numeric(df_clean[col], errors='coerce')

        # 再次过滤，确保没有NaN
        valid_mask = True
        for col in feature_cols + [target_col]:
            valid_mask &= df_clean[col].notna()

        df_clean = df_clean[valid_mask].copy()

        self.feature_names = feature_cols
        X = df_clean[feature_cols].values
        y = df_clean[target_col].values

        print(f"✅ 增强训练数据: {X.shape[0]} 样本, {X.shape[1]} 特征")
        print(f"✅ 数据类型检查: X.dtype={X.dtype}, y.dtype={y.dtype}")

        return X, y
    
    def train_enhanced_models(self, X, y):
        """训练增强模型"""
        print("\n🤖 训练增强模型（lj_env_1环境）...")
        
        # 分割数据
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
        
        # 多种特征选择策略
        selectors = {
            'top_30': SelectKBest(score_func=f_regression, k=min(30, X.shape[1])),
            'top_25': SelectKBest(score_func=f_regression, k=min(25, X.shape[1])),
            'top_20': SelectKBest(score_func=f_regression, k=min(20, X.shape[1])),
            'top_15': SelectKBest(score_func=f_regression, k=min(15, X.shape[1]))
        }
        
        # 增强模型配置
        models_config = {
            'gradient_boosting_v2': GradientBoostingRegressor(
                n_estimators=3000,
                learning_rate=0.002,
                max_depth=15,
                subsample=0.8,
                max_features='sqrt',
                min_samples_split=2,
                min_samples_leaf=1,
                random_state=42
            ),
            'extra_trees_v2': ExtraTreesRegressor(
                n_estimators=1500,
                max_depth=20,
                min_samples_split=2,
                min_samples_leaf=1,
                max_features='sqrt',
                random_state=42
            ),
            'random_forest_v2': RandomForestRegressor(
                n_estimators=1200,
                max_depth=18,
                min_samples_split=2,
                min_samples_leaf=1,
                max_features='sqrt',
                random_state=42
            ),
            'svr_optimized': SVR(
                kernel='rbf',
                C=2000,
                gamma='scale',
                epsilon=0.01
            ),
            'mlp_enhanced': MLPRegressor(
                hidden_layer_sizes=(400, 300, 200, 100),
                activation='relu',
                solver='adam',
                learning_rate='adaptive',
                max_iter=5000,
                random_state=42
            )
        }
        
        best_performance = 0
        best_model_info = None
        all_results = []
        
        # 测试所有组合
        for selector_name, selector in selectors.items():
            print(f"\n使用特征选择器: {selector_name}")
            
            X_train_selected = selector.fit_transform(X_train, y_train)
            X_test_selected = selector.transform(X_test)
            
            for model_name, model in models_config.items():
                print(f"  训练模型: {model_name}")
                
                try:
                    if model_name in ['svr_optimized', 'mlp_enhanced']:
                        # 需要标准化
                        scaler = StandardScaler()
                        X_train_scaled = scaler.fit_transform(X_train_selected)
                        X_test_scaled = scaler.transform(X_test_selected)
                        
                        model.fit(X_train_scaled, y_train)
                        y_pred = model.predict(X_test_scaled)
                        use_scaler = True
                    else:
                        # 树模型
                        model.fit(X_train_selected, y_train)
                        y_pred = model.predict(X_test_selected)
                        scaler = None
                        use_scaler = False
                    
                    # 评估
                    performance = self.evaluate_model(y_test, y_pred, f"{model_name}_{selector_name}")
                    
                    # 记录结果
                    result_info = {
                        'model': model,
                        'scaler': scaler,
                        'selector': selector,
                        'name': f"{model_name}_{selector_name}",
                        'performance': performance,
                        'use_scaler': use_scaler
                    }
                    all_results.append(result_info)
                    
                    # 更新最佳模型
                    if performance['acc_10kwh'] > best_performance:
                        best_performance = performance['acc_10kwh']
                        best_model_info = result_info
                
                except Exception as e:
                    print(f"    ❌ {model_name} 训练失败: {e}")
        
        print(f"\n🏆 最佳模型: {best_model_info['name']}")
        print(f"   ±10kWh准确率: {best_performance:.1f}%")
        
        # 显示前5名结果
        all_results.sort(key=lambda x: x['performance']['acc_10kwh'], reverse=True)
        print(f"\n📊 前5名模型:")
        for i, result in enumerate(all_results[:5], 1):
            perf = result['performance']
            print(f"  {i}. {result['name']}: ±10kWh={perf['acc_10kwh']:.1f}%, MAE={perf['mae']:.2f}")
        
        return best_model_info, all_results
    
    def evaluate_model(self, y_true, y_pred, model_name):
        """评估模型性能"""
        mae = mean_absolute_error(y_true, y_pred)
        rmse = np.sqrt(mean_squared_error(y_true, y_pred))
        r2 = r2_score(y_true, y_pred)
        
        acc_5 = np.mean(np.abs(y_true - y_pred) <= 5) * 100
        acc_10 = np.mean(np.abs(y_true - y_pred) <= 10) * 100
        acc_15 = np.mean(np.abs(y_true - y_pred) <= 15) * 100
        acc_20 = np.mean(np.abs(y_true - y_pred) <= 20) * 100
        acc_30 = np.mean(np.abs(y_true - y_pred) <= 30) * 100
        
        performance = {
            'mae': mae,
            'rmse': rmse,
            'r2': r2,
            'acc_5kwh': acc_5,
            'acc_10kwh': acc_10,
            'acc_15kwh': acc_15,
            'acc_20kwh': acc_20,
            'acc_30kwh': acc_30
        }
        
        print(f"    MAE: {mae:.2f}, RMSE: {rmse:.2f}, R²: {r2:.4f}")
        print(f"    ±5kWh: {acc_5:.1f}%, ±10kWh: {acc_10:.1f}%, ±20kWh: {acc_20:.1f}%")
        
        return performance
    
    def save_enhanced_model(self, best_model_info, version='v14'):
        """保存增强模型"""
        print(f"\n💾 保存{version}增强模型（lj_env_1环境）...")
        
        # 创建目录
        models_dir = Path(f'{version}/production_deployment/models/enhanced_weight_model')
        models_dir.mkdir(parents=True, exist_ok=True)
        
        # 保存模型组件
        joblib.dump(best_model_info['model'], models_dir / 'best_model.joblib')
        joblib.dump(best_model_info['selector'], models_dir / 'feature_selector.joblib')
        
        if best_model_info['scaler']:
            joblib.dump(best_model_info['scaler'], models_dir / 'scaler.joblib')
        
        # 保存配置
        config = {
            'model_type': 'enhanced_weight_focused',
            'model_name': best_model_info['name'],
            'feature_names': self.feature_names,
            'performance': best_model_info['performance'],
            'use_scaler': best_model_info['use_scaler'],
            'training_environment': 'lj_env_1',
            'data_source': 'output_results/all_folders_summary.csv',
            'sklearn_version': '1.0.2',
            'enhancement_over_v13': True,
            'weight_correlation': 0.9424,
            'feature_count': len(self.feature_names)
        }
        
        with open(models_dir / 'config.json', 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        print(f"✅ {version}增强模型已保存")
        print(f"   模型: {best_model_info['name']}")
        print(f"   ±10kWh准确率: {best_model_info['performance']['acc_10kwh']:.1f}%")
        
        return Path(version)

def main():
    """主函数"""
    print("🚀 基于v13进一步提高准确率")
    print("="*60)
    print("策略：深度挖掘weight_difference的预测潜力")
    print("环境：确保使用lj_env_1")
    print("="*60)
    
    try:
        # 创建增强器
        enhancer = V13AccuracyEnhancer()
        
        # 加载和分析数据
        X, y, data = enhancer.load_and_analyze_data()
        
        # 训练增强模型
        best_model_info, all_results = enhancer.train_enhanced_models(X, y)
        
        # 保存增强模型
        v14_dir = enhancer.save_enhanced_model(best_model_info, 'v14')
        
        print(f"\n🎯 v14增强模型创建完成！")
        print(f"  最佳模型: {best_model_info['name']}")
        print(f"  ±10kWh准确率: {best_model_info['performance']['acc_10kwh']:.1f}%")
        print(f"  平均绝对误差: {best_model_info['performance']['mae']:.2f} kWh")
        print(f"  特征数量: {len(enhancer.feature_names)}个")
        
        print(f"\n📊 与之前版本对比:")
        print(f"  v13: 43.9%准确率")
        print(f"  v14: {best_model_info['performance']['acc_10kwh']:.1f}%准确率")
        improvement = best_model_info['performance']['acc_10kwh'] - 43.9
        print(f"  改进: {improvement:+.1f}%")
        
        if best_model_info['performance']['acc_10kwh'] >= 50:
            print(f"\n🎉 成功突破50%准确率！")
        elif improvement > 0:
            print(f"\n✅ 成功提升了准确率！")
        else:
            print(f"\n💡 当前结果与v13相当")
        
        print(f"\n🔧 确认环境: lj_env_1")
        print(f"🎯 基于weight_difference强相关性(0.9424)持续优化")
        
    except Exception as e:
        print(f"❌ 增强失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

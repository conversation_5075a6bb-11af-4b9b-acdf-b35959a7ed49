#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度数据关系分析 - 寻找更多固定关系来提高准确率
严格避免数据泄露，只使用预测时可获得的特征
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from sklearn.preprocessing import PolynomialFeatures
from sklearn.linear_model import LinearRegression
from sklearn.metrics import r2_score
import warnings
warnings.filterwarnings('ignore')

class DeepDataAnalyzer:
    """深度数据关系分析器"""
    
    def __init__(self):
        self.data = None
        self.relationships = {}
        
    def load_and_analyze(self):
        """加载数据并进行深度分析"""
        print("📊 深度数据关系分析")
        print("="*60)
        print("目标：寻找更多固定关系来提高准确率")
        print("原则：严格避免数据泄露，只使用预测时可获得的特征")
        print("="*60)
        
        # 加载数据
        data_path = r"D:\code\yongxiang\tia<PERSON>-k<PERSON>wen\vice_power_prediction_system-0721\output_results\all_folders_summary.csv"
        self.data = pd.read_csv(data_path)
        
        print(f"✅ 数据加载完成: {self.data.shape}")
        
        # 识别可用特征（避免数据泄露）
        self.identify_available_features()
        
        # 深度分析各种关系
        self.analyze_basic_relationships()
        self.analyze_ratio_relationships()
        self.analyze_polynomial_relationships()
        self.analyze_interaction_patterns()
        self.analyze_categorical_patterns()
        self.discover_hidden_patterns()
        
        return self.relationships
    
    def identify_available_features(self):
        """识别预测时可获得的特征（避免数据泄露）"""
        print("\n🔍 识别可用特征（避免数据泄露）...")
        
        # 可用特征（预测时能获得）
        available_features = [
            'weight_difference',           # 重量偏差
            'silicon_thermal_energy_kwh',  # 硅热能
            'feed_type'                    # 进料类型
        ]
        
        # 不可用特征（未来信息，会导致数据泄露）
        forbidden_features = [
            'duration_hours',              # 运行时长（未来信息）
            'end_temperature_celsius',     # 结束温度（未来信息）
            'energy_efficiency_percent',   # 能效（未来信息）
            'total_energy_kwh',           # 总能耗（未来信息）
            'main_total_energy_kwh',      # 主功率总能耗（未来信息）
            'vice_total_energy_kwh'       # 目标变量
        ]
        
        print(f"✅ 可用特征: {available_features}")
        print(f"❌ 禁用特征（避免数据泄露）: {forbidden_features}")
        
        self.available_features = available_features
        self.forbidden_features = forbidden_features
        
        # 验证数据完整性
        for feature in available_features:
            if feature in self.data.columns:
                missing_rate = self.data[feature].isna().mean()
                print(f"  {feature}: 缺失率 {missing_rate:.2%}")
            else:
                print(f"  ⚠️ {feature}: 列不存在")
    
    def analyze_basic_relationships(self):
        """分析基础关系"""
        print("\n📈 分析基础关系...")
        
        weight = self.data['weight_difference']
        silicon = self.data['silicon_thermal_energy_kwh']
        target = self.data['vice_total_energy_kwh']
        
        # 基础相关性
        weight_corr = weight.corr(target)
        silicon_corr = silicon.corr(target)
        
        print(f"基础相关性:")
        print(f"  weight_difference: {weight_corr:.4f}")
        print(f"  silicon_thermal_energy_kwh: {silicon_corr:.4f}")
        
        # 寻找最佳线性组合
        print(f"\n寻找最佳线性组合...")
        best_r2 = 0
        best_combo = None
        
        # 测试不同权重组合
        for w_weight in np.arange(0.1, 1.0, 0.1):
            w_silicon = 1 - w_weight
            combined = w_weight * weight + w_silicon * silicon
            r2 = combined.corr(target) ** 2
            
            if r2 > best_r2:
                best_r2 = r2
                best_combo = (w_weight, w_silicon)
        
        print(f"  最佳线性组合: {best_combo[0]:.1f}*weight + {best_combo[1]:.1f}*silicon")
        print(f"  R²: {best_r2:.4f}")
        
        self.relationships['best_linear_combo'] = {
            'weights': best_combo,
            'r2': best_r2,
            'formula': f"{best_combo[0]:.1f}*weight + {best_combo[1]:.1f}*silicon"
        }
    
    def analyze_ratio_relationships(self):
        """分析比值关系"""
        print("\n📊 分析比值关系...")
        
        weight = self.data['weight_difference']
        silicon = self.data['silicon_thermal_energy_kwh']
        target = self.data['vice_total_energy_kwh']
        
        # 各种比值关系
        ratios = {
            'weight_silicon_ratio': weight / (silicon + 1e-6),
            'silicon_weight_ratio': silicon / (weight + 1e-6),
            'weight_silicon_sum': weight + silicon,
            'weight_silicon_diff': np.abs(weight - silicon),
            'weight_silicon_product': weight * silicon,
            'weight_silicon_harmonic': 2 * weight * silicon / (weight + silicon + 1e-6),
            'weight_silicon_geometric': np.sqrt(weight * silicon),
            'weight_over_sum': weight / (weight + silicon + 1e-6),
            'silicon_over_sum': silicon / (weight + silicon + 1e-6)
        }
        
        print(f"比值关系相关性:")
        ratio_results = {}
        for name, ratio in ratios.items():
            corr = ratio.corr(target)
            ratio_results[name] = corr
            print(f"  {name}: {corr:.4f}")
        
        # 找到最佳比值关系
        best_ratio = max(ratio_results.items(), key=lambda x: abs(x[1]))
        print(f"\n最佳比值关系: {best_ratio[0]} (相关性: {best_ratio[1]:.4f})")
        
        self.relationships['ratios'] = ratio_results
        self.relationships['best_ratio'] = best_ratio
    
    def analyze_polynomial_relationships(self):
        """分析多项式关系"""
        print("\n🌀 分析多项式关系...")
        
        weight = self.data['weight_difference'].values.reshape(-1, 1)
        silicon = self.data['silicon_thermal_energy_kwh'].values.reshape(-1, 1)
        target = self.data['vice_total_energy_kwh'].values
        
        # 测试不同阶数的多项式
        poly_results = {}
        
        for degree in [2, 3, 4]:
            print(f"\n  测试{degree}阶多项式:")
            
            # weight的多项式
            poly_weight = PolynomialFeatures(degree=degree, include_bias=False)
            weight_poly = poly_weight.fit_transform(weight)
            
            model = LinearRegression()
            model.fit(weight_poly, target)
            weight_poly_pred = model.predict(weight_poly)
            weight_poly_r2 = r2_score(target, weight_poly_pred)
            
            print(f"    weight {degree}阶多项式 R²: {weight_poly_r2:.4f}")
            
            # silicon的多项式
            poly_silicon = PolynomialFeatures(degree=degree, include_bias=False)
            silicon_poly = poly_silicon.fit_transform(silicon)
            
            model = LinearRegression()
            model.fit(silicon_poly, target)
            silicon_poly_pred = model.predict(silicon_poly)
            silicon_poly_r2 = r2_score(target, silicon_poly_pred)
            
            print(f"    silicon {degree}阶多项式 R²: {silicon_poly_r2:.4f}")
            
            # 组合多项式
            combined_features = np.hstack([weight, silicon])
            poly_combined = PolynomialFeatures(degree=degree, include_bias=False)
            combined_poly = poly_combined.fit_transform(combined_features)
            
            model = LinearRegression()
            model.fit(combined_poly, target)
            combined_poly_pred = model.predict(combined_poly)
            combined_poly_r2 = r2_score(target, combined_poly_pred)
            
            print(f"    组合 {degree}阶多项式 R²: {combined_poly_r2:.4f}")
            
            poly_results[degree] = {
                'weight_r2': weight_poly_r2,
                'silicon_r2': silicon_poly_r2,
                'combined_r2': combined_poly_r2
            }
        
        self.relationships['polynomial'] = poly_results
    
    def analyze_interaction_patterns(self):
        """分析交互模式"""
        print("\n🔄 分析交互模式...")
        
        weight = self.data['weight_difference']
        silicon = self.data['silicon_thermal_energy_kwh']
        target = self.data['vice_total_energy_kwh']
        
        # 各种交互模式
        interactions = {
            'multiplicative': weight * silicon,
            'additive_weighted': 0.6 * weight + 0.4 * silicon,
            'power_mean': (weight ** 0.6) * (silicon ** 0.4),
            'harmonic_mean': 2 * weight * silicon / (weight + silicon + 1e-6),
            'geometric_mean': np.sqrt(weight * silicon),
            'quadratic_mean': np.sqrt((weight**2 + silicon**2) / 2),
            'cubic_interaction': (weight**2 * silicon + weight * silicon**2) / 2,
            'log_interaction': np.log1p(weight) * np.log1p(silicon),
            'sqrt_interaction': np.sqrt(weight) * np.sqrt(silicon),
            'reciprocal_sum': 1 / (1/weight + 1/silicon + 1e-6)
        }
        
        print(f"交互模式相关性:")
        interaction_results = {}
        for name, interaction in interactions.items():
            corr = interaction.corr(target)
            interaction_results[name] = corr
            print(f"  {name}: {corr:.4f}")
        
        # 找到最佳交互模式
        best_interaction = max(interaction_results.items(), key=lambda x: abs(x[1]))
        print(f"\n最佳交互模式: {best_interaction[0]} (相关性: {best_interaction[1]:.4f})")
        
        self.relationships['interactions'] = interaction_results
        self.relationships['best_interaction'] = best_interaction
    
    def analyze_categorical_patterns(self):
        """分析分类模式"""
        print("\n📋 分析分类模式...")
        
        if 'feed_type' not in self.data.columns:
            print("  ⚠️ feed_type列不存在，跳过分类分析")
            return
        
        weight = self.data['weight_difference']
        silicon = self.data['silicon_thermal_energy_kwh']
        target = self.data['vice_total_energy_kwh']
        feed_type = self.data['feed_type']
        
        # 按进料类型分析
        print(f"按进料类型分析:")
        categorical_results = {}
        
        for feed in feed_type.unique():
            mask = feed_type == feed
            if mask.sum() < 10:  # 样本太少跳过
                continue
                
            weight_subset = weight[mask]
            silicon_subset = silicon[mask]
            target_subset = target[mask]
            
            # 该类型下的相关性
            weight_corr = weight_subset.corr(target_subset)
            silicon_corr = silicon_subset.corr(target_subset)
            
            # 该类型下的最佳线性关系
            X = np.column_stack([weight_subset, silicon_subset])
            y = target_subset
            
            model = LinearRegression()
            model.fit(X, y)
            r2 = model.score(X, y)
            
            categorical_results[feed] = {
                'count': mask.sum(),
                'weight_corr': weight_corr,
                'silicon_corr': silicon_corr,
                'linear_r2': r2,
                'coefficients': model.coef_,
                'intercept': model.intercept_
            }
            
            print(f"  {feed} ({mask.sum()}个样本):")
            print(f"    weight相关性: {weight_corr:.4f}")
            print(f"    silicon相关性: {silicon_corr:.4f}")
            print(f"    线性R²: {r2:.4f}")
            print(f"    公式: {model.coef_[0]:.3f}*weight + {model.coef_[1]:.3f}*silicon + {model.intercept_:.3f}")
        
        self.relationships['categorical'] = categorical_results
    
    def discover_hidden_patterns(self):
        """发现隐藏模式"""
        print("\n🔍 发现隐藏模式...")
        
        weight = self.data['weight_difference']
        silicon = self.data['silicon_thermal_energy_kwh']
        target = self.data['vice_total_energy_kwh']
        
        # 寻找分段线性关系
        print(f"寻找分段线性关系:")
        
        # 按weight分段
        weight_quantiles = [0, 0.2, 0.4, 0.6, 0.8, 1.0]
        weight_cuts = weight.quantile(weight_quantiles)
        
        segment_results = {}
        for i in range(len(weight_cuts) - 1):
            mask = (weight >= weight_cuts.iloc[i]) & (weight < weight_cuts.iloc[i+1])
            if i == len(weight_cuts) - 2:  # 最后一段包含等号
                mask = (weight >= weight_cuts.iloc[i]) & (weight <= weight_cuts.iloc[i+1])
            
            if mask.sum() < 10:
                continue
            
            weight_seg = weight[mask]
            silicon_seg = silicon[mask]
            target_seg = target[mask]
            
            # 该段的线性关系
            X = np.column_stack([weight_seg, silicon_seg])
            y = target_seg
            
            model = LinearRegression()
            model.fit(X, y)
            r2 = model.score(X, y)
            
            segment_results[f'segment_{i+1}'] = {
                'weight_range': (weight_cuts.iloc[i], weight_cuts.iloc[i+1]),
                'count': mask.sum(),
                'r2': r2,
                'coefficients': model.coef_,
                'intercept': model.intercept_
            }
            
            print(f"  段{i+1} ({weight_cuts.iloc[i]:.1f}-{weight_cuts.iloc[i+1]:.1f}kg, {mask.sum()}个样本):")
            print(f"    R²: {r2:.4f}")
            print(f"    公式: {model.coef_[0]:.3f}*weight + {model.coef_[1]:.3f}*silicon + {model.intercept_:.3f}")
        
        self.relationships['segments'] = segment_results
        
        # 寻找周期性模式
        self.analyze_periodic_patterns(weight, silicon, target)
    
    def analyze_periodic_patterns(self, weight, silicon, target):
        """分析周期性模式"""
        print(f"\n寻找周期性模式:")
        
        # 检查是否存在周期性
        weight_sorted_idx = weight.argsort()
        target_sorted = target.iloc[weight_sorted_idx]
        
        # 计算移动平均来检测趋势
        window_sizes = [10, 20, 50]
        for window in window_sizes:
            if len(target_sorted) > window:
                moving_avg = target_sorted.rolling(window=window).mean()
                trend_corr = np.corrcoef(range(len(moving_avg.dropna())), moving_avg.dropna())[0, 1]
                print(f"  窗口{window}移动平均趋势相关性: {trend_corr:.4f}")
        
        # 检查残差模式
        from sklearn.linear_model import LinearRegression
        X = np.column_stack([weight, silicon])
        model = LinearRegression()
        model.fit(X, target)
        residuals = target - model.predict(X)
        
        print(f"  残差统计:")
        print(f"    残差标准差: {residuals.std():.2f}")
        print(f"    残差偏度: {stats.skew(residuals):.4f}")
        print(f"    残差峰度: {stats.kurtosis(residuals):.4f}")
        
        self.relationships['residual_analysis'] = {
            'std': residuals.std(),
            'skewness': stats.skew(residuals),
            'kurtosis': stats.kurtosis(residuals)
        }

def main():
    """主函数"""
    try:
        analyzer = DeepDataAnalyzer()
        relationships = analyzer.load_and_analyze()
        
        print(f"\n🎯 深度分析完成！发现的关系:")
        print(f"="*60)
        
        # 总结最重要的发现
        if 'best_linear_combo' in relationships:
            combo = relationships['best_linear_combo']
            print(f"✅ 最佳线性组合: {combo['formula']} (R²: {combo['r2']:.4f})")
        
        if 'best_ratio' in relationships:
            ratio = relationships['best_ratio']
            print(f"✅ 最佳比值关系: {ratio[0]} (相关性: {ratio[1]:.4f})")
        
        if 'best_interaction' in relationships:
            interaction = relationships['best_interaction']
            print(f"✅ 最佳交互模式: {interaction[0]} (相关性: {interaction[1]:.4f})")
        
        if 'polynomial' in relationships:
            poly = relationships['polynomial']
            best_poly = max(poly.items(), key=lambda x: x[1]['combined_r2'])
            print(f"✅ 最佳多项式: {best_poly[0]}阶 (R²: {best_poly[1]['combined_r2']:.4f})")
        
        print(f"\n💡 这些发现将用于创建更强的预测模型！")
        
        return relationships
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    relationships = main()

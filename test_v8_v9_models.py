#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试v8和v9模型在完整测试数据上的准确性对比
"""

import pandas as pd
import numpy as np
import sys
import os
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# 添加v8和v9路径
sys.path.append('v8/production_deployment/src')
sys.path.append('v9/production_deployment/src')

def load_test_data():
    """加载测试数据"""
    df = pd.read_csv('完整测试数据_含输入特征.csv')
    print(f"✅ 加载测试数据: {df.shape[0]} 条记录")
    return df

def prepare_input_features(df):
    """准备输入特征"""
    # 基于数据中的特征创建模型输入
    input_features = []

    for idx, row in df.iterrows():
        # 根据实际数据计算合理的特征值
        weight_diff = row['weight_difference']
        silicon_energy = row['silicon_thermal_energy_kwh']

        # 根据重量差异估算时长（基于训练数据的经验关系）
        duration = max(24.0, min(120.0, weight_diff / 4.0))  # 24-120小时范围

        # 根据硅热能估算主功率（基于训练数据的经验关系）
        main_power = max(30.0, min(80.0, silicon_energy / 50.0))  # 30-80kW范围

        # 根据重量差异估算总能耗（基于训练数据的经验关系）
        total_energy = silicon_energy * 1.2  # 总能耗通常比硅热能高20%
        main_energy = total_energy * 0.85  # 主功率能耗占85%

        # 创建模型输入特征
        features = {
            'weight_difference': weight_diff,
            'silicon_thermal_energy_kwh': silicon_energy,
            'duration_hours': duration,
            'start_weight': max(800.0, 1500.0 - weight_diff),  # 合理的起始重量
            'end_weight': max(800.0, 1500.0 - weight_diff) + weight_diff,
            'end_temperature_celsius': row['temperature'],
            'first_crystal_seeding_main_power_kw': main_power,
            'feed_number_1_records': max(50, min(200, int(duration * 2))),  # 基于时长的记录数
            'main_total_energy_kwh': main_energy,
            'total_energy_kwh': total_energy,
            'energy_efficiency_percent': max(70.0, min(95.0, 85.0 + np.random.normal(0, 5))),
            'record_count': max(100, min(300, int(duration * 3))),  # 基于时长的记录数
            'device_frequency': 50.0,  # 标准频率
        }
        input_features.append(features)

    return input_features

def test_v8_model(input_features):
    """测试v8模型（85.4%准确率SVR）"""
    print("\n🔧 测试v8模型（85.4%准确率SVR）...")
    
    try:
        # 导入v8预测器
        from predict_svr_85_4 import VicePowerPredictor as V8Predictor
        
        # 初始化预测器
        v8_predictor = V8Predictor(models_dir="v8/production_deployment/models", log_level="ERROR")
        
        predictions = []
        for features in input_features:
            try:
                pred = v8_predictor.predict(features)
                predictions.append(pred)
            except Exception as e:
                print(f"⚠️ v8预测失败: {e}")
                predictions.append(np.nan)
        
        print(f"✅ v8模型预测完成: {len([p for p in predictions if not np.isnan(p)])} 个有效预测")
        return np.array(predictions)
        
    except Exception as e:
        print(f"❌ v8模型测试失败: {e}")
        return None

def test_v9_model(input_features):
    """测试v9模型（97.17%准确率神经网络）"""
    print("\n🔧 测试v9模型（97.17%准确率神经网络）...")
    
    try:
        # 导入v9预测器
        from predict_mlp_97_17 import VicePowerPredictor as V9Predictor
        
        # 初始化预测器
        v9_predictor = V9Predictor(models_dir="v9/production_deployment/models", log_level="ERROR")
        
        predictions = []
        for features in input_features:
            try:
                pred = v9_predictor.predict(features)
                predictions.append(pred)
            except Exception as e:
                print(f"⚠️ v9预测失败: {e}")
                predictions.append(np.nan)
        
        print(f"✅ v9模型预测完成: {len([p for p in predictions if not np.isnan(p)])} 个有效预测")
        return np.array(predictions)
        
    except Exception as e:
        print(f"❌ v9模型测试失败: {e}")
        return None

def calculate_metrics(actual, predicted, model_name):
    """计算评估指标"""
    # 移除NaN值
    mask = ~(np.isnan(actual) | np.isnan(predicted))
    actual_clean = actual[mask]
    predicted_clean = predicted[mask]
    
    if len(actual_clean) == 0:
        return None
    
    # 计算各种指标
    mae = np.mean(np.abs(actual_clean - predicted_clean))
    rmse = np.sqrt(np.mean((actual_clean - predicted_clean) ** 2))
    mape = np.mean(np.abs((actual_clean - predicted_clean) / actual_clean)) * 100
    
    # ±10kWh准确率
    within_10kwh = np.sum(np.abs(actual_clean - predicted_clean) <= 10) / len(actual_clean) * 100
    
    # ±20kWh准确率
    within_20kwh = np.sum(np.abs(actual_clean - predicted_clean) <= 20) / len(actual_clean) * 100
    
    # R²决定系数
    ss_res = np.sum((actual_clean - predicted_clean) ** 2)
    ss_tot = np.sum((actual_clean - np.mean(actual_clean)) ** 2)
    r2 = 1 - (ss_res / ss_tot) if ss_tot != 0 else 0
    
    metrics = {
        'model_name': model_name,
        'sample_count': len(actual_clean),
        'mae': mae,
        'rmse': rmse,
        'mape': mape,
        'within_10kwh': within_10kwh,
        'within_20kwh': within_20kwh,
        'r2': r2
    }
    
    return metrics

def print_metrics(metrics):
    """打印评估指标"""
    if metrics is None:
        print("❌ 无法计算指标")
        return
    
    print(f"\n📊 {metrics['model_name']} 性能指标:")
    print(f"  样本数量: {metrics['sample_count']}")
    print(f"  平均绝对误差 (MAE): {metrics['mae']:.2f} kWh")
    print(f"  均方根误差 (RMSE): {metrics['rmse']:.2f} kWh")
    print(f"  平均绝对百分比误差 (MAPE): {metrics['mape']:.2f}%")
    print(f"  ±10kWh准确率: {metrics['within_10kwh']:.2f}%")
    print(f"  ±20kWh准确率: {metrics['within_20kwh']:.2f}%")
    print(f"  决定系数 (R²): {metrics['r2']:.4f}")

def compare_models(v8_metrics, v9_metrics):
    """对比两个模型"""
    print("\n🔍 模型对比分析:")
    print("="*60)
    
    if v8_metrics is None or v9_metrics is None:
        print("❌ 无法进行模型对比")
        return
    
    # 创建对比表格
    comparison = pd.DataFrame({
        'v8 (SVR 85.4%)': [
            v8_metrics['mae'],
            v8_metrics['rmse'],
            v8_metrics['mape'],
            v8_metrics['within_10kwh'],
            v8_metrics['within_20kwh'],
            v8_metrics['r2']
        ],
        'v9 (MLP 97.17%)': [
            v9_metrics['mae'],
            v9_metrics['rmse'],
            v9_metrics['mape'],
            v9_metrics['within_10kwh'],
            v9_metrics['within_20kwh'],
            v9_metrics['r2']
        ]
    }, index=['MAE (kWh)', 'RMSE (kWh)', 'MAPE (%)', '±10kWh准确率 (%)', '±20kWh准确率 (%)', 'R²'])
    
    print(comparison.round(2))
    
    # 计算改进幅度
    print("\n📈 v9相对于v8的改进:")
    mae_improvement = (v8_metrics['mae'] - v9_metrics['mae']) / v8_metrics['mae'] * 100
    rmse_improvement = (v8_metrics['rmse'] - v9_metrics['rmse']) / v8_metrics['rmse'] * 100
    acc_10_improvement = v9_metrics['within_10kwh'] - v8_metrics['within_10kwh']
    acc_20_improvement = v9_metrics['within_20kwh'] - v8_metrics['within_20kwh']
    
    print(f"  MAE改进: {mae_improvement:+.1f}%")
    print(f"  RMSE改进: {rmse_improvement:+.1f}%")
    print(f"  ±10kWh准确率提升: {acc_10_improvement:+.1f}个百分点")
    print(f"  ±20kWh准确率提升: {acc_20_improvement:+.1f}个百分点")

def main():
    """主函数"""
    print("🚀 v8和v9模型准确性测试对比")
    print("="*60)
    
    # 1. 加载测试数据
    df = load_test_data()
    actual_values = df['actual_vice_power'].values
    
    # 2. 准备输入特征
    input_features = prepare_input_features(df)
    
    # 3. 测试v8模型
    v8_predictions = test_v8_model(input_features)
    
    # 4. 测试v9模型
    v9_predictions = test_v9_model(input_features)
    
    # 5. 计算评估指标
    v8_metrics = calculate_metrics(actual_values, v8_predictions, "v8 (SVR 85.4%)") if v8_predictions is not None else None
    v9_metrics = calculate_metrics(actual_values, v9_predictions, "v9 (MLP 97.17%)") if v9_predictions is not None else None
    
    # 6. 打印结果
    print_metrics(v8_metrics)
    print_metrics(v9_metrics)
    
    # 7. 对比分析
    compare_models(v8_metrics, v9_metrics)
    
    # 8. 保存详细结果
    if v8_predictions is not None and v9_predictions is not None:
        results_df = df.copy()
        results_df['v8_prediction'] = v8_predictions
        results_df['v9_prediction'] = v9_predictions
        results_df['v8_error'] = np.abs(actual_values - v8_predictions)
        results_df['v9_error'] = np.abs(actual_values - v9_predictions)
        
        results_df.to_csv('v8_v9_comparison_results.csv', index=False)
        print(f"\n💾 详细结果已保存到: v8_v9_comparison_results.csv")

if __name__ == "__main__":
    main()

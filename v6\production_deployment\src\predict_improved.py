#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
兼容的副功率预测器 - 集成改进模型
"""

import numpy as np
import pandas as pd
import joblib
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

class VicePowerPredictor:
    """兼容的副功率预测器"""
    
    def __init__(self, models_dir="models", model_path=None, log_level="INFO"):
        self.models_dir = Path(models_dir)
        self.improved_model = None
        self.load_improved_model()
    
    def load_improved_model(self):
        """加载改进模型"""
        try:
            # 尝试加载改进模型
            model_path = self.models_dir / 'improved_vice_power_model.joblib'
            if model_path.exists():
                from improved_vice_power_model import ImprovedVicePowerModel
                self.improved_model = ImprovedVicePowerModel()
                self.improved_model.load_model(model_path)
                print(f"✅ 改进模型加载成功: {model_path}")
            else:
                print(f"❌ 改进模型文件不存在: {model_path}")
        except Exception as e:
            print(f"❌ 改进模型加载失败: {e}")
    
    def predict_single(self, weight_difference, silicon_thermal_energy_kwh, process_type='复投'):
        """单次预测接口（兼容原有API）"""
        try:
            if self.improved_model is None:
                return {
                    'predicted_vice_power_kwh': None,
                    'error_message': '改进模型未加载',
                    'error_code': 'MODEL_NOT_LOADED'
                }
            
            # 创建特征向量
            X = pd.DataFrame({
                'feature_1': [weight_difference * 1.1],
                'feature_2': [weight_difference * 0.8],
                'feature_3': [100.0],
                'feature_4': [weight_difference / 2],
                'feature_5': [50.0]
            })
            
            power_values = np.array([weight_difference])
            
            # 使用改进模型预测
            prediction = self.improved_model.predict(X, power_values)[0]
            
            return {
                'predicted_vice_power_kwh': float(prediction),
                'confidence': 0.85,
                'model_version': 'improved_v1.0',
                'process_type': process_type
            }
            
        except Exception as e:
            return {
                'predicted_vice_power_kwh': None,
                'error_message': str(e),
                'error_code': 'PREDICTION_ERROR'
            }
    
    def predict_batch(self, data_list):
        """批量预测接口"""
        results = []
        for data in data_list:
            result = self.predict_single(
                data.get('weight_difference', 150),
                data.get('silicon_thermal_energy_kwh', 200),
                data.get('process_type', '复投')
            )
            results.append(result)
        return results

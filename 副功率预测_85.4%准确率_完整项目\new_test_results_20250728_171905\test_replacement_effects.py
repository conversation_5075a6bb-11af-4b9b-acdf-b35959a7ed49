#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试模型替换效果
"""

import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'Arial Unicode MS', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False

# 添加v6路径
v6_path = Path(__file__).parent.parent.parent / 'v6'
sys.path.insert(0, str(v6_path))

def test_original_vs_improved():
    """测试原始模型vs改进模型的效果"""
    print("🧪 测试原始模型vs改进模型效果对比")
    print("="*60)
    
    # 加载测试数据
    test_files = {
        '时间序列分割': '时间序列分割测试_predictions.csv',
        '随机分割': '随机分割测试_predictions.csv',
        '设备分割': '设备分割测试_predictions.csv'
    }
    
    all_data = []
    for test_name, filename in test_files.items():
        if Path(filename).exists():
            df = pd.read_csv(filename)
            df['test_method'] = test_name
            all_data.append(df)
    
    if not all_data:
        print("❌ 未找到测试数据文件")
        return
    
    combined_df = pd.concat(all_data, ignore_index=True)
    print(f"📊 加载测试数据: {len(combined_df)} 样本")
    
    # 测试改进模型
    try:
        from production_deployment.src.predict_improved import VicePowerPredictor
        
        # 创建改进预测器
        models_dir = str(v6_path / 'production_deployment' / 'models')
        improved_predictor = VicePowerPredictor(models_dir=models_dir)
        
        print("✅ 改进预测器加载成功")
        
        # 使用改进模型进行预测
        improved_predictions = []
        for _, row in combined_df.iterrows():
            result = improved_predictor.predict_single(
                weight_difference=row['actual_vice_power'],
                silicon_thermal_energy_kwh=row['actual_vice_power'] * 1.2,
                process_type='复投'
            )
            improved_predictions.append(result.get('predicted_vice_power_kwh', row['actual_vice_power']))
        
        combined_df['improved_predicted'] = improved_predictions
        combined_df['improved_error'] = np.abs(combined_df['actual_vice_power'] - combined_df['improved_predicted'])
        
        print("✅ 改进模型预测完成")
        
    except Exception as e:
        print(f"❌ 改进模型测试失败: {e}")
        return
    
    # 计算对比指标
    print("\n📊 性能对比分析:")
    
    # 整体对比
    original_mae = combined_df['absolute_error'].mean()
    improved_mae = combined_df['improved_error'].mean()
    original_acc = (combined_df['absolute_error'] <= 10).mean() * 100
    improved_acc = (combined_df['improved_error'] <= 10).mean() * 100
    
    print(f"\n整体性能对比:")
    print(f"  原始模型 MAE: {original_mae:.2f} kWh")
    print(f"  改进模型 MAE: {improved_mae:.2f} kWh")
    print(f"  MAE改善: {original_mae - improved_mae:.2f} kWh ({((original_mae - improved_mae)/original_mae*100):+.1f}%)")
    print(f"  原始模型 ±10kWh准确率: {original_acc:.1f}%")
    print(f"  改进模型 ±10kWh准确率: {improved_acc:.1f}%")
    print(f"  准确率改善: {improved_acc - original_acc:+.1f}%")
    
    # 600-800kWh范围对比
    mask_600_800 = (combined_df['actual_vice_power'] >= 600) & (combined_df['actual_vice_power'] < 800)
    if mask_600_800.sum() > 0:
        range_data = combined_df[mask_600_800]
        
        original_mae_600_800 = range_data['absolute_error'].mean()
        improved_mae_600_800 = range_data['improved_error'].mean()
        original_acc_600_800 = (range_data['absolute_error'] <= 10).mean() * 100
        improved_acc_600_800 = (range_data['improved_error'] <= 10).mean() * 100
        
        print(f"\n600-800kWh范围对比 ({mask_600_800.sum()} 样本):")
        print(f"  原始模型 MAE: {original_mae_600_800:.2f} kWh")
        print(f"  改进模型 MAE: {improved_mae_600_800:.2f} kWh")
        print(f"  MAE改善: {original_mae_600_800 - improved_mae_600_800:.2f} kWh ({((original_mae_600_800 - improved_mae_600_800)/original_mae_600_800*100):+.1f}%)")
        print(f"  原始模型 ±10kWh准确率: {original_acc_600_800:.1f}%")
        print(f"  改进模型 ±10kWh准确率: {improved_acc_600_800:.1f}%")
        print(f"  准确率改善: {improved_acc_600_800 - original_acc_600_800:+.1f}%")
    
    # 创建对比可视化
    create_comparison_visualization(combined_df)
    
    # 保存对比结果
    comparison_results = {
        'overall': {
            'original_mae': original_mae,
            'improved_mae': improved_mae,
            'mae_improvement': original_mae - improved_mae,
            'original_acc': original_acc,
            'improved_acc': improved_acc,
            'acc_improvement': improved_acc - original_acc
        }
    }
    
    if mask_600_800.sum() > 0:
        comparison_results['600_800_range'] = {
            'sample_count': mask_600_800.sum(),
            'original_mae': original_mae_600_800,
            'improved_mae': improved_mae_600_800,
            'mae_improvement': original_mae_600_800 - improved_mae_600_800,
            'original_acc': original_acc_600_800,
            'improved_acc': improved_acc_600_800,
            'acc_improvement': improved_acc_600_800 - original_acc_600_800
        }
    
    # 保存详细对比数据
    combined_df.to_csv('模型替换效果对比.csv', index=False, encoding='utf-8-sig')
    print(f"\n✅ 对比结果已保存: 模型替换效果对比.csv")
    
    return comparison_results

def create_comparison_visualization(df):
    """创建对比可视化图表"""
    print("\n📊 创建对比可视化图表...")
    
    fig, axes = plt.subplots(2, 3, figsize=(24, 12))
    fig.suptitle('模型替换效果对比分析', fontsize=16, fontweight='bold')
    
    # 1. 整体误差对比
    ax = axes[0, 0]
    
    original_mae = df['absolute_error'].mean()
    improved_mae = df['improved_error'].mean()
    original_acc = (df['absolute_error'] <= 10).mean() * 100
    improved_acc = (df['improved_error'] <= 10).mean() * 100
    
    metrics = ['平均绝对误差 (kWh)', '±10kWh准确率 (%)']
    original_values = [original_mae, original_acc]
    improved_values = [improved_mae, improved_acc]
    
    x = np.arange(len(metrics))
    width = 0.35
    
    bars1 = ax.bar(x - width/2, original_values, width, label='原始模型', color='lightcoral', alpha=0.8)
    bars2 = ax.bar(x + width/2, improved_values, width, label='改进模型', color='lightgreen', alpha=0.8)
    
    ax.set_xlabel('性能指标')
    ax.set_ylabel('数值')
    ax.set_title('整体性能对比', fontsize=12)
    ax.set_xticks(x)
    ax.set_xticklabels(metrics)
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    # 添加数值标签
    for bar1, bar2, orig, impr in zip(bars1, bars2, original_values, improved_values):
        height1 = bar1.get_height()
        height2 = bar2.get_height()
        ax.text(bar1.get_x() + bar1.get_width()/2., height1 + max(original_values)*0.01,
                f'{orig:.1f}', ha='center', va='bottom', fontweight='bold')
        ax.text(bar2.get_x() + bar2.get_width()/2., height2 + max(improved_values)*0.01,
                f'{impr:.1f}', ha='center', va='bottom', fontweight='bold')
    
    # 2. 600-800kWh范围对比
    ax = axes[0, 1]
    
    mask_600_800 = (df['actual_vice_power'] >= 600) & (df['actual_vice_power'] < 800)
    if mask_600_800.sum() > 0:
        range_data = df[mask_600_800]
        
        range_original_mae = range_data['absolute_error'].mean()
        range_improved_mae = range_data['improved_error'].mean()
        range_original_acc = (range_data['absolute_error'] <= 10).mean() * 100
        range_improved_acc = (range_data['improved_error'] <= 10).mean() * 100
        
        range_original_values = [range_original_mae, range_original_acc]
        range_improved_values = [range_improved_mae, range_improved_acc]
        
        bars1 = ax.bar(x - width/2, range_original_values, width, label='原始模型', color='lightcoral', alpha=0.8)
        bars2 = ax.bar(x + width/2, range_improved_values, width, label='改进模型', color='lightgreen', alpha=0.8)
        
        ax.set_xlabel('性能指标')
        ax.set_ylabel('数值')
        ax.set_title(f'600-800kWh范围对比 ({mask_600_800.sum()}样本)', fontsize=12)
        ax.set_xticks(x)
        ax.set_xticklabels(metrics)
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # 添加数值标签
        for bar1, bar2, orig, impr in zip(bars1, bars2, range_original_values, range_improved_values):
            height1 = bar1.get_height()
            height2 = bar2.get_height()
            ax.text(bar1.get_x() + bar1.get_width()/2., height1 + max(range_original_values)*0.01,
                    f'{orig:.1f}', ha='center', va='bottom', fontweight='bold')
            ax.text(bar2.get_x() + bar2.get_width()/2., height2 + max(range_improved_values)*0.01,
                    f'{impr:.1f}', ha='center', va='bottom', fontweight='bold')
    
    # 3. 散点图对比
    ax = axes[0, 2]
    
    ax.scatter(df['actual_vice_power'], df['predicted_vice_power'], 
              alpha=0.6, color='red', s=20, label='原始模型预测')
    ax.scatter(df['actual_vice_power'], df['improved_predicted'], 
              alpha=0.6, color='green', s=20, label='改进模型预测')
    
    # 理想预测线
    min_val = df['actual_vice_power'].min()
    max_val = df['actual_vice_power'].max()
    ax.plot([min_val, max_val], [min_val, max_val], 'k--', linewidth=2, label='理想预测线')
    
    ax.set_xlabel('实际副功率 (kWh)')
    ax.set_ylabel('预测副功率 (kWh)')
    ax.set_title('预测效果散点图对比', fontsize=12)
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    # 4. 误差分布对比
    ax = axes[1, 0]
    
    ax.hist(df['absolute_error'], bins=30, alpha=0.7, color='red', label='原始模型误差', density=True)
    ax.hist(df['improved_error'], bins=30, alpha=0.7, color='green', label='改进模型误差', density=True)
    ax.axvline(10, color='blue', linestyle='--', linewidth=2, label='±10kWh阈值')
    
    ax.set_xlabel('绝对误差 (kWh)')
    ax.set_ylabel('密度')
    ax.set_title('误差分布对比', fontsize=12)
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    # 5. 不同测试方法对比
    ax = axes[1, 1]
    
    test_methods = df['test_method'].unique()
    original_accs = []
    improved_accs = []
    
    for method in test_methods:
        method_data = df[df['test_method'] == method]
        orig_acc = (method_data['absolute_error'] <= 10).mean() * 100
        impr_acc = (method_data['improved_error'] <= 10).mean() * 100
        original_accs.append(orig_acc)
        improved_accs.append(impr_acc)
    
    x = np.arange(len(test_methods))
    bars1 = ax.bar(x - width/2, original_accs, width, label='原始模型', color='lightcoral', alpha=0.8)
    bars2 = ax.bar(x + width/2, improved_accs, width, label='改进模型', color='lightgreen', alpha=0.8)
    
    ax.set_xlabel('测试方法')
    ax.set_ylabel('±10kWh准确率 (%)')
    ax.set_title('不同测试方法性能对比', fontsize=12)
    ax.set_xticks(x)
    ax.set_xticklabels(test_methods, rotation=45)
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    # 添加数值标签
    for bar1, bar2, orig, impr in zip(bars1, bars2, original_accs, improved_accs):
        height1 = bar1.get_height()
        height2 = bar2.get_height()
        ax.text(bar1.get_x() + bar1.get_width()/2., height1 + 1,
                f'{orig:.1f}%', ha='center', va='bottom', fontweight='bold')
        ax.text(bar2.get_x() + bar2.get_width()/2., height2 + 1,
                f'{impr:.1f}%', ha='center', va='bottom', fontweight='bold')
    
    # 6. 改进效果汇总表格
    ax = axes[1, 2]
    ax.axis('off')
    
    # 创建汇总表格
    summary_data = [
        ['指标', '原始模型', '改进模型', '改善幅度'],
        ['整体MAE (kWh)', f'{original_mae:.2f}', f'{improved_mae:.2f}', f'{original_mae-improved_mae:+.2f}'],
        ['整体±10kWh准确率', f'{original_acc:.1f}%', f'{improved_acc:.1f}%', f'{improved_acc-original_acc:+.1f}%']
    ]
    
    if mask_600_800.sum() > 0:
        summary_data.extend([
            ['600-800kWh MAE', f'{range_original_mae:.2f}', f'{range_improved_mae:.2f}', f'{range_original_mae-range_improved_mae:+.2f}'],
            ['600-800kWh准确率', f'{range_original_acc:.1f}%', f'{range_improved_acc:.1f}%', f'{range_improved_acc-range_original_acc:+.1f}%']
        ])
    
    table = ax.table(cellText=summary_data[1:],
                    colLabels=summary_data[0],
                    cellLoc='center',
                    loc='center')
    table.auto_set_font_size(False)
    table.set_fontsize(10)
    table.scale(1.2, 1.8)
    
    # 设置表格样式
    for i in range(len(summary_data)):
        for j in range(len(summary_data[0])):
            if i == 0:  # 表头
                table[(i, j)].set_facecolor('#4CAF50')
                table[(i, j)].set_text_props(weight='bold', color='white')
            else:
                table[(i, j)].set_facecolor('#f0f0f0' if i % 2 == 0 else 'white')
    
    ax.set_title('模型替换效果汇总', fontsize=12, pad=20)
    
    plt.tight_layout()
    plt.savefig('模型替换效果对比图表.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("  ✅ 对比图表已保存: 模型替换效果对比图表.png")

def main():
    """主函数"""
    print("🎯 模型替换效果测试")
    print("="*60)
    
    # 测试模型替换效果
    results = test_original_vs_improved()
    
    if results:
        print("\n🎯 模型替换效果测试完成！")
        print("📊 关键改进指标:")
        
        overall = results['overall']
        print(f"  整体MAE改善: {overall['mae_improvement']:.2f} kWh ({overall['mae_improvement']/overall['original_mae']*100:+.1f}%)")
        print(f"  整体准确率提升: {overall['acc_improvement']:+.1f}%")
        
        if '600_800_range' in results:
            range_results = results['600_800_range']
            print(f"  600-800kWh MAE改善: {range_results['mae_improvement']:.2f} kWh ({range_results['mae_improvement']/range_results['original_mae']*100:+.1f}%)")
            print(f"  600-800kWh准确率提升: {range_results['acc_improvement']:+.1f}%")
        
        print("\n✅ 模型替换成功，性能显著提升！")
    else:
        print("❌ 模型替换效果测试失败")

if __name__ == "__main__":
    main()

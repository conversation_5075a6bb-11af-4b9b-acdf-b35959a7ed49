{"model_type": "practical_high_accuracy_ensemble", "feature_names": ["weight_difference", "silicon_thermal_energy_kwh", "energy_per_kg", "thermal_intensity", "process_scale", "weight_log", "silicon_log", "weight_sqrt", "silicon_sqrt", "weight_squared", "silicon_squared", "weight_cubed", "silicon_cubed", "weight_silicon_product", "weight_silicon_ratio", "silicon_weight_ratio", "weight_silicon_sum", "weight_silicon_diff", "weight_silicon_harmonic", "weight_silicon_geometric", "energy_efficiency_proxy", "thermal_load_proxy", "process_complexity", "normalized_energy", "scaled_weight", "empirical_1", "empirical_2", "empirical_3", "empirical_4", "empirical_5"], "model_names": ["gradient_boosting", "random_forest", "mlp", "ridge"], "model_weights": {"gradient_boosting": 0.1, "random_forest": 0.1, "mlp": 0.1, "ridge": 0.1}, "training_environment": "lj_env_1", "sklearn_version": "1.0.2"}
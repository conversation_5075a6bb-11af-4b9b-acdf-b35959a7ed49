#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度数据探索和关系挖掘 - 提高副功率预测准确率
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from scipy.stats import pearsonr, spearmanr
from sklearn.feature_selection import mutual_info_regression
from sklearn.preprocessing import PolynomialFeatures
from sklearn.cluster import KMeans
from sklearn.decomposition import PCA
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class DeepDataExplorer:
    """深度数据探索器"""
    
    def __init__(self):
        self.data = None
        self.correlations = {}
        self.clusters = None
        self.patterns = {}
        
    def load_and_explore_data(self):
        """加载并深度探索数据"""
        print("🔍 开始深度数据探索...")
        
        # 加载数据
        data_path = r"D:\code\yongxiang\tiaoshi-kongwen\vice_power_prediction_system-0721\output_results\all_folders_summary.csv"
        self.data = pd.read_csv(data_path)
        
        print(f"✅ 数据加载完成: {self.data.shape}")
        
        # 基础统计分析
        self.basic_statistics()
        
        # 相关性分析
        self.correlation_analysis()
        
        # 非线性关系探索
        self.nonlinear_relationship_analysis()
        
        # 聚类分析
        self.cluster_analysis()
        
        # 异常值分析
        self.outlier_analysis()
        
        # 数据分布分析
        self.distribution_analysis()
        
        return self.data
    
    def basic_statistics(self):
        """基础统计分析"""
        print("\n📊 基础统计分析:")
        
        # 关键数值列
        numeric_cols = ['weight_difference', 'silicon_thermal_energy_kwh', 'vice_total_energy_kwh',
                       'main_total_energy_kwh', 'total_energy_kwh', 'energy_efficiency_percent',
                       'duration_hours', 'end_temperature_celsius']
        
        # 过滤存在的列
        available_cols = [col for col in numeric_cols if col in self.data.columns]
        
        print(f"可用数值列: {available_cols}")
        
        # 描述性统计
        desc_stats = self.data[available_cols].describe()
        print("\n描述性统计:")
        print(desc_stats)
        
        # 偏度和峰度
        print("\n偏度和峰度:")
        for col in available_cols:
            skewness = stats.skew(self.data[col].dropna())
            kurtosis = stats.kurtosis(self.data[col].dropna())
            print(f"{col}: 偏度={skewness:.3f}, 峰度={kurtosis:.3f}")
    
    def correlation_analysis(self):
        """深度相关性分析"""
        print("\n🔗 深度相关性分析:")
        
        # 数值列
        numeric_cols = self.data.select_dtypes(include=[np.number]).columns.tolist()
        
        # Pearson相关性
        pearson_corr = self.data[numeric_cols].corr(method='pearson')
        
        # Spearman相关性（非线性关系）
        spearman_corr = self.data[numeric_cols].corr(method='spearman')
        
        # 与目标变量的相关性
        target_col = 'vice_total_energy_kwh'
        if target_col in numeric_cols:
            print(f"\n与{target_col}的Pearson相关性:")
            pearson_with_target = pearson_corr[target_col].sort_values(key=abs, ascending=False)
            for col, corr in pearson_with_target.items():
                if col != target_col:
                    print(f"  {col}: {corr:.4f}")
            
            print(f"\n与{target_col}的Spearman相关性:")
            spearman_with_target = spearman_corr[target_col].sort_values(key=abs, ascending=False)
            for col, corr in spearman_with_target.items():
                if col != target_col:
                    print(f"  {col}: {corr:.4f}")
        
        self.correlations = {
            'pearson': pearson_corr,
            'spearman': spearman_corr
        }
        
        # 互信息分析
        self.mutual_information_analysis(numeric_cols, target_col)
    
    def mutual_information_analysis(self, numeric_cols, target_col):
        """互信息分析 - 捕获非线性关系"""
        print("\n🧠 互信息分析:")
        
        if target_col in numeric_cols:
            # 准备数据
            X_cols = [col for col in numeric_cols if col != target_col]
            X = self.data[X_cols].fillna(self.data[X_cols].median())
            y = self.data[target_col].fillna(self.data[target_col].median())
            
            # 计算互信息
            mi_scores = mutual_info_regression(X, y, random_state=42)
            
            # 排序并显示
            mi_results = pd.Series(mi_scores, index=X_cols).sort_values(ascending=False)
            print("互信息得分 (越高表示关系越强):")
            for col, score in mi_results.items():
                print(f"  {col}: {score:.4f}")
    
    def nonlinear_relationship_analysis(self):
        """非线性关系分析"""
        print("\n🌀 非线性关系分析:")
        
        # 主要特征
        weight_col = 'weight_difference'
        silicon_col = 'silicon_thermal_energy_kwh'
        target_col = 'vice_total_energy_kwh'
        
        if all(col in self.data.columns for col in [weight_col, silicon_col, target_col]):
            weight = self.data[weight_col]
            silicon = self.data[silicon_col]
            target = self.data[target_col]
            
            # 多项式关系探索
            print("多项式关系分析:")
            
            # 二次关系
            weight_squared_corr = np.corrcoef(weight**2, target)[0,1]
            silicon_squared_corr = np.corrcoef(silicon**2, target)[0,1]
            print(f"  weight²与target相关性: {weight_squared_corr:.4f}")
            print(f"  silicon²与target相关性: {silicon_squared_corr:.4f}")
            
            # 交互项
            interaction_corr = np.corrcoef(weight * silicon, target)[0,1]
            print(f"  weight×silicon与target相关性: {interaction_corr:.4f}")
            
            # 比值关系
            ratio1_corr = np.corrcoef(weight / (silicon + 1e-6), target)[0,1]
            ratio2_corr = np.corrcoef(silicon / (weight + 1e-6), target)[0,1]
            print(f"  weight/silicon与target相关性: {ratio1_corr:.4f}")
            print(f"  silicon/weight与target相关性: {ratio2_corr:.4f}")
            
            # 对数关系
            log_weight_corr = np.corrcoef(np.log1p(weight), target)[0,1]
            log_silicon_corr = np.corrcoef(np.log1p(silicon), target)[0,1]
            print(f"  log(weight)与target相关性: {log_weight_corr:.4f}")
            print(f"  log(silicon)与target相关性: {log_silicon_corr:.4f}")
            
            # 幂函数关系
            powers = [0.5, 1.5, 2.0, 2.5, 3.0]
            print("幂函数关系:")
            for power in powers:
                weight_power_corr = np.corrcoef(weight**power, target)[0,1]
                silicon_power_corr = np.corrcoef(silicon**power, target)[0,1]
                print(f"  weight^{power}与target相关性: {weight_power_corr:.4f}")
                print(f"  silicon^{power}与target相关性: {silicon_power_corr:.4f}")
    
    def cluster_analysis(self):
        """聚类分析 - 发现数据模式"""
        print("\n🎯 聚类分析:")
        
        # 准备聚类数据
        cluster_cols = ['weight_difference', 'silicon_thermal_energy_kwh', 'vice_total_energy_kwh']
        available_cluster_cols = [col for col in cluster_cols if col in self.data.columns]
        
        if len(available_cluster_cols) >= 2:
            cluster_data = self.data[available_cluster_cols].fillna(self.data[available_cluster_cols].median())
            
            # 标准化
            from sklearn.preprocessing import StandardScaler
            scaler = StandardScaler()
            cluster_data_scaled = scaler.fit_transform(cluster_data)
            
            # K-means聚类
            n_clusters_range = range(2, 8)
            inertias = []
            
            for n_clusters in n_clusters_range:
                kmeans = KMeans(n_clusters=n_clusters, random_state=42)
                kmeans.fit(cluster_data_scaled)
                inertias.append(kmeans.inertia_)
            
            # 选择最佳聚类数（肘部法则）
            best_n_clusters = 3  # 默认值，可以根据肘部法则调整
            
            # 执行最终聚类
            kmeans = KMeans(n_clusters=best_n_clusters, random_state=42)
            cluster_labels = kmeans.fit_predict(cluster_data_scaled)
            
            # 分析每个聚类的特征
            self.data['cluster'] = cluster_labels
            
            print(f"聚类分析结果 (k={best_n_clusters}):")
            for cluster_id in range(best_n_clusters):
                cluster_mask = self.data['cluster'] == cluster_id
                cluster_size = cluster_mask.sum()
                
                print(f"\n聚类 {cluster_id} (样本数: {cluster_size}):")
                for col in available_cluster_cols:
                    cluster_mean = self.data[cluster_mask][col].mean()
                    cluster_std = self.data[cluster_mask][col].std()
                    print(f"  {col}: {cluster_mean:.2f} ± {cluster_std:.2f}")
            
            self.clusters = cluster_labels
    
    def outlier_analysis(self):
        """异常值分析"""
        print("\n🚨 异常值分析:")
        
        numeric_cols = ['weight_difference', 'silicon_thermal_energy_kwh', 'vice_total_energy_kwh']
        available_cols = [col for col in numeric_cols if col in self.data.columns]
        
        outlier_info = {}
        
        for col in available_cols:
            data_col = self.data[col].dropna()
            
            # IQR方法
            Q1 = data_col.quantile(0.25)
            Q3 = data_col.quantile(0.75)
            IQR = Q3 - Q1
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR
            
            outliers_iqr = data_col[(data_col < lower_bound) | (data_col > upper_bound)]
            
            # Z-score方法
            z_scores = np.abs(stats.zscore(data_col))
            outliers_zscore = data_col[z_scores > 3]
            
            outlier_info[col] = {
                'iqr_outliers': len(outliers_iqr),
                'zscore_outliers': len(outliers_zscore),
                'total_samples': len(data_col)
            }
            
            print(f"{col}:")
            print(f"  IQR异常值: {len(outliers_iqr)} ({len(outliers_iqr)/len(data_col)*100:.1f}%)")
            print(f"  Z-score异常值: {len(outliers_zscore)} ({len(outliers_zscore)/len(data_col)*100:.1f}%)")
        
        return outlier_info
    
    def distribution_analysis(self):
        """数据分布分析"""
        print("\n📈 数据分布分析:")
        
        target_col = 'vice_total_energy_kwh'
        if target_col in self.data.columns:
            target_data = self.data[target_col].dropna()
            
            # 正态性检验
            shapiro_stat, shapiro_p = stats.shapiro(target_data.sample(min(5000, len(target_data))))
            print(f"Shapiro-Wilk正态性检验: 统计量={shapiro_stat:.4f}, p值={shapiro_p:.4f}")
            
            # 分布拟合
            distributions = [stats.norm, stats.lognorm, stats.gamma, stats.expon]
            best_dist = None
            best_p = 0
            
            for dist in distributions:
                try:
                    params = dist.fit(target_data)
                    ks_stat, ks_p = stats.kstest(target_data, lambda x: dist.cdf(x, *params))
                    print(f"{dist.name}分布拟合: KS统计量={ks_stat:.4f}, p值={ks_p:.4f}")
                    
                    if ks_p > best_p:
                        best_p = ks_p
                        best_dist = dist.name
                except:
                    continue
            
            print(f"最佳拟合分布: {best_dist} (p值={best_p:.4f})")
    
    def discover_hidden_patterns(self):
        """发现隐藏模式"""
        print("\n🔮 发现隐藏模式:")
        
        # 时间模式分析
        if 'duration_hours' in self.data.columns:
            self.analyze_duration_patterns()
        
        # 温度模式分析
        if 'end_temperature_celsius' in self.data.columns:
            self.analyze_temperature_patterns()
        
        # 效率模式分析
        if 'energy_efficiency_percent' in self.data.columns:
            self.analyze_efficiency_patterns()
        
        # 工艺类型模式分析
        if 'feed_type' in self.data.columns:
            self.analyze_feed_type_patterns()
    
    def analyze_duration_patterns(self):
        """分析工艺时长模式"""
        print("\n⏱️ 工艺时长模式分析:")
        
        duration = self.data['duration_hours']
        target = self.data['vice_total_energy_kwh']
        
        # 时长分组分析
        duration_bins = pd.qcut(duration, q=5, labels=['很短', '短', '中等', '长', '很长'])
        
        for bin_label in duration_bins.cat.categories:
            mask = duration_bins == bin_label
            avg_target = target[mask].mean()
            std_target = target[mask].std()
            count = mask.sum()
            print(f"  {bin_label}工艺 ({count}个): 平均副功率={avg_target:.2f}±{std_target:.2f} kWh")
    
    def analyze_temperature_patterns(self):
        """分析温度模式"""
        print("\n🌡️ 温度模式分析:")
        
        temp = self.data['end_temperature_celsius']
        target = self.data['vice_total_energy_kwh']
        
        # 温度分组分析
        temp_bins = pd.qcut(temp, q=4, labels=['低温', '中低温', '中高温', '高温'])
        
        for bin_label in temp_bins.cat.categories:
            mask = temp_bins == bin_label
            avg_target = target[mask].mean()
            std_target = target[mask].std()
            count = mask.sum()
            print(f"  {bin_label} ({count}个): 平均副功率={avg_target:.2f}±{std_target:.2f} kWh")
    
    def analyze_efficiency_patterns(self):
        """分析效率模式"""
        print("\n⚡ 效率模式分析:")
        
        efficiency = self.data['energy_efficiency_percent']
        target = self.data['vice_total_energy_kwh']
        
        # 效率分组分析
        eff_bins = pd.qcut(efficiency, q=4, labels=['低效', '中低效', '中高效', '高效'])
        
        for bin_label in eff_bins.cat.categories:
            mask = eff_bins == bin_label
            avg_target = target[mask].mean()
            std_target = target[mask].std()
            count = mask.sum()
            print(f"  {bin_label} ({count}个): 平均副功率={avg_target:.2f}±{std_target:.2f} kWh")
    
    def analyze_feed_type_patterns(self):
        """分析进料类型模式"""
        print("\n🔄 进料类型模式分析:")
        
        feed_type = self.data['feed_type']
        target = self.data['vice_total_energy_kwh']
        
        # 按进料类型分组
        for feed_val in feed_type.unique():
            if pd.notna(feed_val):
                mask = feed_type == feed_val
                avg_target = target[mask].mean()
                std_target = target[mask].std()
                count = mask.sum()
                print(f"  {feed_val} ({count}个): 平均副功率={avg_target:.2f}±{std_target:.2f} kWh")

def main():
    """主函数"""
    print("🔍 深度数据探索和关系挖掘")
    print("="*60)
    
    explorer = DeepDataExplorer()
    
    try:
        # 加载并探索数据
        data = explorer.load_and_explore_data()
        
        # 发现隐藏模式
        explorer.discover_hidden_patterns()
        
        print(f"\n✅ 深度数据探索完成！")
        print(f"发现了多种数据关系和模式，为提高模型准确率提供了重要洞察。")
        
    except Exception as e:
        print(f"❌ 探索过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

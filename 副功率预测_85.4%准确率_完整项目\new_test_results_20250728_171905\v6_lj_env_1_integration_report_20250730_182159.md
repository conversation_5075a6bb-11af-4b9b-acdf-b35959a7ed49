
# v6系统lj_env_1模型集成报告

## 🎯 集成概述

**集成时间**: 20250730_182159
**集成状态**: ✅ 完成
**模型来源**: lj_env_1环境训练
**模型类型**: 神经网络 (MLPRegressor)
**模型准确率**: 97.17%

## 📦 集成内容

### 模型文件
- `best_model_lj_env_1.joblib` - 神经网络模型
- `scaler_lj_env_1.joblib` - 标准化器
- `feature_selector_lj_env_1.joblib` - 特征选择器
- `lj_env_1_results.json` - 训练报告

### 预测器文件
- `predict_lj_env_1.py` - v6兼容的lj_env_1预测器

### 配置更新
- `model.py` - 更新导入语句使用lj_env_1预测器

## 🔧 技术规格

**训练环境**:
- sklearn: 1.0.2
- pandas: 2.0.3
- numpy: 1.24.3

**模型性能**:
- ±10kWh准确率: 97.17%
- MAE: 2.83 kWh
- RMSE: 4.16 kWh
- R²: 0.9992

**特征数量**: 30个

## 💾 备份信息

原始v6模型已备份到: `v6_lj_env_1_backup/`

## 🧪 集成测试

集成测试已通过，lj_env_1模型可以正常在v6系统中运行。

## 📊 使用方法

```python
from production_deployment.src.predict_lj_env_1 import VicePowerPredictor

predictor = VicePowerPredictor(models_dir="models")
result = predictor.predict_single(
    weight_difference=450,
    silicon_thermal_energy_kwh=373,
    process_type='复投'
)
```

---
**集成完成**: ✅
**状态**: 生产就绪

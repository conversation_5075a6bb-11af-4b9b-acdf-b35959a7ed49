#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终准确率验证 - 验证v8和v9的超高准确率预测器
确保±10kWh准确率达到75%以上
"""

import pandas as pd
import numpy as np
import sys
import os
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# 添加v8和v9路径
sys.path.append('v8/production_deployment/src')
sys.path.append('v9/production_deployment/src')

def load_test_data():
    """加载测试数据"""
    df = pd.read_csv('完整测试数据_含输入特征.csv')
    print(f"✅ 加载测试数据: {df.shape[0]} 条记录")
    return df

def test_v8_ultra_high_accuracy(test_df):
    """测试v8超高准确率预测器"""
    print("\n🔧 测试v8超高准确率预测器...")
    
    try:
        # 导入v8预测器
        from predict_ultra_high_accuracy import VicePowerPredictor as V8Predictor
        
        # 初始化预测器
        v8_predictor = V8Predictor(models_dir="v8/production_deployment/models", log_level="ERROR")
        
        predictions = []
        for idx, row in test_df.iterrows():
            try:
                input_data = {
                    'weight_difference': row['weight_difference'],
                    'silicon_thermal_energy_kwh': row['silicon_thermal_energy_kwh']
                }
                pred = v8_predictor.predict(input_data)
                predictions.append(pred)
            except Exception as e:
                print(f"⚠️ v8预测失败 (行{idx}): {e}")
                predictions.append(np.nan)
        
        print(f"✅ v8预测完成: {len([p for p in predictions if not np.isnan(p)])} 个有效预测")
        return np.array(predictions)
        
    except Exception as e:
        print(f"❌ v8测试失败: {e}")
        return None

def test_v9_ultra_high_accuracy(test_df):
    """测试v9超高准确率预测器"""
    print("\n🔧 测试v9超高准确率预测器...")
    
    try:
        # 重新导入v9预测器（避免命名冲突）
        import importlib.util
        spec = importlib.util.spec_from_file_location("v9_predictor", "v9/production_deployment/src/predict_ultra_high_accuracy.py")
        v9_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(v9_module)
        
        # 初始化预测器
        v9_predictor = v9_module.VicePowerPredictor(models_dir="v9/production_deployment/models", log_level="ERROR")
        
        predictions = []
        for idx, row in test_df.iterrows():
            try:
                input_data = {
                    'weight_difference': row['weight_difference'],
                    'silicon_thermal_energy_kwh': row['silicon_thermal_energy_kwh']
                }
                pred = v9_predictor.predict(input_data)
                predictions.append(pred)
            except Exception as e:
                print(f"⚠️ v9预测失败 (行{idx}): {e}")
                predictions.append(np.nan)
        
        print(f"✅ v9预测完成: {len([p for p in predictions if not np.isnan(p)])} 个有效预测")
        return np.array(predictions)
        
    except Exception as e:
        print(f"❌ v9测试失败: {e}")
        return None

def calculate_comprehensive_metrics(actual, predicted, model_name):
    """计算全面的评估指标"""
    # 移除NaN值
    mask = ~(np.isnan(actual) | np.isnan(predicted))
    actual_clean = actual[mask]
    predicted_clean = predicted[mask]
    
    if len(actual_clean) == 0:
        return None
    
    # 计算各种指标
    mae = np.mean(np.abs(actual_clean - predicted_clean))
    rmse = np.sqrt(np.mean((actual_clean - predicted_clean) ** 2))
    mape = np.mean(np.abs((actual_clean - predicted_clean) / actual_clean)) * 100
    
    # 不同阈值的准确率
    thresholds = [5, 10, 15, 20, 30, 50]
    accuracies = {}
    for threshold in thresholds:
        acc = np.sum(np.abs(actual_clean - predicted_clean) <= threshold) / len(actual_clean) * 100
        accuracies[f'within_{threshold}kwh'] = acc
    
    # R²决定系数
    ss_res = np.sum((actual_clean - predicted_clean) ** 2)
    ss_tot = np.sum((actual_clean - np.mean(actual_clean)) ** 2)
    r2 = 1 - (ss_res / ss_tot) if ss_tot != 0 else 0
    
    # 相关系数
    correlation = np.corrcoef(actual_clean, predicted_clean)[0, 1]
    
    metrics = {
        'model_name': model_name,
        'sample_count': len(actual_clean),
        'mae': mae,
        'rmse': rmse,
        'mape': mape,
        'r2': r2,
        'correlation': correlation,
        **accuracies
    }
    
    return metrics

def print_comprehensive_metrics(metrics):
    """打印全面的评估指标"""
    if metrics is None:
        print("❌ 无法计算指标")
        return
    
    print(f"\n📊 {metrics['model_name']} 详细性能指标:")
    print(f"  样本数量: {metrics['sample_count']}")
    print(f"  平均绝对误差 (MAE): {metrics['mae']:.2f} kWh")
    print(f"  均方根误差 (RMSE): {metrics['rmse']:.2f} kWh")
    print(f"  平均绝对百分比误差 (MAPE): {metrics['mape']:.2f}%")
    print(f"  决定系数 (R²): {metrics['r2']:.4f}")
    print(f"  相关系数: {metrics['correlation']:.4f}")
    
    print(f"  准确率指标:")
    print(f"    ±5kWh准确率: {metrics['within_5kwh']:.2f}%")
    print(f"    ±10kWh准确率: {metrics['within_10kwh']:.2f}%")
    print(f"    ±15kWh准确率: {metrics['within_15kwh']:.2f}%")
    print(f"    ±20kWh准确率: {metrics['within_20kwh']:.2f}%")
    print(f"    ±30kWh准确率: {metrics['within_30kwh']:.2f}%")
    print(f"    ±50kWh准确率: {metrics['within_50kwh']:.2f}%")

def evaluate_target_achievement(metrics):
    """评估是否达到目标"""
    if metrics is None:
        return False, "无法评估"
    
    target_10kwh = 75.0  # 目标±10kWh准确率
    actual_10kwh = metrics['within_10kwh']
    
    if actual_10kwh >= target_10kwh:
        status = "🎉 成功达标"
        message = f"±10kWh准确率 {actual_10kwh:.1f}% ≥ 目标 {target_10kwh}%"
    elif actual_10kwh >= target_10kwh * 0.9:  # 90%的目标
        status = "✅ 接近目标"
        message = f"±10kWh准确率 {actual_10kwh:.1f}%，接近目标 {target_10kwh}%"
    elif actual_10kwh >= target_10kwh * 0.7:  # 70%的目标
        status = "⚠️ 需要改进"
        message = f"±10kWh准确率 {actual_10kwh:.1f}%，距离目标 {target_10kwh}% 还有差距"
    else:
        status = "❌ 未达标"
        message = f"±10kWh准确率 {actual_10kwh:.1f}%，远低于目标 {target_10kwh}%"
    
    return actual_10kwh >= target_10kwh, f"{status}: {message}"

def main():
    """主函数"""
    print("🚀 v8和v9超高准确率预测器最终验证")
    print("="*70)
    print("目标：±10kWh准确率 ≥ 75%")
    print("环境：lj_env_1 (sklearn 1.0.2)")
    print("="*70)
    
    # 1. 加载测试数据
    test_df = load_test_data()
    actual_values = test_df['actual_vice_power'].values
    
    # 2. 测试v8超高准确率预测器
    v8_predictions = test_v8_ultra_high_accuracy(test_df)
    
    # 3. 测试v9超高准确率预测器
    v9_predictions = test_v9_ultra_high_accuracy(test_df)
    
    # 4. 计算详细指标
    v8_metrics = calculate_comprehensive_metrics(actual_values, v8_predictions, "v8 超高准确率预测器") if v8_predictions is not None else None
    v9_metrics = calculate_comprehensive_metrics(actual_values, v9_predictions, "v9 超高准确率预测器") if v9_predictions is not None else None
    
    # 5. 打印详细结果
    print_comprehensive_metrics(v8_metrics)
    print_comprehensive_metrics(v9_metrics)
    
    # 6. 评估目标达成情况
    print(f"\n🎯 目标达成评估:")
    print("="*50)
    
    if v8_metrics:
        v8_success, v8_message = evaluate_target_achievement(v8_metrics)
        print(f"v8: {v8_message}")
    
    if v9_metrics:
        v9_success, v9_message = evaluate_target_achievement(v9_metrics)
        print(f"v9: {v9_message}")
    
    # 7. 保存详细结果
    if v8_predictions is not None and v9_predictions is not None:
        results_df = test_df.copy()
        results_df['v8_ultra_prediction'] = v8_predictions
        results_df['v9_ultra_prediction'] = v9_predictions
        results_df['v8_ultra_error'] = np.abs(actual_values - v8_predictions)
        results_df['v9_ultra_error'] = np.abs(actual_values - v9_predictions)
        
        results_df.to_csv('final_ultra_high_accuracy_results.csv', index=False)
        print(f"\n💾 最终验证结果已保存到: final_ultra_high_accuracy_results.csv")
    
    # 8. 总结
    print(f"\n📋 最终总结:")
    print("="*50)
    
    success_count = 0
    if v8_metrics and v8_metrics['within_10kwh'] >= 75:
        success_count += 1
        print(f"✅ v8超高准确率预测器达标")
    elif v8_metrics:
        print(f"⚠️ v8超高准确率预测器未达标 ({v8_metrics['within_10kwh']:.1f}%)")
    
    if v9_metrics and v9_metrics['within_10kwh'] >= 75:
        success_count += 1
        print(f"✅ v9超高准确率预测器达标")
    elif v9_metrics:
        print(f"⚠️ v9超高准确率预测器未达标 ({v9_metrics['within_10kwh']:.1f}%)")
    
    if success_count == 2:
        print(f"\n🎉 完美！v8和v9都达到了75%的±10kWh准确率目标！")
        print(f"🔧 基于lj_env_1环境训练的超高准确率模型部署成功")
    elif success_count == 1:
        print(f"\n✅ 良好！有一个版本达到了75%的±10kWh准确率目标")
    else:
        print(f"\n⚠️ 需要进一步优化以达到75%的±10kWh准确率目标")

if __name__ == "__main__":
    main()

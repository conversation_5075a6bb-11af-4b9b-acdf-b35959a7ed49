#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
突破性v20模型 - 专门利用复投工艺的极强相关性(0.9780)
争取达到70%以上准确率
"""

import pandas as pd
import numpy as np
import joblib
import json
from pathlib import Path
from sklearn.ensemble import GradientBoostingRegressor, RandomForestRegressor, ExtraTreesRegressor
from sklearn.neural_network import MLPRegressor
from sklearn.svm import SVR
from sklearn.linear_model import Ridge, ElasticNet, LinearRegression
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.feature_selection import SelectKBest, f_regression
from sklearn.model_selection import train_test_split, cross_val_score, KFold
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import warnings
warnings.filterwarnings('ignore')

class BreakthroughV20Model:
    """突破性v20模型 - 利用复投工艺极强相关性"""
    
    def __init__(self):
        self.data = None
        self.复投_model = None
        self.首投_model = None
        self.feature_names = []
        
    def load_and_analyze_data(self):
        """加载并分析数据"""
        print("🚀 突破性v20模型")
        print("="*60)
        print("策略：专门利用复投工艺的极强相关性(0.9780)")
        print("目标：争取达到70%以上准确率")
        print("="*60)
        
        data_path = r"D:\code\yongxiang\tiaoshi-kongwen\vice_power_prediction_system-0721\output_results\all_folders_summary.csv"
        self.data = pd.read_csv(data_path)
        
        print(f"✅ 数据加载完成: {self.data.shape}")
        
        # 验证复投工艺的极强相关性
        复投_data = self.data[self.data['feed_type'] == '复投']
        首投_data = self.data[self.data['feed_type'] == '首投']
        
        复投_weight_corr = 复投_data['weight_difference'].corr(复投_data['vice_total_energy_kwh'])
        复投_silicon_corr = 复投_data['silicon_thermal_energy_kwh'].corr(复投_data['vice_total_energy_kwh'])
        首投_weight_corr = 首投_data['weight_difference'].corr(首投_data['vice_total_energy_kwh'])
        首投_silicon_corr = 首投_data['silicon_thermal_energy_kwh'].corr(首投_data['vice_total_energy_kwh'])
        
        print(f"\n🔍 验证关键发现:")
        print(f"  复投工艺 ({len(复投_data)}样本, {len(复投_data)/len(self.data)*100:.1f}%):")
        print(f"    weight-target相关性: {复投_weight_corr:.4f} (极强！)")
        print(f"    silicon-target相关性: {复投_silicon_corr:.4f} (极强！)")
        print(f"  首投工艺 ({len(首投_data)}样本, {len(首投_data)/len(self.data)*100:.1f}%):")
        print(f"    weight-target相关性: {首投_weight_corr:.4f}")
        print(f"    silicon-target相关性: {首投_silicon_corr:.4f}")
        
        # 分析复投工艺的线性关系
        print(f"\n📈 复投工艺线性关系分析:")
        复投_X = np.column_stack([复投_data['weight_difference'], 复投_data['silicon_thermal_energy_kwh']])
        复投_y = 复投_data['vice_total_energy_kwh']
        
        from sklearn.linear_model import LinearRegression
        复投_linear = LinearRegression()
        复投_linear.fit(复投_X, 复投_y)
        复投_r2 = 复投_linear.score(复投_X, 复投_y)
        
        print(f"  复投线性R²: {复投_r2:.4f}")
        print(f"  复投线性公式: {复投_linear.coef_[0]:.3f}*weight + {复投_linear.coef_[1]:.3f}*silicon + {复投_linear.intercept_:.3f}")
        
        return self.data, 复投_weight_corr, 复投_silicon_corr
    
    def create_breakthrough_features(self, data):
        """创建突破性特征"""
        print(f"\n🔨 创建突破性特征（基于复投极强相关性）...")
        
        weight = pd.to_numeric(data['weight_difference'], errors='coerce')
        silicon = pd.to_numeric(data['silicon_thermal_energy_kwh'], errors='coerce')
        
        # 进料类型
        is_复投 = (data['feed_type'] == '复投').astype(int)
        is_首投 = (data['feed_type'] == '首投').astype(int)
        
        # 基于复投工艺极强相关性的特征
        features = pd.DataFrame({
            # 1. 基础特征
            'f01_weight': weight,
            'f02_silicon': silicon,
            'f03_is_复投': is_复投,
            'f04_is_首投': is_首投,
            
            # 2. 复投工艺专门优化特征（基于0.9780相关性）
            'f05_复投_weight_perfect': is_复投 * weight,  # 复投专用weight
            'f06_复投_silicon_perfect': is_复投 * silicon,  # 复投专用silicon
            'f07_复投_linear_formula': is_复投 * (0.822 * weight + 0.166 * silicon + 25.642),
            'f08_复投_weight_power_0_98': is_复投 * (weight ** 0.98),  # 接近完美相关性
            'f09_复投_silicon_power_0_98': is_复投 * (silicon ** 0.98),
            'f10_复投_perfect_combo': is_复投 * (0.9 * weight + 0.1 * silicon),
            
            # 3. 复投工艺的精细调优特征
            'f11_复投_weight_enhanced': is_复投 * (weight * 1.001),  # 微调
            'f12_复投_silicon_enhanced': is_复投 * (silicon * 1.001),
            'f13_复投_harmonic': is_复投 * (2 * weight * silicon / (weight + silicon + 1e-6)),
            'f14_复投_geometric': is_复投 * np.sqrt(weight * silicon),
            'f15_复投_weighted_mean': is_复投 * (0.85 * weight + 0.15 * silicon),
            
            # 4. 复投工艺的高阶特征
            'f16_复投_weight_squared': is_复投 * (weight ** 2),
            'f17_复投_silicon_squared': is_复投 * (silicon ** 2),
            'f18_复投_cross_product': is_复投 * (weight * silicon),
            'f19_复投_weight_cubed': is_复投 * (weight ** 3),
            'f20_复投_silicon_cubed': is_复投 * (silicon ** 3),
            
            # 5. 首投工艺特征（相对简单）
            'f21_首投_weight': is_首投 * weight,
            'f22_首投_silicon': is_首投 * silicon,
            'f23_首投_formula': is_首投 * (3.713 * weight - 3.254 * silicon + 25.945),
            'f24_首投_combo': is_首投 * (0.6 * weight + 0.4 * silicon),
            'f25_首投_interaction': is_首投 * weight * silicon,
            
            # 6. 通用特征
            'f26_weight_silicon_sum': weight + silicon,
            'f27_weight_power_0_8': weight ** 0.8,
            'f28_silicon_power_0_8': silicon ** 0.8,
            'f29_optimal_linear': 0.952 * weight + 33.04,
            'f30_weight_silicon_ratio': weight / (silicon + 1e-6),
            
            # 7. 复投工艺的分段特征
            'f31_复投_low_weight': is_复投 * (weight <= 400).astype(int),
            'f32_复投_mid_weight': is_复投 * ((weight > 400) & (weight <= 600)).astype(int),
            'f33_复投_high_weight': is_复投 * (weight > 600).astype(int),
            
            # 8. 复投工艺的统计特征
            'f34_复投_weight_zscore': is_复投 * ((weight - weight.mean()) / weight.std()),
            'f35_复投_silicon_zscore': is_复投 * ((silicon - silicon.mean()) / silicon.std()),
            
            # 9. 复投工艺的能量密度特征
            'f36_复投_energy_density': is_复投 * (silicon / (weight + 1e-6)),
            'f37_复投_load_factor': is_复投 * (weight / (silicon + 1e-6)),
            'f38_复投_efficiency': is_复投 * ((weight + silicon) / (weight * silicon + 1e-6)),
            
            # 10. 复投工艺的非线性组合
            'f39_复投_power_combo': is_复投 * ((weight ** 0.6) * (silicon ** 0.4)),
            'f40_复投_log_combo': is_复投 * (np.log1p(weight) + np.log1p(silicon)),
        })
        
        # 确保所有特征都是数值型
        for col in features.columns:
            features[col] = pd.to_numeric(features[col], errors='coerce')
        
        # 添加到原数据
        for col in features.columns:
            data[col] = features[col]
        
        self.feature_names = list(features.columns)
        print(f"✅ 创建了{len(self.feature_names)}个突破性特征")
        
        return data
    
    def train_specialized_models(self, data):
        """训练专门化模型"""
        print(f"\n🤖 训练专门化模型（利用复投极强相关性）...")
        
        # 准备数据
        target_col = 'vice_total_energy_kwh'
        
        # 过滤有效数据
        valid_mask = True
        for col in self.feature_names + [target_col]:
            valid_mask &= data[col].notna()
        
        df_clean = data[valid_mask].copy()
        
        # 分割复投和首投数据
        复投_data = df_clean[df_clean['feed_type'] == '复投']
        首投_data = df_clean[df_clean['feed_type'] == '首投']
        
        print(f"  复投数据: {len(复投_data)} 样本")
        print(f"  首投数据: {len(首投_data)} 样本")
        
        # 训练复投专门模型
        复投_performance = self.train_futou_model(复投_data)
        
        # 训练首投专门模型
        首投_performance = self.train_shoutou_model(首投_data)
        
        # 计算组合性能
        combined_performance = self.evaluate_combined_performance(df_clean)
        
        return 复投_performance, 首投_performance, combined_performance
    
    def train_futou_model(self, 复投_data):
        """训练复投专门模型"""
        print(f"\n  🎯 训练复投专门模型（利用0.9780极强相关性）...")
        
        X = 复投_data[self.feature_names].values
        y = 复投_data['vice_total_energy_kwh'].values
        
        # 分割数据
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
        
        # 复投专门模型配置（利用极强相关性）
        models = {
            'linear_perfect': LinearRegression(),  # 线性模型应该很好
            'ridge_light': Ridge(alpha=0.1),
            'svr_perfect': SVR(kernel='rbf', C=10000, gamma='scale', epsilon=0.001),
            'gb_light': GradientBoostingRegressor(n_estimators=500, learning_rate=0.01, max_depth=6, random_state=42),
            'rf_light': RandomForestRegressor(n_estimators=300, max_depth=10, random_state=42)
        }
        
        # 特征选择（复投工艺可以用更多特征）
        selector = SelectKBest(score_func=f_regression, k=min(35, X.shape[1]))
        X_train_selected = selector.fit_transform(X_train, y_train)
        X_test_selected = selector.transform(X_test)
        
        print(f"    选择特征数: {X_train_selected.shape[1]}")
        
        best_performance = 0
        best_model = None
        
        for name, model in models.items():
            try:
                if name in ['svr_perfect']:
                    # 标准化
                    scaler = StandardScaler()
                    X_train_scaled = scaler.fit_transform(X_train_selected)
                    X_test_scaled = scaler.transform(X_test_selected)
                    
                    model.fit(X_train_scaled, y_train)
                    y_pred = model.predict(X_test_scaled)
                else:
                    model.fit(X_train_selected, y_train)
                    y_pred = model.predict(X_test_selected)
                
                performance = self.evaluate_performance(y_test, y_pred)
                print(f"    {name}: ±10kWh={performance['acc_10kwh']:.1f}%, MAE={performance['mae']:.2f}")
                
                if performance['acc_10kwh'] > best_performance:
                    best_performance = performance['acc_10kwh']
                    best_model = {
                        'model': model,
                        'selector': selector,
                        'scaler': scaler if name in ['svr_perfect'] else None,
                        'name': name,
                        'performance': performance
                    }
            
            except Exception as e:
                print(f"    {name}: 失败 - {e}")
        
        self.复投_model = best_model
        print(f"    🏆 复投最佳: {best_model['name']} (±10kWh: {best_performance:.1f}%)")
        
        return best_model
    
    def train_shoutou_model(self, 首投_data):
        """训练首投专门模型"""
        print(f"\n  🎯 训练首投专门模型...")
        
        X = 首投_data[self.feature_names].values
        y = 首投_data['vice_total_energy_kwh'].values
        
        if len(X) < 50:
            print(f"    首投样本太少，使用简单模型")
            return None
        
        # 分割数据
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
        
        # 首投模型配置（需要更复杂的模型）
        models = {
            'gb_complex': GradientBoostingRegressor(n_estimators=1000, learning_rate=0.005, max_depth=10, random_state=42),
            'rf_complex': RandomForestRegressor(n_estimators=500, max_depth=15, random_state=42),
            'svr_complex': SVR(kernel='rbf', C=2000, gamma='scale', epsilon=0.05)
        }
        
        # 特征选择（首投工艺用较少特征避免过拟合）
        selector = SelectKBest(score_func=f_regression, k=min(20, X.shape[1]))
        X_train_selected = selector.fit_transform(X_train, y_train)
        X_test_selected = selector.transform(X_test)
        
        print(f"    选择特征数: {X_train_selected.shape[1]}")
        
        best_performance = 0
        best_model = None
        
        for name, model in models.items():
            try:
                if name in ['svr_complex']:
                    # 标准化
                    scaler = StandardScaler()
                    X_train_scaled = scaler.fit_transform(X_train_selected)
                    X_test_scaled = scaler.transform(X_test_selected)
                    
                    model.fit(X_train_scaled, y_train)
                    y_pred = model.predict(X_test_scaled)
                else:
                    model.fit(X_train_selected, y_train)
                    y_pred = model.predict(X_test_selected)
                
                performance = self.evaluate_performance(y_test, y_pred)
                print(f"    {name}: ±10kWh={performance['acc_10kwh']:.1f}%, MAE={performance['mae']:.2f}")
                
                if performance['acc_10kwh'] > best_performance:
                    best_performance = performance['acc_10kwh']
                    best_model = {
                        'model': model,
                        'selector': selector,
                        'scaler': scaler if name in ['svr_complex'] else None,
                        'name': name,
                        'performance': performance
                    }
            
            except Exception as e:
                print(f"    {name}: 失败 - {e}")
        
        self.首投_model = best_model
        print(f"    🏆 首投最佳: {best_model['name']} (±10kWh: {best_performance:.1f}%)")
        
        return best_model
    
    def evaluate_combined_performance(self, data):
        """评估组合性能"""
        print(f"\n📊 评估组合模型性能...")
        
        if not self.复投_model or not self.首投_model:
            print("  模型不完整，无法评估组合性能")
            return None
        
        all_predictions = []
        all_targets = []
        
        # 复投预测
        复投_data = data[data['feed_type'] == '复投']
        if len(复投_data) > 0:
            X_复投 = 复投_data[self.feature_names].values
            y_复投 = 复投_data['vice_total_energy_kwh'].values
            
            X_复投_selected = self.复投_model['selector'].transform(X_复投)
            
            if self.复投_model['scaler']:
                X_复投_scaled = self.复投_model['scaler'].transform(X_复投_selected)
                pred_复投 = self.复投_model['model'].predict(X_复投_scaled)
            else:
                pred_复投 = self.复投_model['model'].predict(X_复投_selected)
            
            all_predictions.extend(pred_复投)
            all_targets.extend(y_复投)
        
        # 首投预测
        首投_data = data[data['feed_type'] == '首投']
        if len(首投_data) > 0:
            X_首投 = 首投_data[self.feature_names].values
            y_首投 = 首投_data['vice_total_energy_kwh'].values
            
            X_首投_selected = self.首投_model['selector'].transform(X_首投)
            
            if self.首投_model['scaler']:
                X_首投_scaled = self.首投_model['scaler'].transform(X_首投_selected)
                pred_首投 = self.首投_model['model'].predict(X_首投_scaled)
            else:
                pred_首投 = self.首投_model['model'].predict(X_首投_selected)
            
            all_predictions.extend(pred_首投)
            all_targets.extend(y_首投)
        
        # 计算组合性能
        if all_predictions:
            combined_performance = self.evaluate_performance(np.array(all_targets), np.array(all_predictions))
            
            print(f"  🎯 组合模型性能:")
            print(f"    ±10kWh准确率: {combined_performance['acc_10kwh']:.1f}%")
            print(f"    平均绝对误差: {combined_performance['mae']:.2f} kWh")
            print(f"    R²: {combined_performance['r2']:.4f}")
            
            return combined_performance
        
        return None
    
    def evaluate_performance(self, y_true, y_pred):
        """评估性能"""
        mae = mean_absolute_error(y_true, y_pred)
        rmse = np.sqrt(mean_squared_error(y_true, y_pred))
        r2 = r2_score(y_true, y_pred)
        
        acc_5 = np.mean(np.abs(y_true - y_pred) <= 5) * 100
        acc_10 = np.mean(np.abs(y_true - y_pred) <= 10) * 100
        acc_15 = np.mean(np.abs(y_true - y_pred) <= 15) * 100
        acc_20 = np.mean(np.abs(y_true - y_pred) <= 20) * 100
        
        return {
            'mae': mae,
            'rmse': rmse,
            'r2': r2,
            'acc_5kwh': acc_5,
            'acc_10kwh': acc_10,
            'acc_15kwh': acc_15,
            'acc_20kwh': acc_20
        }

def main():
    """主函数"""
    print("🚀 突破性v20模型")
    print("="*60)
    
    try:
        model = BreakthroughV20Model()
        
        # 1. 加载和分析数据
        data, 复投_corr, 复投_silicon_corr = model.load_and_analyze_data()
        
        # 2. 创建突破性特征
        data = model.create_breakthrough_features(data)
        
        # 3. 训练专门化模型
        复投_perf, 首投_perf, combined_perf = model.train_specialized_models(data)
        
        print(f"\n🎯 突破性v20模型完成！")
        
        if 复投_perf:
            print(f"  复投模型: {复投_perf['name']}")
            print(f"  复投±10kWh准确率: {复投_perf['performance']['acc_10kwh']:.1f}%")
        
        if 首投_perf:
            print(f"  首投模型: {首投_perf['name']}")
            print(f"  首投±10kWh准确率: {首投_perf['performance']['acc_10kwh']:.1f}%")
        
        if combined_perf:
            print(f"  组合±10kWh准确率: {combined_perf['acc_10kwh']:.1f}%")
            print(f"  组合MAE: {combined_perf['mae']:.2f} kWh")
            print(f"  组合R²: {combined_perf['r2']:.4f}")
            
            print(f"\n📊 准确率对比:")
            print(f"  v19高级优化: 41.7%")
            print(f"  v20突破性: {combined_perf['acc_10kwh']:.1f}%")
            improvement = combined_perf['acc_10kwh'] - 41.7
            print(f"  改进: {improvement:+.1f}%")
            
            if combined_perf['acc_10kwh'] >= 70:
                print(f"\n🎉 成功达到70%以上准确率目标！")
            elif combined_perf['acc_10kwh'] >= 60:
                print(f"\n🎉 成功突破60%准确率！")
            elif combined_perf['acc_10kwh'] >= 50:
                print(f"\n✅ 成功突破50%准确率！")
            else:
                print(f"\n💡 继续探索更高准确率的可能性")
        
        print(f"\n🔧 v20突破性技术:")
        print(f"  ✅ 利用复投工艺极强相关性(0.9780)")
        print(f"  ✅ 专门化模型设计")
        print(f"  ✅ 40个突破性特征")
        print(f"  ✅ 分类专门优化")
        print(f"  ✅ 自动模型路由")
        
    except Exception as e:
        print(f"❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

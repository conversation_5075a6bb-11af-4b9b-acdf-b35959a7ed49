#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
v6系统改进副功率预测模型全面功能验证和集成检查
"""

import sys
import os
import pandas as pd
import numpy as np
import joblib
from pathlib import Path
import traceback
import json
import warnings
warnings.filterwarnings('ignore')

class V6ValidationSuite:
    """v6系统验证测试套件"""
    
    def __init__(self):
        self.v6_root = Path(__file__).parent.parent.parent / 'v6'
        self.models_dir = self.v6_root / 'production_deployment' / 'models'
        self.src_dir = self.v6_root / 'production_deployment' / 'src'
        self.backup_dir = Path('v6_backups')
        
        self.test_results = {
            'model_loading': {},
            'api_compatibility': {},
            'prediction_functionality': {},
            'integration_status': {},
            'performance_comparison': {},
            'error_handling': {}
        }
        
        # 添加v6路径到系统路径
        sys.path.insert(0, str(self.v6_root))
        
        print(f"🔍 v6系统验证测试套件初始化")
        print(f"  v6根目录: {self.v6_root}")
        print(f"  模型目录: {self.models_dir}")
        print(f"  源码目录: {self.src_dir}")
    
    def test_model_loading(self):
        """1. 模型加载验证"""
        print("\n" + "="*60)
        print("🔧 1. 模型加载验证")
        print("="*60)
        
        results = {}
        
        # 1.1 检查模型文件存在性
        print("📁 1.1 检查模型文件存在性...")
        
        model_files = {
            'real_improved_model.joblib': self.models_dir / 'real_improved_model.joblib',
            'predict_real_improved.py': self.src_dir / 'predict_real_improved.py'
        }
        
        for name, path in model_files.items():
            exists = path.exists()
            size = f"{path.stat().st_size / 1024:.1f}KB" if exists else "N/A"
            status = "✅" if exists else "❌"
            print(f"  {status} {name}: {path} ({size})")
            results[f'{name}_exists'] = exists
        
        # 1.2 加载改进模型文件
        print("\n🔧 1.2 加载改进模型文件...")
        
        try:
            model_path = self.models_dir / 'real_improved_model.joblib'
            if model_path.exists():
                improved_model = joblib.load(model_path)
                print(f"  ✅ 改进模型加载成功")
                
                # 检查模型属性
                expected_attrs = ['bias_corrections', 'error_reduction_factors']
                for attr in expected_attrs:
                    if hasattr(improved_model, attr):
                        print(f"    ✅ 属性 {attr}: {getattr(improved_model, attr)}")
                        results[f'model_{attr}'] = getattr(improved_model, attr)
                    else:
                        print(f"    ❌ 缺少属性: {attr}")
                        results[f'model_{attr}'] = None
                
                results['model_loading_success'] = True
            else:
                print(f"  ❌ 模型文件不存在: {model_path}")
                results['model_loading_success'] = False
                
        except Exception as e:
            print(f"  ❌ 模型加载失败: {e}")
            results['model_loading_success'] = False
            results['model_loading_error'] = str(e)
        
        # 1.3 实例化VicePowerPredictor
        print("\n🔧 1.3 实例化VicePowerPredictor...")
        
        try:
            from production_deployment.src.predict_real_improved import VicePowerPredictor
            
            predictor = VicePowerPredictor(models_dir=str(self.models_dir))
            print(f"  ✅ VicePowerPredictor实例化成功")
            
            # 检查预测器属性
            if hasattr(predictor, 'bias_corrections'):
                print(f"    ✅ 偏差修正参数: {predictor.bias_corrections}")
                results['predictor_bias_corrections'] = predictor.bias_corrections
            
            if hasattr(predictor, 'error_reduction_factors'):
                print(f"    ✅ 误差减少因子: {predictor.error_reduction_factors}")
                results['predictor_error_factors'] = predictor.error_reduction_factors
            
            results['predictor_instantiation'] = True
            self.predictor = predictor
            
        except Exception as e:
            print(f"  ❌ VicePowerPredictor实例化失败: {e}")
            print(f"    错误详情: {traceback.format_exc()}")
            results['predictor_instantiation'] = False
            results['predictor_error'] = str(e)
            self.predictor = None
        
        self.test_results['model_loading'] = results
        return results
    
    def test_api_compatibility(self):
        """2. API接口兼容性测试"""
        print("\n" + "="*60)
        print("🔌 2. API接口兼容性测试")
        print("="*60)
        
        results = {}
        
        if not self.predictor:
            print("❌ 预测器未初始化，跳过API测试")
            results['skipped'] = True
            self.test_results['api_compatibility'] = results
            return results
        
        # 2.1 测试predict_single方法
        print("🔧 2.1 测试predict_single方法...")
        
        test_cases = [
            {'weight_difference': 300, 'silicon_thermal_energy_kwh': 400, 'process_type': '复投'},
            {'weight_difference': 650, 'silicon_thermal_energy_kwh': 800, 'process_type': '复投'},
            {'weight_difference': 750, 'silicon_thermal_energy_kwh': 900, 'process_type': '复投'}
        ]
        
        predict_single_results = []
        
        for i, case in enumerate(test_cases, 1):
            try:
                result = self.predictor.predict_single(
                    weight_difference=case['weight_difference'],
                    silicon_thermal_energy_kwh=case['silicon_thermal_energy_kwh'],
                    process_type=case['process_type']
                )
                
                print(f"  测试用例{i}: ✅")
                print(f"    输入: weight={case['weight_difference']}, energy={case['silicon_thermal_energy_kwh']}")
                print(f"    输出: {result}")
                
                # 检查必需字段
                required_fields = ['predicted_vice_power_kwh', 'confidence', 'model_version']
                missing_fields = [field for field in required_fields if field not in result]
                
                if missing_fields:
                    print(f"    ⚠️ 缺少字段: {missing_fields}")
                else:
                    print(f"    ✅ 包含所有必需字段")
                
                predict_single_results.append({
                    'case': case,
                    'result': result,
                    'success': True,
                    'missing_fields': missing_fields
                })
                
            except Exception as e:
                print(f"  测试用例{i}: ❌ {e}")
                predict_single_results.append({
                    'case': case,
                    'result': None,
                    'success': False,
                    'error': str(e)
                })
        
        results['predict_single'] = predict_single_results
        
        # 2.2 测试predict_batch方法
        print("\n🔧 2.2 测试predict_batch方法...")
        
        try:
            batch_data = [
                {'weight_difference': 300, 'silicon_thermal_energy_kwh': 400, 'process_type': '复投'},
                {'weight_difference': 650, 'silicon_thermal_energy_kwh': 800, 'process_type': '复投'}
            ]
            
            batch_results = self.predictor.predict_batch(batch_data)
            
            print(f"  ✅ 批量预测成功")
            print(f"    输入样本数: {len(batch_data)}")
            print(f"    输出结果数: {len(batch_results)}")
            
            for i, result in enumerate(batch_results):
                print(f"    结果{i+1}: {result.get('predicted_vice_power_kwh', 'N/A'):.2f} kWh")
            
            results['predict_batch'] = {
                'success': True,
                'input_count': len(batch_data),
                'output_count': len(batch_results),
                'results': batch_results
            }
            
        except Exception as e:
            print(f"  ❌ 批量预测失败: {e}")
            results['predict_batch'] = {
                'success': False,
                'error': str(e)
            }
        
        self.test_results['api_compatibility'] = results
        return results
    
    def test_prediction_functionality(self):
        """3. 预测功能验证"""
        print("\n" + "="*60)
        print("🎯 3. 预测功能验证")
        print("="*60)
        
        results = {}
        
        if not self.predictor:
            print("❌ 预测器未初始化，跳过预测功能测试")
            results['skipped'] = True
            self.test_results['prediction_functionality'] = results
            return results
        
        # 3.1 不同功率范围测试
        print("🔧 3.1 不同功率范围预测测试...")
        
        power_range_tests = [
            {'range': '低功率(<600kWh)', 'weight': 300, 'energy': 400, 'expected_correction': 0},
            {'range': '600-700kWh', 'weight': 650, 'energy': 800, 'expected_correction': 10.21},
            {'range': '700-800kWh', 'weight': 750, 'energy': 900, 'expected_correction': 80.13},
            {'range': '高功率(>800kWh)', 'weight': 900, 'energy': 1100, 'expected_correction': 0}
        ]
        
        range_test_results = []
        
        for test in power_range_tests:
            try:
                result = self.predictor.predict_single(
                    weight_difference=test['weight'],
                    silicon_thermal_energy_kwh=test['energy'],
                    process_type='复投'
                )
                
                predicted_value = result.get('predicted_vice_power_kwh', 0)
                original_prediction = result.get('original_prediction', 0)
                corrected_prediction = result.get('corrected_prediction', 0)
                
                # 计算实际应用的修正
                if original_prediction and corrected_prediction:
                    actual_correction = corrected_prediction - original_prediction
                else:
                    actual_correction = 0
                
                print(f"  {test['range']}:")
                print(f"    输入: weight={test['weight']}, energy={test['energy']}")
                print(f"    原始预测: {original_prediction:.2f} kWh")
                print(f"    修正后预测: {corrected_prediction:.2f} kWh")
                print(f"    最终预测: {predicted_value:.2f} kWh")
                print(f"    实际修正: {actual_correction:.2f} kWh (期望: {test['expected_correction']})")
                
                # 验证修正是否正确应用
                correction_applied_correctly = abs(actual_correction - test['expected_correction']) < 1.0
                status = "✅" if correction_applied_correctly else "⚠️"
                print(f"    修正验证: {status}")
                
                range_test_results.append({
                    'range': test['range'],
                    'input': {'weight': test['weight'], 'energy': test['energy']},
                    'original_prediction': original_prediction,
                    'corrected_prediction': corrected_prediction,
                    'final_prediction': predicted_value,
                    'actual_correction': actual_correction,
                    'expected_correction': test['expected_correction'],
                    'correction_correct': correction_applied_correctly
                })
                
            except Exception as e:
                print(f"  {test['range']}: ❌ {e}")
                range_test_results.append({
                    'range': test['range'],
                    'error': str(e),
                    'success': False
                })
        
        results['power_range_tests'] = range_test_results
        
        # 3.2 偏差修正验证
        print("\n🔧 3.2 偏差修正参数验证...")
        
        expected_corrections = {(600, 700): 10.21, (700, 800): 80.13}
        actual_corrections = getattr(self.predictor, 'bias_corrections', {})
        
        correction_verification = {}
        for range_key, expected_value in expected_corrections.items():
            actual_value = actual_corrections.get(range_key, 0)
            is_correct = abs(actual_value - expected_value) < 0.01
            status = "✅" if is_correct else "❌"
            
            print(f"  {range_key}: {status} 期望={expected_value}, 实际={actual_value}")
            correction_verification[str(range_key)] = {
                'expected': expected_value,
                'actual': actual_value,
                'correct': is_correct
            }
        
        results['bias_correction_verification'] = correction_verification
        
        self.test_results['prediction_functionality'] = results
        return results
    
    def test_integration_status(self):
        """4. 集成状态检查"""
        print("\n" + "="*60)
        print("🔗 4. 集成状态检查")
        print("="*60)
        
        results = {}
        
        # 4.1 检查model.py导入更新
        print("🔧 4.1 检查model.py导入更新...")
        
        model_py_path = self.v6_root / 'model.py'
        if model_py_path.exists():
            with open(model_py_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            old_import = "from .production_deployment.src.predict import VicePowerPredictor"
            new_import = "from .production_deployment.src.predict_real_improved import VicePowerPredictor"
            
            has_old_import = old_import in content
            has_new_import = new_import in content
            
            if has_new_import and not has_old_import:
                print(f"  ✅ 导入已正确更新为改进版本")
                results['import_updated'] = True
            elif has_old_import and not has_new_import:
                print(f"  ❌ 仍使用原始导入")
                results['import_updated'] = False
            else:
                print(f"  ⚠️ 导入状态异常 (old={has_old_import}, new={has_new_import})")
                results['import_updated'] = False
            
            results['has_old_import'] = has_old_import
            results['has_new_import'] = has_new_import
        else:
            print(f"  ❌ model.py文件不存在")
            results['model_py_exists'] = False
        
        # 4.2 检查备份文件
        print("\n🔧 4.2 检查备份文件...")
        
        backup_files = [
            'original_predict.py',
            'original_model_v6.py'
        ]
        
        backup_status = {}
        for backup_file in backup_files:
            backup_path = self.backup_dir / backup_file
            exists = backup_path.exists()
            size = f"{backup_path.stat().st_size / 1024:.1f}KB" if exists else "N/A"
            status = "✅" if exists else "❌"
            
            print(f"  {status} {backup_file}: {backup_path} ({size})")
            backup_status[backup_file] = exists
        
        results['backup_files'] = backup_status
        
        # 4.3 测试v6主入口调用
        print("\n🔧 4.3 测试v6主入口调用...")
        
        try:
            # 尝试通过v6主入口调用
            from model import VicePowerPredictor as V6Predictor
            
            v6_predictor = V6Predictor()
            test_result = v6_predictor.predict_single(
                weight_difference=650,
                silicon_thermal_energy_kwh=800,
                process_type='复投'
            )
            
            print(f"  ✅ v6主入口调用成功")
            print(f"    测试结果: {test_result.get('predicted_vice_power_kwh', 'N/A'):.2f} kWh")
            print(f"    模型版本: {test_result.get('model_version', 'N/A')}")
            
            results['v6_main_entry'] = {
                'success': True,
                'test_result': test_result
            }
            
        except Exception as e:
            print(f"  ❌ v6主入口调用失败: {e}")
            results['v6_main_entry'] = {
                'success': False,
                'error': str(e)
            }
        
        self.test_results['integration_status'] = results
        return results
    
    def test_performance_comparison(self):
        """5. 性能对比验证"""
        print("\n" + "="*60)
        print("📊 5. 性能对比验证")
        print("="*60)
        
        results = {}
        
        # 加载真实改进效果数据
        comparison_file = Path('真实改进效果对比.csv')
        if not comparison_file.exists():
            print("❌ 未找到改进效果对比文件")
            results['data_available'] = False
            self.test_results['performance_comparison'] = results
            return results
        
        df = pd.read_csv(comparison_file)
        print(f"📊 加载对比数据: {len(df)} 样本")
        
        # 5.1 整体性能对比
        print("\n🔧 5.1 整体性能对比...")
        
        original_mae = df['absolute_error'].mean()
        improved_mae = df['improved_error'].mean()
        original_acc = (df['absolute_error'] <= 10).mean() * 100
        improved_acc = (df['improved_error'] <= 10).mean() * 100
        
        mae_improvement = original_mae - improved_mae
        mae_improvement_pct = mae_improvement / original_mae * 100
        acc_improvement = improved_acc - original_acc
        
        print(f"  整体MAE: {original_mae:.2f} → {improved_mae:.2f} kWh")
        print(f"  MAE改善: {mae_improvement:.2f} kWh ({mae_improvement_pct:.1f}%)")
        print(f"  整体±10kWh准确率: {original_acc:.1f}% → {improved_acc:.1f}%")
        print(f"  准确率提升: {acc_improvement:+.1f}%")
        
        # 验证是否达到预期
        mae_target_met = mae_improvement_pct >= 80  # 预期改善80%以上
        acc_target_met = acc_improvement >= 15     # 预期提升15%以上
        
        print(f"  MAE改善目标(≥80%): {'✅' if mae_target_met else '❌'}")
        print(f"  准确率提升目标(≥15%): {'✅' if acc_target_met else '❌'}")
        
        results['overall'] = {
            'original_mae': original_mae,
            'improved_mae': improved_mae,
            'mae_improvement': mae_improvement,
            'mae_improvement_pct': mae_improvement_pct,
            'original_acc': original_acc,
            'improved_acc': improved_acc,
            'acc_improvement': acc_improvement,
            'mae_target_met': mae_target_met,
            'acc_target_met': acc_target_met
        }
        
        # 5.2 600-800kWh范围专项验证
        print("\n🔧 5.2 600-800kWh范围专项验证...")
        
        mask_600_800 = (df['actual_vice_power'] >= 600) & (df['actual_vice_power'] < 800)
        range_data = df[mask_600_800]
        
        if len(range_data) > 0:
            range_original_mae = range_data['absolute_error'].mean()
            range_improved_mae = range_data['improved_error'].mean()
            range_original_acc = (range_data['absolute_error'] <= 10).mean() * 100
            range_improved_acc = (range_data['improved_error'] <= 10).mean() * 100
            
            range_mae_improvement = range_original_mae - range_improved_mae
            range_mae_improvement_pct = range_mae_improvement / range_original_mae * 100
            range_acc_improvement = range_improved_acc - range_original_acc
            
            print(f"  600-800kWh样本数: {len(range_data)}")
            print(f"  范围MAE: {range_original_mae:.2f} → {range_improved_mae:.2f} kWh")
            print(f"  范围MAE改善: {range_mae_improvement:.2f} kWh ({range_mae_improvement_pct:.1f}%)")
            print(f"  范围±10kWh准确率: {range_original_acc:.1f}% → {range_improved_acc:.1f}%")
            print(f"  范围准确率提升: {range_acc_improvement:+.1f}%")
            
            # 验证600-800kWh范围目标
            range_mae_target_met = range_mae_improvement_pct >= 80
            range_acc_target_met = range_acc_improvement >= 40  # 600-800kWh范围预期更大提升
            
            print(f"  范围MAE改善目标(≥80%): {'✅' if range_mae_target_met else '❌'}")
            print(f"  范围准确率提升目标(≥40%): {'✅' if range_acc_target_met else '❌'}")
            
            results['600_800_range'] = {
                'sample_count': len(range_data),
                'original_mae': range_original_mae,
                'improved_mae': range_improved_mae,
                'mae_improvement': range_mae_improvement,
                'mae_improvement_pct': range_mae_improvement_pct,
                'original_acc': range_original_acc,
                'improved_acc': range_improved_acc,
                'acc_improvement': range_acc_improvement,
                'mae_target_met': range_mae_target_met,
                'acc_target_met': range_acc_target_met
            }
        else:
            print("  ❌ 600-800kWh范围无数据")
            results['600_800_range'] = {'no_data': True}
        
        self.test_results['performance_comparison'] = results
        return results
    
    def test_error_handling(self):
        """6. 错误处理测试"""
        print("\n" + "="*60)
        print("🛡️ 6. 错误处理测试")
        print("="*60)
        
        results = {}
        
        if not self.predictor:
            print("❌ 预测器未初始化，跳过错误处理测试")
            results['skipped'] = True
            self.test_results['error_handling'] = results
            return results
        
        # 6.1 异常输入测试
        print("🔧 6.1 异常输入测试...")
        
        error_test_cases = [
            {'name': '负数输入', 'weight': -100, 'energy': 200},
            {'name': '零值输入', 'weight': 0, 'energy': 0},
            {'name': '极大值输入', 'weight': 999999, 'energy': 999999},
            {'name': '非数值输入', 'weight': 'abc', 'energy': 'def'},
            {'name': 'None输入', 'weight': None, 'energy': None}
        ]
        
        error_handling_results = []
        
        for test_case in error_test_cases:
            try:
                result = self.predictor.predict_single(
                    weight_difference=test_case['weight'],
                    silicon_thermal_energy_kwh=test_case['energy'],
                    process_type='复投'
                )
                
                # 检查是否返回了错误信息
                has_error = 'error_message' in result or 'error_code' in result
                predicted_value = result.get('predicted_vice_power_kwh')
                
                if has_error:
                    print(f"  {test_case['name']}: ✅ 正确返回错误")
                    print(f"    错误信息: {result.get('error_message', 'N/A')}")
                elif predicted_value is None:
                    print(f"  {test_case['name']}: ✅ 返回None值")
                else:
                    print(f"  {test_case['name']}: ⚠️ 返回了预测值: {predicted_value}")
                
                error_handling_results.append({
                    'test_case': test_case['name'],
                    'input': test_case,
                    'result': result,
                    'handled_correctly': has_error or predicted_value is None
                })
                
            except Exception as e:
                print(f"  {test_case['name']}: ✅ 抛出异常 ({type(e).__name__})")
                error_handling_results.append({
                    'test_case': test_case['name'],
                    'input': test_case,
                    'exception': str(e),
                    'handled_correctly': True
                })
        
        results['error_input_tests'] = error_handling_results
        
        self.test_results['error_handling'] = results
        return results
    
    def generate_validation_report(self):
        """生成验证报告"""
        print("\n" + "="*60)
        print("📋 生成验证报告")
        print("="*60)
        
        # 计算总体成功率
        total_tests = 0
        passed_tests = 0
        
        for category, results in self.test_results.items():
            if 'skipped' in results:
                continue
                
            if category == 'model_loading':
                total_tests += 3
                if results.get('model_loading_success'): passed_tests += 1
                if results.get('predictor_instantiation'): passed_tests += 1
                if results.get('predictor_bias_corrections'): passed_tests += 1
                
            elif category == 'api_compatibility':
                total_tests += 2
                if results.get('predict_single') and all(r['success'] for r in results['predict_single']): passed_tests += 1
                if results.get('predict_batch', {}).get('success'): passed_tests += 1
                
            elif category == 'prediction_functionality':
                total_tests += 2
                if results.get('power_range_tests') and all(r.get('correction_correct', False) for r in results['power_range_tests'] if 'correction_correct' in r): passed_tests += 1
                if results.get('bias_correction_verification') and all(v['correct'] for v in results['bias_correction_verification'].values()): passed_tests += 1
                
            elif category == 'integration_status':
                total_tests += 3
                if results.get('import_updated'): passed_tests += 1
                if results.get('backup_files') and all(results['backup_files'].values()): passed_tests += 1
                if results.get('v6_main_entry', {}).get('success'): passed_tests += 1
                
            elif category == 'performance_comparison':
                total_tests += 2
                if results.get('overall', {}).get('mae_target_met') and results.get('overall', {}).get('acc_target_met'): passed_tests += 1
                if results.get('600_800_range', {}).get('mae_target_met') and results.get('600_800_range', {}).get('acc_target_met'): passed_tests += 1
                
            elif category == 'error_handling':
                total_tests += 1
                if results.get('error_input_tests') and all(r['handled_correctly'] for r in results['error_input_tests']): passed_tests += 1
        
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        # 生成报告
        report = f"""
# v6系统改进副功率预测模型验证报告

## 🎯 验证总结
- **总测试项**: {total_tests}
- **通过测试**: {passed_tests}
- **成功率**: {success_rate:.1f}%
- **验证状态**: {'✅ 通过' if success_rate >= 80 else '❌ 需要修复'}

## 📊 详细验证结果

### 1. 模型加载验证
- 模型文件加载: {'✅' if self.test_results['model_loading'].get('model_loading_success') else '❌'}
- 预测器实例化: {'✅' if self.test_results['model_loading'].get('predictor_instantiation') else '❌'}
- 偏差修正参数: {self.test_results['model_loading'].get('predictor_bias_corrections', 'N/A')}

### 2. API接口兼容性
- predict_single方法: {'✅' if self.test_results.get('api_compatibility', {}).get('predict_single') else '❌'}
- predict_batch方法: {'✅' if self.test_results.get('api_compatibility', {}).get('predict_batch', {}).get('success') else '❌'}

### 3. 预测功能验证
- 功率范围测试: {'✅' if self.test_results.get('prediction_functionality', {}).get('power_range_tests') else '❌'}
- 偏差修正验证: {'✅' if self.test_results.get('prediction_functionality', {}).get('bias_correction_verification') else '❌'}

### 4. 集成状态检查
- 导入语句更新: {'✅' if self.test_results.get('integration_status', {}).get('import_updated') else '❌'}
- 备份文件完整: {'✅' if self.test_results.get('integration_status', {}).get('backup_files') else '❌'}
- v6主入口调用: {'✅' if self.test_results.get('integration_status', {}).get('v6_main_entry', {}).get('success') else '❌'}

### 5. 性能对比验证
"""
        
        if 'performance_comparison' in self.test_results and 'overall' in self.test_results['performance_comparison']:
            overall = self.test_results['performance_comparison']['overall']
            report += f"""
- 整体MAE改善: {overall.get('mae_improvement', 0):.2f} kWh ({overall.get('mae_improvement_pct', 0):.1f}%)
- 整体准确率提升: {overall.get('acc_improvement', 0):+.1f}%
- MAE目标达成: {'✅' if overall.get('mae_target_met') else '❌'}
- 准确率目标达成: {'✅' if overall.get('acc_target_met') else '❌'}
"""
        
        if 'performance_comparison' in self.test_results and '600_800_range' in self.test_results['performance_comparison']:
            range_results = self.test_results['performance_comparison']['600_800_range']
            if not range_results.get('no_data'):
                report += f"""
- 600-800kWh MAE改善: {range_results.get('mae_improvement', 0):.2f} kWh ({range_results.get('mae_improvement_pct', 0):.1f}%)
- 600-800kWh准确率提升: {range_results.get('acc_improvement', 0):+.1f}%
"""
        
        report += f"""
### 6. 错误处理测试
- 异常输入处理: {'✅' if self.test_results.get('error_handling', {}).get('error_input_tests') else '❌'}

## 🎯 结论
{'✅ v6系统集成验证通过，改进模型运行正常' if success_rate >= 80 else '❌ v6系统集成存在问题，需要修复'}

---
**验证时间**: 2025-07-28
**验证状态**: {'完成' if success_rate >= 80 else '需要修复'}
"""
        
        with open('v6系统验证报告.md', 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"✅ 验证报告已保存: v6系统验证报告.md")
        print(f"📊 总体验证成功率: {success_rate:.1f}%")
        
        return report

def main():
    """主函数"""
    print("🚀 v6系统改进副功率预测模型全面验证")
    print("="*60)
    
    # 创建验证套件
    validator = V6ValidationSuite()
    
    # 执行所有验证测试
    validator.test_model_loading()
    validator.test_api_compatibility()
    validator.test_prediction_functionality()
    validator.test_integration_status()
    validator.test_performance_comparison()
    validator.test_error_handling()
    
    # 生成验证报告
    validator.generate_validation_report()
    
    # 保存详细测试结果
    with open('v6验证详细结果.json', 'w', encoding='utf-8') as f:
        json.dump(validator.test_results, f, ensure_ascii=False, indent=2, default=str)
    
    print(f"\n🎯 v6系统验证完成！")
    print(f"📋 详细结果已保存: v6验证详细结果.json")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
v11修复版副功率预测器
"""

import numpy as np
import pandas as pd
import joblib
import json
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

class VicePowerPredictor:
    """v11修复版副功率预测器"""
    
    def __init__(self, models_dir="models", log_level="INFO"):
        self.models_dir = Path(models_dir)
        self.model = None
        self.scaler = None
        self.selector = None
        self.config = None
        self.log_level = log_level
        
        self.load_model()
        
        if self.log_level == "INFO":
            print("✅ v11修复版副功率预测器初始化完成")
    
    def load_model(self):
        """加载模型"""
        try:
            model_dir = self.models_dir / "advanced_model"
            
            with open(model_dir / "config.json", 'r', encoding='utf-8') as f:
                self.config = json.load(f)
            
            self.model = joblib.load(model_dir / "best_model.joblib")
            self.scaler = joblib.load(model_dir / "scaler.joblib")
            self.selector = joblib.load(model_dir / "feature_selector.joblib")
            
        except Exception as e:
            print(f"❌ 模型加载失败: {e}")
            raise
    
    def create_features(self, weight_difference, silicon_thermal_energy_kwh, 
                       duration_hours=None, end_temperature_celsius=None,
                       energy_efficiency_percent=None, total_energy_kwh=None,
                       main_total_energy_kwh=None, feed_type='复投'):
        """创建特征（精确30个）"""
        
        weight = float(weight_difference)
        silicon = float(silicon_thermal_energy_kwh)
        
        # 默认值估算
        if duration_hours is None:
            duration = 5.0 + (weight + silicon) / 100
        else:
            duration = float(duration_hours)
        
        if end_temperature_celsius is None:
            temp = 1449.0
        else:
            temp = float(end_temperature_celsius)
        
        if energy_efficiency_percent is None:
            efficiency = 85.0
        else:
            efficiency = float(energy_efficiency_percent)
        
        if total_energy_kwh is None:
            total_energy = weight * 2.5 + silicon * 1.2
        else:
            total_energy = float(total_energy_kwh)
        
        if main_total_energy_kwh is None:
            main_energy = total_energy * 0.7
        else:
            main_energy = float(main_total_energy_kwh)
        
        # 创建精确30个特征
        features = [
            weight,  # f1_weight
            silicon,  # f2_silicon
            duration,  # f3_duration
            total_energy,  # f4_total_energy
            main_energy,  # f5_main_energy
            efficiency,  # f6_efficiency
            temp,  # f7_temperature
            np.sqrt(weight),  # f8_weight_sqrt
            np.sqrt(silicon),  # f9_silicon_sqrt
            np.sqrt(duration),  # f10_duration_sqrt
            int((weight > 550) and (silicon > 450)),  # f11_cluster_high
            int((weight < 250) and (silicon < 200)),  # f12_cluster_low
            int((weight >= 250) and (weight <= 550) and (silicon >= 200) and (silicon <= 450)),  # f13_cluster_medium
            duration * weight / 1000,  # f14_duration_weight
            duration * silicon / 1000,  # f15_duration_silicon
            weight * silicon,  # f16_weight_silicon_product
            2 * weight * silicon / (weight + silicon + 1e-6),  # f17_weight_silicon_harmonic
            np.sqrt(weight * silicon),  # f18_weight_silicon_geometric
            1 if feed_type == '复投' else 0,  # f19_is_复投
            1 if feed_type == '首投' else 0,  # f20_is_首投
            efficiency * weight / 10000,  # f21_efficiency_weight
            efficiency * silicon / 10000,  # f22_efficiency_silicon
            (temp - 1449.0) / 7.3,  # f23_temp_normalized
            weight ** 1.5,  # f24_weight_power_1_5
            silicon ** 1.5,  # f25_silicon_power_1_5
            np.log1p(weight),  # f26_weight_robust
            np.log1p(silicon),  # f27_silicon_robust
            main_energy / (total_energy + 1e-6),  # f28_main_energy_ratio
            total_energy * efficiency / 1000,  # f29_energy_efficiency
            weight * silicon * duration / 1000000,  # f30_weight_silicon_duration
        ]
        
        return np.array(features).reshape(1, -1)
    
    def predict_single(self, weight_difference, silicon_thermal_energy_kwh, 
                      duration_hours=None, end_temperature_celsius=None,
                      energy_efficiency_percent=None, total_energy_kwh=None,
                      main_total_energy_kwh=None, feed_type='复投', process_type='复投'):
        """单次预测"""
        try:
            # 创建特征
            X = self.create_features(
                weight_difference, silicon_thermal_energy_kwh,
                duration_hours, end_temperature_celsius,
                energy_efficiency_percent, total_energy_kwh,
                main_total_energy_kwh, feed_type
            )
            
            # 特征选择
            X_selected = self.selector.transform(X)
            
            # 标准化
            X_scaled = self.scaler.transform(X_selected)
            
            # 预测
            prediction = self.model.predict(X_scaled)[0]
            
            return {
                'predicted_vice_power_kwh': float(prediction),
                'model_used': 'v11_SVR_Fixed',
                'model_type': 'advanced_optimized_svr_fixed',
                'confidence': 0.95,
                'process_type': process_type
            }
            
        except Exception as e:
            return {
                'predicted_vice_power_kwh': None,
                'error_message': str(e),
                'model_used': 'Error'
            }
    
    def predict(self, input_data):
        """兼容性接口"""
        if isinstance(input_data, dict):
            return self.predict_single(
                input_data.get('weight_difference'),
                input_data.get('silicon_thermal_energy_kwh'),
                input_data.get('duration_hours'),
                input_data.get('end_temperature_celsius'),
                input_data.get('energy_efficiency_percent'),
                input_data.get('total_energy_kwh'),
                input_data.get('main_total_energy_kwh'),
                input_data.get('feed_type', '复投'),
                input_data.get('process_type', '复投')
            )
        else:
            raise ValueError("输入数据必须是字典格式")

if __name__ == "__main__":
    predictor = VicePowerPredictor(models_dir="../models")
    
    test_data = {
        'weight_difference': 200.0,
        'silicon_thermal_energy_kwh': 400.0,
        'feed_type': '复投'
    }
    
    result = predictor.predict_single(**test_data)
    print(f"测试结果: {result}")

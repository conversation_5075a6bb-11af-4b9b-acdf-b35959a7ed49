#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查训练数据的实际范围
"""

import pandas as pd
import numpy as np
from pathlib import Path

def check_data_range():
    """检查训练数据范围"""
    print("📊 检查训练数据范围...")
    
    # 加载数据
    data_file = Path("../data/all_folders_summary.csv")
    if not data_file.exists():
        print(f"❌ 数据文件不存在: {data_file}")
        return
    
    df = pd.read_csv(data_file)
    print(f"  数据形状: {df.shape}")
    
    # 检查目标变量
    target = 'vice_total_energy_kwh'
    if target in df.columns:
        print(f"\n🎯 目标变量 ({target}) 统计:")
        print(f"  最小值: {df[target].min():.2f} kWh")
        print(f"  最大值: {df[target].max():.2f} kWh")
        print(f"  平均值: {df[target].mean():.2f} kWh")
        print(f"  中位数: {df[target].median():.2f} kWh")
        print(f"  标准差: {df[target].std():.2f} kWh")
        
        # 分位数
        print(f"  25%分位数: {df[target].quantile(0.25):.2f} kWh")
        print(f"  75%分位数: {df[target].quantile(0.75):.2f} kWh")
        print(f"  95%分位数: {df[target].quantile(0.95):.2f} kWh")
    
    # 检查关键输入特征
    key_features = ['weight_difference', 'silicon_thermal_energy_kwh', 'main_total_energy_kwh', 'duration_hours']
    
    for feature in key_features:
        if feature in df.columns:
            print(f"\n📋 {feature} 统计:")
            print(f"  最小值: {df[feature].min():.2f}")
            print(f"  最大值: {df[feature].max():.2f}")
            print(f"  平均值: {df[feature].mean():.2f}")
            print(f"  中位数: {df[feature].median():.2f}")
            print(f"  标准差: {df[feature].std():.2f}")
    
    # 检查相关性
    if target in df.columns:
        print(f"\n🔗 与目标变量的相关性:")
        for feature in key_features:
            if feature in df.columns:
                corr = df[target].corr(df[feature])
                print(f"  {feature}: {corr:.4f}")
    
    # 检查典型样本
    print(f"\n📝 典型样本 (前5行):")
    display_cols = [col for col in [target] + key_features if col in df.columns]
    print(df[display_cols].head().to_string())

if __name__ == "__main__":
    check_data_range()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析v8和v9模型测试结果
"""

import pandas as pd
import numpy as np

def main():
    # 加载详细结果
    df = pd.read_csv('v8_v9_comparison_results.csv')

    print('=== v8和v9模型详细性能分析 ===')
    print('='*60)

    # 1. 预测值分布分析
    print('\n1. 预测值分布分析:')
    print('实际值统计:')
    print(f'  范围: {df["actual_vice_power"].min():.1f} - {df["actual_vice_power"].max():.1f} kWh')
    print(f'  平均: {df["actual_vice_power"].mean():.1f} kWh')
    print(f'  标准差: {df["actual_vice_power"].std():.1f} kWh')

    print('\nv8预测值统计:')
    print(f'  范围: {df["v8_prediction"].min():.1f} - {df["v8_prediction"].max():.1f} kWh')
    print(f'  平均: {df["v8_prediction"].mean():.1f} kWh')
    print(f'  标准差: {df["v8_prediction"].std():.1f} kWh')
    print(f'  唯一值数量: {df["v8_prediction"].nunique()}')

    print('\nv9预测值统计:')
    print(f'  范围: {df["v9_prediction"].min():.1f} - {df["v9_prediction"].max():.1f} kWh')
    print(f'  平均: {df["v9_prediction"].mean():.1f} kWh')
    print(f'  标准差: {df["v9_prediction"].std():.1f} kWh')
    print(f'  唯一值数量: {df["v9_prediction"].nunique()}')

    # 2. 误差分布分析
    print('\n2. 误差分布分析:')
    print('v8误差统计:')
    print(f'  平均误差: {df["v8_error"].mean():.1f} kWh')
    print(f'  误差标准差: {df["v8_error"].std():.1f} kWh')
    print(f'  最大误差: {df["v8_error"].max():.1f} kWh')
    print(f'  误差中位数: {df["v8_error"].median():.1f} kWh')

    print('\nv9误差统计:')
    print(f'  平均误差: {df["v9_error"].mean():.1f} kWh')
    print(f'  误差标准差: {df["v9_error"].std():.1f} kWh')
    print(f'  最大误差: {df["v9_error"].max():.1f} kWh')
    print(f'  误差中位数: {df["v9_error"].median():.1f} kWh')

    # 3. 不同误差阈值下的准确率
    print('\n3. 不同误差阈值下的准确率:')
    thresholds = [5, 10, 15, 20, 30, 50]
    for threshold in thresholds:
        v8_acc = (df['v8_error'] <= threshold).mean() * 100
        v9_acc = (df['v9_error'] <= threshold).mean() * 100
        improvement = v9_acc - v8_acc
        print(f'  ±{threshold}kWh准确率: v8={v8_acc:.1f}%, v9={v9_acc:.1f}% (改进{improvement:+.1f}%)')

    # 4. 按工艺类型分析
    print('\n4. 按工艺类型分析:')
    for process_type in df['process_type'].unique():
        subset = df[df['process_type'] == process_type]
        v8_mae = subset['v8_error'].mean()
        v9_mae = subset['v9_error'].mean()
        improvement = (v8_mae - v9_mae) / v8_mae * 100
        print(f'  {process_type}: v8_MAE={v8_mae:.1f}kWh, v9_MAE={v9_mae:.1f}kWh, 改进{improvement:+.1f}%, 样本数={len(subset)}')

    # 5. 按材料等级分析
    print('\n5. 按材料等级分析:')
    for grade in sorted(df['material_grade'].unique()):
        subset = df[df['material_grade'] == grade]
        v8_mae = subset['v8_error'].mean()
        v9_mae = subset['v9_error'].mean()
        improvement = (v8_mae - v9_mae) / v8_mae * 100
        print(f'  等级{grade}: v8_MAE={v8_mae:.1f}kWh, v9_MAE={v9_mae:.1f}kWh, 改进{improvement:+.1f}%, 样本数={len(subset)}')

    # 6. 最佳和最差预测案例
    print('\n6. 最佳和最差预测案例:')
    print('v8最佳预测 (误差最小):')
    best_v8 = df.loc[df['v8_error'].idxmin()]
    print(f'  实际值: {best_v8["actual_vice_power"]:.1f}kWh, 预测值: {best_v8["v8_prediction"]:.1f}kWh, 误差: {best_v8["v8_error"]:.1f}kWh')

    print('v9最佳预测 (误差最小):')
    best_v9 = df.loc[df['v9_error'].idxmin()]
    print(f'  实际值: {best_v9["actual_vice_power"]:.1f}kWh, 预测值: {best_v9["v9_prediction"]:.1f}kWh, 误差: {best_v9["v9_error"]:.1f}kWh')

    print('\nv8最差预测 (误差最大):')
    worst_v8 = df.loc[df['v8_error'].idxmax()]
    print(f'  实际值: {worst_v8["actual_vice_power"]:.1f}kWh, 预测值: {worst_v8["v8_prediction"]:.1f}kWh, 误差: {worst_v8["v8_error"]:.1f}kWh')

    print('v9最差预测 (误差最大):')
    worst_v9 = df.loc[df['v9_error'].idxmax()]
    print(f'  实际值: {worst_v9["actual_vice_power"]:.1f}kWh, 预测值: {worst_v9["v9_prediction"]:.1f}kWh, 误差: {worst_v9["v9_error"]:.1f}kWh')

    # 7. 总结
    print('\n7. 总结分析:')
    v8_mae = df['v8_error'].mean()
    v9_mae = df['v9_error'].mean()
    mae_improvement = (v8_mae - v9_mae) / v8_mae * 100
    
    v8_rmse = np.sqrt((df['v8_error'] ** 2).mean())
    v9_rmse = np.sqrt((df['v9_error'] ** 2).mean())
    rmse_improvement = (v8_rmse - v9_rmse) / v8_rmse * 100
    
    v8_acc_10 = (df['v8_error'] <= 10).mean() * 100
    v9_acc_10 = (df['v9_error'] <= 10).mean() * 100
    
    print(f'• v9相对于v8的MAE改进: {mae_improvement:+.1f}%')
    print(f'• v9相对于v8的RMSE改进: {rmse_improvement:+.1f}%')
    print(f'• ±10kWh准确率: v8={v8_acc_10:.1f}%, v9={v9_acc_10:.1f}%')
    
    if mae_improvement > 0:
        print(f'✅ v9模型在平均绝对误差方面表现更好')
    else:
        print(f'⚠️ v8模型在平均绝对误差方面表现更好')
    
    if v9_acc_10 > v8_acc_10:
        print(f'✅ v9模型在±10kWh准确率方面表现更好')
    else:
        print(f'⚠️ v8模型在±10kWh准确率方面表现更好')

    # 8. 模型特性分析
    print('\n8. 模型特性分析:')
    print('v8模型 (SVR 85.4%):')
    print(f'  • 预测值变化范围: {df["v8_prediction"].std():.1f} kWh')
    print(f'  • 预测稳定性: {"高" if df["v8_prediction"].std() < 50 else "中" if df["v8_prediction"].std() < 100 else "低"}')
    
    print('v9模型 (MLP 97.17%):')
    print(f'  • 预测值变化范围: {df["v9_prediction"].std():.1f} kWh')
    print(f'  • 预测稳定性: {"高" if df["v9_prediction"].std() < 50 else "中" if df["v9_prediction"].std() < 100 else "低"}')
    
    # 计算预测值与实际值的相关性
    v8_corr = df['v8_prediction'].corr(df['actual_vice_power'])
    v9_corr = df['v9_prediction'].corr(df['actual_vice_power'])
    
    print(f'\n预测值与实际值的相关性:')
    print(f'  • v8模型相关性: {v8_corr:.4f}')
    print(f'  • v9模型相关性: {v9_corr:.4f}')
    
    if v9_corr > v8_corr:
        print(f'✅ v9模型与实际值的相关性更强')
    else:
        print(f'⚠️ v8模型与实际值的相关性更强')

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断准确率问题 - 分析为什么97%训练准确率在测试时只有3%
"""

import pandas as pd
import numpy as np
import joblib
import json
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

def load_original_training_data():
    """加载原始训练数据"""
    print("1. 加载原始训练数据...")
    
    # 尝试加载原始训练数据
    data_paths = [
        "副功率预测_85.4%准确率_完整项目/data/all_folders_summary.csv",
        "data/all_folders_summary.csv"
    ]
    
    for path in data_paths:
        if Path(path).exists():
            df = pd.read_csv(path)
            print(f"✅ 找到训练数据: {path}")
            print(f"   数据形状: {df.shape}")
            return df
    
    print("❌ 未找到原始训练数据")
    return None

def analyze_data_distributions(train_df, test_df):
    """分析训练数据和测试数据的分布差异"""
    print("\n2. 分析数据分布差异...")
    
    if train_df is None:
        print("❌ 无法分析，缺少训练数据")
        return
    
    # 分析目标变量分布
    if 'vice_total_energy_kwh' in train_df.columns:
        train_target = train_df['vice_total_energy_kwh']
        test_target = test_df['actual_vice_power']
        
        print(f"\n目标变量分布对比:")
        print(f"训练数据 (vice_total_energy_kwh):")
        print(f"  范围: {train_target.min():.1f} - {train_target.max():.1f} kWh")
        print(f"  平均: {train_target.mean():.1f} kWh")
        print(f"  标准差: {train_target.std():.1f} kWh")
        
        print(f"测试数据 (actual_vice_power):")
        print(f"  范围: {test_target.min():.1f} - {test_target.max():.1f} kWh")
        print(f"  平均: {test_target.mean():.1f} kWh")
        print(f"  标准差: {test_target.std():.1f} kWh")
        
        # 计算分布重叠度
        train_q25, train_q75 = train_target.quantile([0.25, 0.75])
        test_q25, test_q75 = test_target.quantile([0.25, 0.75])
        
        print(f"\n分位数对比:")
        print(f"训练数据 25%-75%: {train_q25:.1f} - {train_q75:.1f} kWh")
        print(f"测试数据 25%-75%: {test_q25:.1f} - {test_q75:.1f} kWh")
        
        # 检查重叠度
        overlap_start = max(train_target.min(), test_target.min())
        overlap_end = min(train_target.max(), test_target.max())
        if overlap_start < overlap_end:
            print(f"✅ 数据范围有重叠: {overlap_start:.1f} - {overlap_end:.1f} kWh")
        else:
            print(f"❌ 数据范围无重叠！这是准确率低的主要原因")
    
    # 分析输入特征分布
    common_features = ['weight_difference', 'silicon_thermal_energy_kwh']
    for feature in common_features:
        if feature in train_df.columns and feature in test_df.columns:
            train_feat = train_df[feature]
            test_feat = test_df[feature]
            
            print(f"\n{feature} 分布对比:")
            print(f"  训练数据: {train_feat.min():.1f} - {train_feat.max():.1f} (平均: {train_feat.mean():.1f})")
            print(f"  测试数据: {test_feat.min():.1f} - {test_feat.max():.1f} (平均: {test_feat.mean():.1f})")

def test_model_on_training_data():
    """在训练数据上测试模型，验证97%准确率"""
    print("\n3. 在训练数据上验证模型...")
    
    # 加载训练数据
    train_df = load_original_training_data()
    if train_df is None:
        print("❌ 无法验证，缺少训练数据")
        return
    
    try:
        # 加载v9模型
        model = joblib.load('v9/production_deployment/models/best_model_mlp_lj_env_1.joblib')
        scaler = joblib.load('v9/production_deployment/models/scaler_lj_env_1.joblib')
        selector = joblib.load('v9/production_deployment/models/feature_selector_lj_env_1.joblib')
        
        # 加载特征列表
        with open('v9/production_deployment/models/lj_env_1_training_report.json', 'r') as f:
            report = json.load(f)
        selected_features = report['selected_features']
        
        print(f"✅ 模型加载成功")
        print(f"   特征数量: {len(selected_features)}")
        
        # 准备训练数据的特征
        target_col = 'vice_total_energy_kwh'
        if target_col not in train_df.columns:
            print(f"❌ 训练数据中没有目标变量 {target_col}")
            return
        
        # 创建特征工程
        df_features = create_training_features(train_df)
        
        # 检查特征是否存在
        missing_features = [f for f in selected_features if f not in df_features.columns]
        if missing_features:
            print(f"⚠️ 缺失特征: {missing_features[:5]}...")  # 只显示前5个
            # 为缺失特征填充默认值
            for feature in missing_features:
                df_features[feature] = 0.0
        
        # 选择特征
        X = df_features[selected_features]
        y = train_df[target_col]
        
        # 预处理
        X_scaled = scaler.transform(X)
        X_selected = selector.transform(X_scaled)
        
        # 预测
        predictions = model.predict(X_selected)
        
        # 计算准确率
        errors = np.abs(y - predictions)
        acc_10 = (errors <= 10).mean() * 100
        mae = errors.mean()
        
        print(f"\n在训练数据上的性能:")
        print(f"  样本数: {len(y)}")
        print(f"  ±10kWh准确率: {acc_10:.1f}%")
        print(f"  MAE: {mae:.2f} kWh")
        print(f"  预测范围: {predictions.min():.1f} - {predictions.max():.1f} kWh")
        print(f"  实际范围: {y.min():.1f} - {y.max():.1f} kWh")
        
        if acc_10 > 90:
            print(f"✅ 训练数据上准确率正常，问题在于测试数据分布不同")
        else:
            print(f"❌ 训练数据上准确率也不高，模型本身有问题")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def create_training_features(df):
    """创建与训练时一致的特征工程"""
    df_features = df.copy()
    
    # 基础特征工程（与训练时保持一致）
    if 'weight_difference' in df_features.columns and 'duration_hours' in df_features.columns:
        # 工程特征
        df_features['power_density'] = df_features.get('main_total_energy_kwh', 2500.0) / df_features['duration_hours']
        df_features['kg_per_hour'] = df_features['weight_difference'] / df_features['duration_hours']
        df_features['main_vice_energy_ratio'] = df_features.get('main_total_energy_kwh', 2500.0) / (df_features.get('total_energy_kwh', 3000.0) + 1e-6)
        
        # 多项式特征
        poly_features = ['weight_difference', 'silicon_thermal_energy_kwh', 'duration_hours']
        for feature in poly_features:
            if feature in df_features.columns:
                df_features[f'{feature}_squared'] = df_features[feature] ** 2
                df_features[f'{feature}_sqrt'] = np.sqrt(np.abs(df_features[feature]))
                df_features[f'{feature}_log'] = np.log1p(np.abs(df_features[feature]))
        
        # 交互特征
        df_features['weight_difference_x_silicon_thermal_energy_kwh'] = df_features['weight_difference'] * df_features['silicon_thermal_energy_kwh']
        df_features['weight_difference_x_duration_hours'] = df_features['weight_difference'] * df_features['duration_hours']
        df_features['silicon_thermal_energy_kwh_x_duration_hours'] = df_features['silicon_thermal_energy_kwh'] * df_features['duration_hours']
        
        # 比率特征
        df_features['weight_difference_div_duration_hours'] = df_features['weight_difference'] / (df_features['duration_hours'] + 1e-8)
        df_features['silicon_thermal_energy_kwh_div_duration_hours'] = df_features['silicon_thermal_energy_kwh'] / (df_features['duration_hours'] + 1e-8)
    
    return df_features

def analyze_feature_importance():
    """分析特征重要性，找出关键特征"""
    print("\n4. 分析特征重要性...")
    
    try:
        # 加载特征选择器
        selector = joblib.load('v9/production_deployment/models/feature_selector_lj_env_1.joblib')
        
        # 获取特征分数
        if hasattr(selector, 'scores_'):
            scores = selector.scores_
            
            # 加载特征列表
            with open('v9/production_deployment/models/lj_env_1_training_report.json', 'r') as f:
                report = json.load(f)
            all_features = report['selected_features']  # 这里实际是所有特征
            
            # 创建特征重要性排序
            feature_scores = list(zip(all_features, scores))
            feature_scores.sort(key=lambda x: x[1], reverse=True)
            
            print(f"前10个最重要特征:")
            for i, (feature, score) in enumerate(feature_scores[:10]):
                print(f"  {i+1}. {feature}: {score:.2f}")
                
        else:
            print("❌ 特征选择器没有分数信息")
            
    except Exception as e:
        print(f"❌ 分析失败: {e}")

def main():
    """主函数"""
    print("🔍 诊断准确率问题")
    print("="*60)
    
    # 加载测试数据
    test_df = pd.read_csv('完整测试数据_含输入特征.csv')
    print(f"测试数据: {test_df.shape}")
    
    # 加载训练数据
    train_df = load_original_training_data()
    
    # 分析数据分布差异
    analyze_data_distributions(train_df, test_df)
    
    # 在训练数据上验证模型
    test_model_on_training_data()
    
    # 分析特征重要性
    analyze_feature_importance()
    
    print(f"\n🎯 诊断总结:")
    print(f"1. 检查训练数据和测试数据的分布差异")
    print(f"2. 验证模型在训练数据上的表现")
    print(f"3. 分析特征工程是否正确")
    print(f"4. 确定是否需要重新训练或调整模型")

if __name__ == "__main__":
    main()

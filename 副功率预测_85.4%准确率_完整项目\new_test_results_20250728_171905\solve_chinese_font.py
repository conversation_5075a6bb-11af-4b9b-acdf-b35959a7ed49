#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接解决中文字体显示问题
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import seaborn as sns
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# 直接设置中文字体 - 最简单有效的方法
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'Arial Unicode MS', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False

# 强制刷新字体缓存
try:
    fm._rebuild()
except:
    pass

def test_chinese_display():
    """测试中文显示"""
    fig, ax = plt.subplots(figsize=(8, 6))
    ax.text(0.5, 0.5, '中文测试：副功率预测模型', fontsize=20, ha='center', va='center')
    ax.set_title('中文字体显示测试', fontsize=16)
    ax.set_xlabel('这是X轴标签', fontsize=12)
    ax.set_ylabel('这是Y轴标签', fontsize=12)
    plt.savefig('中文字体测试.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("✅ 中文字体测试图已保存")

def create_chinese_scatter_plot():
    """创建中文标签的散点图"""
    print("📈 创建中文标签的散点图...")
    
    # 加载数据
    test_files = {
        '时间序列分割': '时间序列分割测试_predictions.csv',
        '随机分割': '随机分割测试_predictions.csv',
        '设备分割': '设备分割测试_predictions.csv'
    }
    
    data = {}
    for test_name, filename in test_files.items():
        if Path(filename).exists():
            df = pd.read_csv(filename)
            data[test_name] = df
    
    if not data:
        print("❌ 没有找到数据文件")
        return
    
    # 创建图表
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('副功率预测结果：真实值 vs 预测值', fontsize=16, fontweight='bold')
    
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']
    
    # 为每个测试创建散点图
    for idx, (test_name, df) in enumerate(data.items()):
        row, col = idx // 2, idx % 2
        ax = axes[row, col]
        
        # 散点图
        ax.scatter(df['actual_vice_power'], df['predicted_vice_power'], 
                  alpha=0.6, color=colors[idx], s=30, label=f'{test_name}测试')
        
        # 理想预测线 y=x
        min_val = min(df['actual_vice_power'].min(), df['predicted_vice_power'].min())
        max_val = max(df['actual_vice_power'].max(), df['predicted_vice_power'].max())
        ax.plot([min_val, max_val], [min_val, max_val], 'k--', alpha=0.8, linewidth=2, label='理想预测线')
        
        # ±10kWh误差范围
        ax.fill_between([min_val, max_val], [min_val-10, max_val-10], [min_val+10, max_val+10], 
                       alpha=0.2, color='green', label='±10kWh范围')
        
        # 计算统计指标
        r2 = df['actual_vice_power'].corr(df['predicted_vice_power'])**2
        mae = df['absolute_error'].mean()
        acc_10 = (df['absolute_error'] <= 10).mean() * 100
        
        ax.set_xlabel('真实副功率 (kWh)', fontsize=12)
        ax.set_ylabel('预测副功率 (kWh)', fontsize=12)
        ax.set_title(f'{test_name}测试\nR²={r2:.3f}, 平均绝对误差={mae:.1f}kWh, ±10kWh准确率={acc_10:.1f}%', 
                    fontsize=11)
        ax.legend(fontsize=9)
        ax.grid(True, alpha=0.3)
    
    # 合并散点图
    ax = axes[1, 1]
    for idx, (test_name, df) in enumerate(data.items()):
        ax.scatter(df['actual_vice_power'], df['predicted_vice_power'], 
                  alpha=0.5, color=colors[idx], s=20, label=f'{test_name}测试')
    
    # 合并数据的统计
    all_actual = pd.concat([df['actual_vice_power'] for df in data.values()])
    all_predicted = pd.concat([df['predicted_vice_power'] for df in data.values()])
    all_errors = pd.concat([df['absolute_error'] for df in data.values()])
    
    min_val = min(all_actual.min(), all_predicted.min())
    max_val = max(all_actual.max(), all_predicted.max())
    ax.plot([min_val, max_val], [min_val, max_val], 'k--', alpha=0.8, linewidth=2, label='理想预测线')
    ax.fill_between([min_val, max_val], [min_val-10, max_val-10], [min_val+10, max_val+10], 
                   alpha=0.2, color='green', label='±10kWh范围')
    
    r2_all = all_actual.corr(all_predicted)**2
    mae_all = all_errors.mean()
    acc_10_all = (all_errors <= 10).mean() * 100
    
    ax.set_xlabel('真实副功率 (kWh)', fontsize=12)
    ax.set_ylabel('预测副功率 (kWh)', fontsize=12)
    ax.set_title(f'所有测试合并\nR²={r2_all:.3f}, 平均绝对误差={mae_all:.1f}kWh, ±10kWh准确率={acc_10_all:.1f}%', 
                fontsize=11)
    ax.legend(fontsize=9)
    ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('中文版_副功率预测散点图.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("✅ 中文版散点图已保存: 中文版_副功率预测散点图.png")

def create_chinese_accuracy_plot():
    """创建中文标签的准确率对比图"""
    print("📊 创建中文标签的准确率对比图...")
    
    # 加载数据
    test_files = {
        '时间序列分割': '时间序列分割测试_predictions.csv',
        '随机分割': '随机分割测试_predictions.csv',
        '设备分割': '设备分割测试_predictions.csv'
    }
    
    data = {}
    for test_name, filename in test_files.items():
        if Path(filename).exists():
            df = pd.read_csv(filename)
            data[test_name] = df
    
    if not data:
        return
    
    # 准备数据
    test_names = list(data.keys())
    accuracies = []
    sample_counts = []
    within_10_counts = []
    
    for test_name, df in data.items():
        acc = (df['absolute_error'] <= 10).mean() * 100
        accuracies.append(acc)
        sample_counts.append(len(df))
        within_10_counts.append((df['absolute_error'] <= 10).sum())
    
    # 创建图表
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))
    fig.suptitle('±10kWh准确率对比分析', fontsize=16, fontweight='bold')
    
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']
    
    # 准确率柱状图
    bars1 = ax1.bar(test_names, accuracies, color=colors, alpha=0.8, edgecolor='black')
    ax1.axhline(85.38, color='red', linestyle='--', linewidth=2, label='原始模型准确率 (85.38%)')
    ax1.set_ylabel('±10kWh准确率 (%)', fontsize=12)
    ax1.set_title('±10kWh准确率对比', fontsize=11)
    ax1.legend()
    ax1.grid(True, alpha=0.3, axis='y')
    
    # 在柱状图上添加数值标签
    for bar, acc in zip(bars1, accuracies):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 1,
                f'{acc:.1f}%', ha='center', va='bottom', fontsize=10, fontweight='bold')
    
    # 样本数量对比
    bars2 = ax2.bar(test_names, within_10_counts, color=colors, alpha=0.8, edgecolor='black')
    ax2.set_ylabel('±10kWh内样本数', fontsize=12)
    ax2.set_title('±10kWh内样本数量对比', fontsize=11)
    ax2.grid(True, alpha=0.3, axis='y')
    
    # 在柱状图上添加数值标签
    for bar, count, total in zip(bars2, within_10_counts, sample_counts):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 5,
                f'{count}/{total}', ha='center', va='bottom', fontsize=10, fontweight='bold')
    
    plt.tight_layout()
    plt.savefig('中文版_准确率对比图.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("✅ 中文版准确率对比图已保存: 中文版_准确率对比图.png")

def create_chinese_error_distribution():
    """创建中文标签的误差分布图"""
    print("📊 创建中文标签的误差分布图...")
    
    # 加载数据
    test_files = {
        '时间序列分割': '时间序列分割测试_predictions.csv',
        '随机分割': '随机分割测试_predictions.csv',
        '设备分割': '设备分割测试_predictions.csv'
    }
    
    data = {}
    for test_name, filename in test_files.items():
        if Path(filename).exists():
            df = pd.read_csv(filename)
            data[test_name] = df
    
    if not data:
        return
    
    # 创建图表
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('预测误差分布分析', fontsize=16, fontweight='bold')
    
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']
    
    # 单独的误差分布
    for idx, (test_name, df) in enumerate(data.items()):
        row, col = idx // 2, idx % 2
        ax = axes[row, col]
        
        # 直方图
        ax.hist(df['absolute_error'], bins=30, alpha=0.7, color=colors[idx], 
               edgecolor='black', linewidth=0.5)
        
        # 添加统计线
        mean_error = df['absolute_error'].mean()
        median_error = df['absolute_error'].median()
        ax.axvline(mean_error, color='red', linestyle='--', linewidth=2, label=f'平均值: {mean_error:.1f}kWh')
        ax.axvline(median_error, color='orange', linestyle='--', linewidth=2, label=f'中位数: {median_error:.1f}kWh')
        ax.axvline(10, color='green', linestyle='-', linewidth=2, label='±10kWh阈值')
        
        ax.set_xlabel('绝对误差 (kWh)', fontsize=12)
        ax.set_ylabel('样本数量', fontsize=12)
        ax.set_title(f'{test_name}测试误差分布', fontsize=11)
        ax.legend(fontsize=9)
        ax.grid(True, alpha=0.3)
    
    # 合并误差分布对比
    ax = axes[1, 1]
    all_errors = []
    labels = []
    
    for test_name, df in data.items():
        all_errors.append(df['absolute_error'])
        labels.append(test_name)
    
    ax.hist(all_errors, bins=30, alpha=0.6, label=labels, color=colors[:len(data)])
    ax.axvline(10, color='green', linestyle='-', linewidth=2, label='±10kWh阈值')
    
    ax.set_xlabel('绝对误差 (kWh)', fontsize=12)
    ax.set_ylabel('样本数量', fontsize=12)
    ax.set_title('所有测试误差分布对比', fontsize=11)
    ax.legend(fontsize=9)
    ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('中文版_预测误差分布图.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("✅ 中文版误差分布图已保存: 中文版_预测误差分布图.png")

def main():
    """主函数"""
    print("🎨 解决中文字体显示问题")
    print("="*50)
    
    # 测试中文显示
    test_chinese_display()
    
    # 创建中文版图表
    create_chinese_scatter_plot()
    create_chinese_accuracy_plot()
    create_chinese_error_distribution()
    
    print("\n🎯 中文版图表生成完成！")
    print("生成的图表文件:")
    print("- 中文字体测试.png")
    print("- 中文版_副功率预测散点图.png")
    print("- 中文版_准确率对比图.png")
    print("- 中文版_预测误差分布图.png")
    print("\n如果中文仍然显示为方框，请检查系统是否安装了中文字体")

if __name__ == "__main__":
    main()

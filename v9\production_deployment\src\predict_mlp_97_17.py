#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
v9系统集成的97.17%准确率神经网络模型预测器
基于lj_env_1环境训练的高性能MLPRegressor模型
"""

import numpy as np
import pandas as pd
import joblib
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

class VicePowerPredictor:
    """v9系统集成的97.17%准确率神经网络副功率预测器"""
    
    def __init__(self, models_dir="models", model_path=None, log_level="INFO"):
        self.models_dir = Path(models_dir)
        self.model = None
        self.scaler = None
        self.selector = None
        self.selected_features = None
        self.log_level = log_level
        
        # 加载97.17%准确率的神经网络模型
        self.load_mlp_97_17_models()
        
        if self.log_level == "INFO":
            print(f"✅ v9-MLP97.17%副功率预测器初始化完成")
            print(f"  模型类型: 神经网络 (MLPRegressor)")
            print(f"  训练环境: lj_env_1 (sklearn 1.0.2)")
            print(f"  准确率: 97.17%")
    
    def load_mlp_97_17_models(self):
        """加载97.17%准确率的神经网络模型"""
        try:
            # 加载神经网络模型
            model_path = self.models_dir / "best_model_mlp_lj_env_1.joblib"
            if model_path.exists():
                self.model = joblib.load(model_path)
                if self.log_level == "INFO":
                    print(f"✅ 神经网络模型加载成功")
            else:
                raise FileNotFoundError(f"神经网络模型文件不存在: {model_path}")
            
            # 加载预处理器
            scaler_path = self.models_dir / "scaler_lj_env_1.joblib"
            if scaler_path.exists():
                self.scaler = joblib.load(scaler_path)
            else:
                raise FileNotFoundError(f"标准化器文件不存在: {scaler_path}")
            
            # 加载特征选择器
            selector_path = self.models_dir / "feature_selector_lj_env_1.joblib"
            if selector_path.exists():
                self.selector = joblib.load(selector_path)
            else:
                raise FileNotFoundError(f"特征选择器文件不存在: {selector_path}")
            
            # 加载结果信息获取特征列表
            results_path = self.models_dir / "lj_env_1_training_report.json"
            if results_path.exists():
                import json
                with open(results_path, 'r', encoding='utf-8') as f:
                    results = json.load(f)
                self.selected_features = results.get('selected_features', [])
                if self.log_level == "INFO":
                    print(f"✅ 特征信息加载成功，共{len(self.selected_features)}个特征")
            
        except Exception as e:
            print(f"❌ 模型加载失败: {e}")
            raise
    
    def create_features(self, df):
        """创建特征工程（与97.17%模型训练时完全一致）"""
        df_features = df.copy()
        
        # 确保必要的基础特征存在
        if 'weight_difference' not in df_features.columns:
            df_features['weight_difference'] = 200.0
        if 'silicon_thermal_energy_kwh' not in df_features.columns:
            df_features['silicon_thermal_energy_kwh'] = 300.0
        if 'duration_hours' not in df_features.columns:
            df_features['duration_hours'] = 48.0
        
        # 工程特征（与lj_env_1训练时一致）
        df_features['power_density'] = df_features.get('main_total_energy_kwh', 2500.0) / df_features['duration_hours']
        df_features['kg_per_hour'] = df_features['weight_difference'] / df_features['duration_hours']
        df_features['main_vice_energy_ratio'] = df_features.get('main_total_energy_kwh', 2500.0) / (df_features.get('total_energy_kwh', 3000.0) + 1e-6)
        
        # 多项式特征
        poly_features = ['weight_difference', 'silicon_thermal_energy_kwh', 'duration_hours']
        for feature in poly_features:
            if feature in df_features.columns:
                df_features[f'{feature}_squared'] = df_features[feature] ** 2
                df_features[f'{feature}_sqrt'] = np.sqrt(np.abs(df_features[feature]))
                df_features[f'{feature}_log'] = np.log1p(np.abs(df_features[feature]))
        
        # 交互特征
        df_features['weight_difference_x_silicon_thermal_energy_kwh'] = df_features['weight_difference'] * df_features['silicon_thermal_energy_kwh']
        df_features['weight_difference_x_duration_hours'] = df_features['weight_difference'] * df_features['duration_hours']
        df_features['silicon_thermal_energy_kwh_x_duration_hours'] = df_features['silicon_thermal_energy_kwh'] * df_features['duration_hours']
        
        # 比率特征
        df_features['weight_difference_div_duration_hours'] = df_features['weight_difference'] / (df_features['duration_hours'] + 1e-8)
        df_features['silicon_thermal_energy_kwh_div_duration_hours'] = df_features['silicon_thermal_energy_kwh'] / (df_features['duration_hours'] + 1e-8)
        
        return df_features
    
    def create_complete_feature_set(self, df):
        """创建完整的特征集（包含所有训练时的特征）"""
        df_features = df.copy()
        
        # 确保所有基础特征存在（使用合理的默认值）
        base_features = {
            'start_weight': 1000.0,
            'end_weight': 1200.0,
            'weight_difference': 200.0,
            'end_temperature_celsius': 1450.0,
            'first_crystal_seeding_main_power_kw': 45.0,
            'feed_number_1_records': 100,
            'main_total_energy_kwh': 2500.0,
            'total_energy_kwh': 3000.0,
            'silicon_thermal_energy_kwh': 2000.0,
            'energy_efficiency_percent': 85.0,
            'record_count': 150,
            'duration_hours': 48.0,
            'device_frequency': 50.0
        }
        
        for feature, default_value in base_features.items():
            if feature not in df_features.columns:
                df_features[feature] = default_value
        
        # 应用特征工程
        df_features = self.create_features(df_features)
        
        return df_features
    
    def predict(self, input_data):
        """预测副功率"""
        try:
            # 转换为DataFrame
            if isinstance(input_data, dict):
                df = pd.DataFrame([input_data])
            elif isinstance(input_data, pd.DataFrame):
                df = input_data.copy()
            else:
                raise ValueError("输入数据必须是字典或DataFrame")
            
            # 创建完整的特征集
            df_features = self.create_complete_feature_set(df)
            
            # 选择训练时使用的特征
            if self.selected_features:
                # 确保所有需要的特征都存在
                missing_features = [f for f in self.selected_features if f not in df_features.columns]
                if missing_features:
                    print(f"⚠️ 缺失特征: {missing_features}")
                    # 为缺失特征填充合理的默认值
                    for feature in missing_features:
                        df_features[feature] = 0.0
                
                X = df_features[self.selected_features]
            else:
                X = df_features
            
            # 标准化
            X_scaled = self.scaler.transform(X)
            
            # 特征选择
            X_selected = self.selector.transform(X_scaled)
            
            # 预测
            prediction = self.model.predict(X_selected)
            
            if self.log_level == "INFO":
                print(f"🎯 神经网络预测完成: {prediction[0]:.2f} kWh")
            
            return prediction[0] if len(prediction) == 1 else prediction
            
        except Exception as e:
            print(f"❌ 预测失败: {e}")
            raise
    
    def get_model_info(self):
        """获取模型信息"""
        return {
            'model_type': 'MLPRegressor',
            'accuracy': '97.17%',
            'training_environment': 'lj_env_1',
            'sklearn_version': '1.0.2',
            'feature_count': len(self.selected_features) if self.selected_features else 'unknown'
        }

def test_mlp_97_17_predictor():
    """测试神经网络 97.17%预测器"""
    try:
        # 初始化预测器
        predictor = VicePowerPredictor(models_dir="../models", log_level="INFO")
        
        # 测试数据
        test_data = {
            'start_weight': 1000.0,
            'end_weight': 1200.0,
            'weight_difference': 200.0,
            'end_temperature_celsius': 1450.0,
            'first_crystal_seeding_main_power_kw': 45.0,
            'feed_number_1_records': 100,
            'main_total_energy_kwh': 2500.0,
            'total_energy_kwh': 3000.0,
            'silicon_thermal_energy_kwh': 2000.0,
            'energy_efficiency_percent': 85.0,
            'record_count': 150,
            'duration_hours': 48.0,
            'power_density': 50.0,
            'kg_per_hour': 4.2,
            'main_vice_energy_ratio': 0.83,
            'device_frequency': 50.0
        }
        
        # 进行预测
        prediction = predictor.predict(test_data)
        print(f"\n🧪 测试预测结果: {prediction:.2f} kWh")
        
        # 显示模型信息
        info = predictor.get_model_info()
        print(f"\n📊 模型信息:")
        for key, value in info.items():
            print(f"  {key}: {value}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 v9系统神经网络 97.17%副功率预测器")
    print("="*60)
    
    # 测试预测器
    success = test_mlp_97_17_predictor()
    
    if success:
        print(f"\n✅ 神经网络 97.17%预测器测试成功！")
        print(f"🎯 准确率: 97.17%")
        print(f"🔧 训练环境: lj_env_1 (sklearn 1.0.2)")
        print(f"💡 已集成到v9系统中")
    else:
        print(f"\n❌ 神经网络 97.17%预测器测试失败")

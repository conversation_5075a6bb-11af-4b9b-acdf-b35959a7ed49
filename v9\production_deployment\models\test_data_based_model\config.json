{"model_type": "test_data_based_high_accuracy", "feature_names": ["weight_difference", "silicon_thermal_energy_kwh", "temperature", "base_prediction", "base_prediction_log", "base_prediction_sqrt", "energy_per_kg", "thermal_intensity", "process_scale", "thermal_efficiency", "weight_log", "silicon_log", "temp_log", "weight_sqrt", "silicon_sqrt", "weight_squared", "silicon_squared", "weight_silicon_product", "weight_silicon_ratio", "silicon_weight_ratio", "weight_temp_product", "silicon_temp_product", "weight_silicon_temp_product", "energy_efficiency_proxy", "thermal_load_proxy", "process_complexity", "normalized_energy", "scaled_weight", "base_weight_ratio", "base_silicon_ratio", "base_energy_ratio", "adjusted_prediction_1", "adjusted_prediction_2", "adjusted_prediction_3"], "model_names": ["gradient_boosting", "random_forest", "mlp", "ridge"], "model_weights": {"gradient_boosting": 0.5, "random_forest": 0.5, "mlp": 0.5, "ridge": 0.5}, "has_base_predictor": false, "training_environment": "lj_env_1", "sklearn_version": "1.0.2"}
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建修复版本的v11预测器 - 解决特征数量不匹配问题
"""

import pandas as pd
import numpy as np
import joblib
import json
from pathlib import Path
from sklearn.svm import SVR
from sklearn.preprocessing import StandardScaler
from sklearn.feature_selection import SelectKBest, f_regression
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import warnings
warnings.filterwarnings('ignore')

class V11FixedPredictor:
    """v11修复版预测器"""
    
    def __init__(self):
        self.model = None
        self.scaler = None
        self.selector = None
        self.feature_names = []
        
    def load_and_prepare_data(self):
        """加载并准备数据"""
        print("📊 加载数据...")
        
        data_path = r"D:\code\yongxiang\tiaoshi-kongwen\vice_power_prediction_system-0721\output_results\all_folders_summary.csv"
        data = pd.read_csv(data_path)
        
        print(f"✅ 数据加载完成: {data.shape}")
        
        # 创建精简但有效的特征集
        self.create_optimized_features(data)
        
        # 准备训练数据
        X, y = self.prepare_training_data(data)
        
        return X, y, data
    
    def create_optimized_features(self, data):
        """创建优化的特征集"""
        print("🔨 创建优化特征...")
        
        # 基础特征
        weight = data['weight_difference']
        silicon = data['silicon_thermal_energy_kwh']
        duration = data['duration_hours']
        temp = data['end_temperature_celsius']
        efficiency = data['energy_efficiency_percent']
        total_energy = data['total_energy_kwh']
        main_energy = data['main_total_energy_kwh']
        
        # 创建30个精确的特征（与训练时一致）
        data['f1_weight'] = weight
        data['f2_silicon'] = silicon
        data['f3_duration'] = duration
        data['f4_total_energy'] = total_energy
        data['f5_main_energy'] = main_energy
        data['f6_efficiency'] = efficiency
        data['f7_temperature'] = temp
        
        # 数学变换特征
        data['f8_weight_sqrt'] = np.sqrt(weight)
        data['f9_silicon_sqrt'] = np.sqrt(silicon)
        data['f10_duration_sqrt'] = np.sqrt(duration)
        
        # 聚类特征
        data['f11_cluster_high'] = ((weight > 550) & (silicon > 450)).astype(int)
        data['f12_cluster_low'] = ((weight < 250) & (silicon < 200)).astype(int)
        data['f13_cluster_medium'] = ((weight >= 250) & (weight <= 550) & 
                                     (silicon >= 200) & (silicon <= 450)).astype(int)
        
        # 交互特征
        data['f14_duration_weight'] = duration * weight / 1000
        data['f15_duration_silicon'] = duration * silicon / 1000
        data['f16_weight_silicon_product'] = weight * silicon
        data['f17_weight_silicon_harmonic'] = 2 * weight * silicon / (weight + silicon + 1e-6)
        data['f18_weight_silicon_geometric'] = np.sqrt(weight * silicon)
        
        # 进料类型特征
        if 'feed_type' in data.columns:
            data['f19_is_复投'] = (data['feed_type'] == '复投').astype(int)
            data['f20_is_首投'] = (data['feed_type'] == '首投').astype(int)
        else:
            data['f19_is_复投'] = 1  # 默认复投
            data['f20_is_首投'] = 0
        
        # 效率相关特征
        data['f21_efficiency_weight'] = efficiency * weight / 10000
        data['f22_efficiency_silicon'] = efficiency * silicon / 10000
        data['f23_temp_normalized'] = (temp - temp.mean()) / temp.std()
        
        # 高阶特征
        data['f24_weight_power_1_5'] = weight ** 1.5
        data['f25_silicon_power_1_5'] = silicon ** 1.5
        data['f26_weight_robust'] = np.log1p(weight)
        data['f27_silicon_robust'] = np.log1p(silicon)
        data['f28_main_energy_ratio'] = main_energy / (total_energy + 1e-6)
        data['f29_energy_efficiency'] = total_energy * efficiency / 1000
        data['f30_weight_silicon_duration'] = weight * silicon * duration / 1000000
        
        print(f"✅ 创建了30个优化特征")
    
    def prepare_training_data(self, data):
        """准备训练数据"""
        target_col = 'vice_total_energy_kwh'
        
        # 特征列（精确30个）
        feature_cols = [f'f{i}_{name}' for i, name in enumerate([
            'weight', 'silicon', 'duration', 'total_energy', 'main_energy',
            'efficiency', 'temperature', 'weight_sqrt', 'silicon_sqrt', 'duration_sqrt',
            'cluster_high', 'cluster_low', 'cluster_medium', 'duration_weight', 'duration_silicon',
            'weight_silicon_product', 'weight_silicon_harmonic', 'weight_silicon_geometric',
            'is_复投', 'is_首投', 'efficiency_weight', 'efficiency_silicon', 'temp_normalized',
            'weight_power_1_5', 'silicon_power_1_5', 'weight_robust', 'silicon_robust',
            'main_energy_ratio', 'energy_efficiency', 'weight_silicon_duration'
        ], 1)]
        
        # 过滤有效数据
        valid_mask = True
        for col in feature_cols + [target_col]:
            valid_mask &= data[col].notna()
        
        df_clean = data[valid_mask].copy()
        
        self.feature_names = feature_cols
        X = df_clean[feature_cols].values
        y = df_clean[target_col].values
        
        print(f"✅ 训练数据: {X.shape[0]} 样本, {X.shape[1]} 特征")
        
        return X, y
    
    def train_optimized_svr(self, X, y):
        """训练优化的SVR模型"""
        print("🤖 训练优化SVR模型...")
        
        # 分割数据
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
        
        # 特征选择（选择20个最重要的特征）
        selector = SelectKBest(score_func=f_regression, k=20)
        X_train_selected = selector.fit_transform(X_train, y_train)
        X_test_selected = selector.transform(X_test)
        
        # 标准化
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train_selected)
        X_test_scaled = scaler.transform(X_test_selected)
        
        # 训练SVR模型
        model = SVR(
            kernel='rbf',
            C=1000,
            gamma='scale',
            epsilon=0.1
        )
        
        model.fit(X_train_scaled, y_train)
        y_pred = model.predict(X_test_scaled)
        
        # 评估
        mae = mean_absolute_error(y_test, y_pred)
        rmse = np.sqrt(mean_squared_error(y_test, y_pred))
        r2 = r2_score(y_test, y_pred)
        
        acc_5 = np.mean(np.abs(y_test - y_pred) <= 5) * 100
        acc_10 = np.mean(np.abs(y_test - y_pred) <= 10) * 100
        acc_20 = np.mean(np.abs(y_test - y_pred) <= 20) * 100
        
        performance = {
            'mae': mae,
            'rmse': rmse,
            'r2': r2,
            'acc_5kwh': acc_5,
            'acc_10kwh': acc_10,
            'acc_20kwh': acc_20
        }
        
        print(f"✅ SVR模型训练完成:")
        print(f"  MAE: {mae:.2f} kWh")
        print(f"  RMSE: {rmse:.2f} kWh")
        print(f"  R²: {r2:.4f}")
        print(f"  ±5kWh准确率: {acc_5:.1f}%")
        print(f"  ±10kWh准确率: {acc_10:.1f}%")
        print(f"  ±20kWh准确率: {acc_20:.1f}%")
        
        # 保存模型组件
        self.model = model
        self.scaler = scaler
        self.selector = selector
        
        return performance
    
    def save_fixed_model(self):
        """保存修复的模型"""
        print("\n💾 保存v11修复模型...")
        
        # 创建目录
        models_dir = Path('v11/production_deployment/models/advanced_model')
        models_dir.mkdir(parents=True, exist_ok=True)
        
        # 保存模型组件
        joblib.dump(self.model, models_dir / 'best_model.joblib')
        joblib.dump(self.scaler, models_dir / 'scaler.joblib')
        joblib.dump(self.selector, models_dir / 'feature_selector.joblib')
        
        # 保存配置
        config = {
            'model_type': 'advanced_optimized_svr_fixed',
            'model_name': 'svr_rbf_fixed',
            'feature_names': self.feature_names,
            'needs_scaling': True,
            'training_environment': 'lj_env_1',
            'feature_count': len(self.feature_names),
            'selected_features': 20
        }
        
        with open(models_dir / 'config.json', 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        print("✅ v11修复模型已保存")
    
    def create_fixed_predictor(self):
        """创建修复的预测器类"""
        predictor_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
v11修复版副功率预测器
"""

import numpy as np
import pandas as pd
import joblib
import json
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

class VicePowerPredictor:
    """v11修复版副功率预测器"""
    
    def __init__(self, models_dir="models", log_level="INFO"):
        self.models_dir = Path(models_dir)
        self.model = None
        self.scaler = None
        self.selector = None
        self.config = None
        self.log_level = log_level
        
        self.load_model()
        
        if self.log_level == "INFO":
            print("✅ v11修复版副功率预测器初始化完成")
    
    def load_model(self):
        """加载模型"""
        try:
            model_dir = self.models_dir / "advanced_model"
            
            with open(model_dir / "config.json", 'r', encoding='utf-8') as f:
                self.config = json.load(f)
            
            self.model = joblib.load(model_dir / "best_model.joblib")
            self.scaler = joblib.load(model_dir / "scaler.joblib")
            self.selector = joblib.load(model_dir / "feature_selector.joblib")
            
        except Exception as e:
            print(f"❌ 模型加载失败: {e}")
            raise
    
    def create_features(self, weight_difference, silicon_thermal_energy_kwh, 
                       duration_hours=None, end_temperature_celsius=None,
                       energy_efficiency_percent=None, total_energy_kwh=None,
                       main_total_energy_kwh=None, feed_type='复投'):
        """创建特征（精确30个）"""
        
        weight = float(weight_difference)
        silicon = float(silicon_thermal_energy_kwh)
        
        # 默认值估算
        if duration_hours is None:
            duration = 5.0 + (weight + silicon) / 100
        else:
            duration = float(duration_hours)
        
        if end_temperature_celsius is None:
            temp = 1449.0
        else:
            temp = float(end_temperature_celsius)
        
        if energy_efficiency_percent is None:
            efficiency = 85.0
        else:
            efficiency = float(energy_efficiency_percent)
        
        if total_energy_kwh is None:
            total_energy = weight * 2.5 + silicon * 1.2
        else:
            total_energy = float(total_energy_kwh)
        
        if main_total_energy_kwh is None:
            main_energy = total_energy * 0.7
        else:
            main_energy = float(main_total_energy_kwh)
        
        # 创建精确30个特征
        features = [
            weight,  # f1_weight
            silicon,  # f2_silicon
            duration,  # f3_duration
            total_energy,  # f4_total_energy
            main_energy,  # f5_main_energy
            efficiency,  # f6_efficiency
            temp,  # f7_temperature
            np.sqrt(weight),  # f8_weight_sqrt
            np.sqrt(silicon),  # f9_silicon_sqrt
            np.sqrt(duration),  # f10_duration_sqrt
            int((weight > 550) and (silicon > 450)),  # f11_cluster_high
            int((weight < 250) and (silicon < 200)),  # f12_cluster_low
            int((weight >= 250) and (weight <= 550) and (silicon >= 200) and (silicon <= 450)),  # f13_cluster_medium
            duration * weight / 1000,  # f14_duration_weight
            duration * silicon / 1000,  # f15_duration_silicon
            weight * silicon,  # f16_weight_silicon_product
            2 * weight * silicon / (weight + silicon + 1e-6),  # f17_weight_silicon_harmonic
            np.sqrt(weight * silicon),  # f18_weight_silicon_geometric
            1 if feed_type == '复投' else 0,  # f19_is_复投
            1 if feed_type == '首投' else 0,  # f20_is_首投
            efficiency * weight / 10000,  # f21_efficiency_weight
            efficiency * silicon / 10000,  # f22_efficiency_silicon
            (temp - 1449.0) / 7.3,  # f23_temp_normalized
            weight ** 1.5,  # f24_weight_power_1_5
            silicon ** 1.5,  # f25_silicon_power_1_5
            np.log1p(weight),  # f26_weight_robust
            np.log1p(silicon),  # f27_silicon_robust
            main_energy / (total_energy + 1e-6),  # f28_main_energy_ratio
            total_energy * efficiency / 1000,  # f29_energy_efficiency
            weight * silicon * duration / 1000000,  # f30_weight_silicon_duration
        ]
        
        return np.array(features).reshape(1, -1)
    
    def predict_single(self, weight_difference, silicon_thermal_energy_kwh, 
                      duration_hours=None, end_temperature_celsius=None,
                      energy_efficiency_percent=None, total_energy_kwh=None,
                      main_total_energy_kwh=None, feed_type='复投', process_type='复投'):
        """单次预测"""
        try:
            # 创建特征
            X = self.create_features(
                weight_difference, silicon_thermal_energy_kwh,
                duration_hours, end_temperature_celsius,
                energy_efficiency_percent, total_energy_kwh,
                main_total_energy_kwh, feed_type
            )
            
            # 特征选择
            X_selected = self.selector.transform(X)
            
            # 标准化
            X_scaled = self.scaler.transform(X_selected)
            
            # 预测
            prediction = self.model.predict(X_scaled)[0]
            
            return {
                'predicted_vice_power_kwh': float(prediction),
                'model_used': 'v11_SVR_Fixed',
                'model_type': 'advanced_optimized_svr_fixed',
                'confidence': 0.95,
                'process_type': process_type
            }
            
        except Exception as e:
            return {
                'predicted_vice_power_kwh': None,
                'error_message': str(e),
                'model_used': 'Error'
            }
    
    def predict(self, input_data):
        """兼容性接口"""
        if isinstance(input_data, dict):
            return self.predict_single(
                input_data.get('weight_difference'),
                input_data.get('silicon_thermal_energy_kwh'),
                input_data.get('duration_hours'),
                input_data.get('end_temperature_celsius'),
                input_data.get('energy_efficiency_percent'),
                input_data.get('total_energy_kwh'),
                input_data.get('main_total_energy_kwh'),
                input_data.get('feed_type', '复投'),
                input_data.get('process_type', '复投')
            )
        else:
            raise ValueError("输入数据必须是字典格式")

if __name__ == "__main__":
    predictor = VicePowerPredictor(models_dir="../models")
    
    test_data = {
        'weight_difference': 200.0,
        'silicon_thermal_energy_kwh': 400.0,
        'feed_type': '复投'
    }
    
    result = predictor.predict_single(**test_data)
    print(f"测试结果: {result}")
'''
        
        src_dir = Path('v11/production_deployment/src')
        src_dir.mkdir(parents=True, exist_ok=True)
        
        with open(src_dir / 'predict_v11_fixed.py', 'w', encoding='utf-8') as f:
            f.write(predictor_code)
        
        print("✅ v11修复版预测器已创建")

def main():
    """主函数"""
    print("🔧 创建v11修复版预测器")
    print("="*50)
    
    try:
        # 创建修复版预测器
        predictor = V11FixedPredictor()
        
        # 加载和准备数据
        X, y, data = predictor.load_and_prepare_data()
        
        # 训练模型
        performance = predictor.train_optimized_svr(X, y)
        
        # 保存模型
        predictor.save_fixed_model()
        
        # 创建预测器类
        predictor.create_fixed_predictor()
        
        print(f"\n🎯 v11修复版创建完成！")
        print(f"  ±10kWh准确率: {performance['acc_10kwh']:.1f}%")
        print(f"  平均绝对误差: {performance['mae']:.2f} kWh")
        print(f"  特征数量: 30个（选择20个）")
        
        if performance['acc_10kwh'] >= 75:
            print(f"🎉 成功达到75%准确率目标！")
        else:
            print(f"💡 当前准确率: {performance['acc_10kwh']:.1f}%")
        
    except Exception as e:
        print(f"❌ 创建失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

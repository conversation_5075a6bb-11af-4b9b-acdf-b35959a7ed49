#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修正的v8和v9模型测试 - 使用正确的特征工程和数据分布
"""

import pandas as pd
import numpy as np
import sys
import os
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# 添加v8和v9路径
sys.path.append('v8/production_deployment/src')
sys.path.append('v9/production_deployment/src')

def load_test_data():
    """加载测试数据"""
    df = pd.read_csv('完整测试数据_含输入特征.csv')
    print(f"✅ 加载测试数据: {df.shape[0]} 条记录")
    return df

def prepare_corrected_input_features(df):
    """准备修正的输入特征 - 基于训练数据的分布"""
    input_features = []
    
    for idx, row in df.iterrows():
        # 使用实际的特征值
        weight_diff = row['weight_difference']
        silicon_energy = row['silicon_thermal_energy_kwh']
        temperature = row['temperature']
        
        # 基于训练数据分布估算缺失的关键特征
        # total_energy_kwh是最重要的特征，需要合理估算
        # 根据训练数据，total_energy_kwh通常比silicon_thermal_energy_kwh高20-50%
        total_energy = silicon_energy * np.random.uniform(1.2, 1.5)
        
        # main_total_energy_kwh通常是total_energy的80-90%
        main_energy = total_energy * np.random.uniform(0.8, 0.9)
        
        # 根据重量差异和硅热能估算合理的时长
        # 基于训练数据的经验关系
        duration = max(20.0, min(150.0, weight_diff / 3.0 + silicon_energy / 15.0))
        
        # 根据重量差异估算起始重量
        start_weight = max(500.0, 1200.0 - weight_diff * 0.3)
        end_weight = start_weight + weight_diff
        
        # 根据硅热能估算主功率
        main_power = max(25.0, min(100.0, silicon_energy / 8.0))
        
        # 创建完整的特征集
        features = {
            # 核心特征
            'start_weight': start_weight,
            'end_weight': end_weight,
            'weight_difference': weight_diff,
            'end_temperature_celsius': temperature,
            'first_crystal_seeding_main_power_kw': main_power,
            'feed_number_1_records': max(50, min(300, int(duration * 1.5))),
            'main_total_energy_kwh': main_energy,
            'total_energy_kwh': total_energy,  # 关键特征！
            'silicon_thermal_energy_kwh': silicon_energy,
            'energy_efficiency_percent': max(70.0, min(95.0, 85.0 + np.random.normal(0, 3))),
            'record_count': max(100, min(500, int(duration * 2.5))),
            'duration_hours': duration,
            'device_frequency': 50.0,  # 标准频率
        }
        input_features.append(features)
    
    return input_features

def test_v9_model_corrected(input_features):
    """测试v9模型（使用修正的特征工程）"""
    print("\n🔧 测试v9模型（修正版）...")
    
    try:
        # 导入v9预测器
        from predict_mlp_97_17 import VicePowerPredictor as V9Predictor
        
        # 初始化预测器
        v9_predictor = V9Predictor(models_dir="v9/production_deployment/models", log_level="ERROR")
        
        predictions = []
        for features in input_features:
            try:
                pred = v9_predictor.predict(features)
                predictions.append(pred)
            except Exception as e:
                print(f"⚠️ v9预测失败: {e}")
                predictions.append(np.nan)
        
        print(f"✅ v9模型预测完成: {len([p for p in predictions if not np.isnan(p)])} 个有效预测")
        return np.array(predictions)
        
    except Exception as e:
        print(f"❌ v9模型测试失败: {e}")
        return None

def calculate_metrics(actual, predicted, model_name):
    """计算评估指标"""
    # 移除NaN值
    mask = ~(np.isnan(actual) | np.isnan(predicted))
    actual_clean = actual[mask]
    predicted_clean = predicted[mask]
    
    if len(actual_clean) == 0:
        return None
    
    # 计算各种指标
    mae = np.mean(np.abs(actual_clean - predicted_clean))
    rmse = np.sqrt(np.mean((actual_clean - predicted_clean) ** 2))
    mape = np.mean(np.abs((actual_clean - predicted_clean) / actual_clean)) * 100
    
    # 不同阈值的准确率
    within_5kwh = np.sum(np.abs(actual_clean - predicted_clean) <= 5) / len(actual_clean) * 100
    within_10kwh = np.sum(np.abs(actual_clean - predicted_clean) <= 10) / len(actual_clean) * 100
    within_15kwh = np.sum(np.abs(actual_clean - predicted_clean) <= 15) / len(actual_clean) * 100
    within_20kwh = np.sum(np.abs(actual_clean - predicted_clean) <= 20) / len(actual_clean) * 100
    within_30kwh = np.sum(np.abs(actual_clean - predicted_clean) <= 30) / len(actual_clean) * 100
    
    # R²决定系数
    ss_res = np.sum((actual_clean - predicted_clean) ** 2)
    ss_tot = np.sum((actual_clean - np.mean(actual_clean)) ** 2)
    r2 = 1 - (ss_res / ss_tot) if ss_tot != 0 else 0
    
    metrics = {
        'model_name': model_name,
        'sample_count': len(actual_clean),
        'mae': mae,
        'rmse': rmse,
        'mape': mape,
        'within_5kwh': within_5kwh,
        'within_10kwh': within_10kwh,
        'within_15kwh': within_15kwh,
        'within_20kwh': within_20kwh,
        'within_30kwh': within_30kwh,
        'r2': r2
    }
    
    return metrics

def print_metrics(metrics):
    """打印评估指标"""
    if metrics is None:
        print("❌ 无法计算指标")
        return
    
    print(f"\n📊 {metrics['model_name']} 性能指标:")
    print(f"  样本数量: {metrics['sample_count']}")
    print(f"  平均绝对误差 (MAE): {metrics['mae']:.2f} kWh")
    print(f"  均方根误差 (RMSE): {metrics['rmse']:.2f} kWh")
    print(f"  平均绝对百分比误差 (MAPE): {metrics['mape']:.2f}%")
    print(f"  ±5kWh准确率: {metrics['within_5kwh']:.2f}%")
    print(f"  ±10kWh准确率: {metrics['within_10kwh']:.2f}%")
    print(f"  ±15kWh准确率: {metrics['within_15kwh']:.2f}%")
    print(f"  ±20kWh准确率: {metrics['within_20kwh']:.2f}%")
    print(f"  ±30kWh准确率: {metrics['within_30kwh']:.2f}%")
    print(f"  决定系数 (R²): {metrics['r2']:.4f}")

def analyze_prediction_quality(actual, predicted, test_df):
    """分析预测质量"""
    print(f"\n🔍 预测质量分析:")
    
    # 预测值分布
    print(f"预测值分布:")
    print(f"  范围: {predicted.min():.1f} - {predicted.max():.1f} kWh")
    print(f"  平均: {predicted.mean():.1f} kWh")
    print(f"  标准差: {predicted.std():.1f} kWh")
    print(f"  唯一值数量: {len(np.unique(predicted))}")
    
    # 与实际值的相关性
    correlation = np.corrcoef(actual, predicted)[0, 1]
    print(f"  与实际值相关性: {correlation:.4f}")
    
    # 按工艺类型分析
    print(f"\n按工艺类型分析:")
    for process_type in test_df['process_type'].unique():
        mask = test_df['process_type'] == process_type
        if mask.sum() > 0:
            subset_actual = actual[mask]
            subset_pred = predicted[mask]
            subset_mae = np.mean(np.abs(subset_actual - subset_pred))
            subset_acc = np.mean(np.abs(subset_actual - subset_pred) <= 10) * 100
            print(f"  {process_type}: MAE={subset_mae:.1f}kWh, ±10kWh准确率={subset_acc:.1f}%, 样本数={mask.sum()}")

def main():
    """主函数"""
    print("🚀 修正的v8和v9模型准确性测试")
    print("="*60)
    
    # 1. 加载测试数据
    df = load_test_data()
    actual_values = df['actual_vice_power'].values
    
    # 2. 准备修正的输入特征
    print("\n准备修正的输入特征（基于训练数据分布）...")
    input_features = prepare_corrected_input_features(df)
    
    # 3. 测试v9模型
    v9_predictions = test_v9_model_corrected(input_features)
    
    if v9_predictions is not None:
        # 4. 计算评估指标
        v9_metrics = calculate_metrics(actual_values, v9_predictions, "v9 (MLP 97.17% - 修正版)")
        
        # 5. 打印结果
        print_metrics(v9_metrics)
        
        # 6. 分析预测质量
        analyze_prediction_quality(actual_values, v9_predictions, df)
        
        # 7. 保存结果
        results_df = df.copy()
        results_df['v9_corrected_prediction'] = v9_predictions
        results_df['v9_corrected_error'] = np.abs(actual_values - v9_predictions)
        
        results_df.to_csv('v9_corrected_test_results.csv', index=False)
        print(f"\n💾 修正测试结果已保存到: v9_corrected_test_results.csv")
        
        # 8. 总结
        if v9_metrics and v9_metrics['within_10kwh'] >= 75:
            print(f"\n🎉 成功！v9模型±10kWh准确率达到 {v9_metrics['within_10kwh']:.1f}%，满足75%的要求")
        elif v9_metrics and v9_metrics['within_10kwh'] >= 50:
            print(f"\n✅ 良好！v9模型±10kWh准确率为 {v9_metrics['within_10kwh']:.1f}%，接近目标")
        else:
            print(f"\n⚠️ 需要改进！v9模型±10kWh准确率为 {v9_metrics['within_10kwh']:.1f}%，未达到75%目标")
            print(f"   建议：1. 进一步优化特征工程  2. 使用更多训练数据  3. 调整模型参数")

if __name__ == "__main__":
    main()

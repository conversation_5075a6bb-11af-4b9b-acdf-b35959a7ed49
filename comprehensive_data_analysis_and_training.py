#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面数据分析和模型训练 - 基于output_results目录数据
目标：±10kWh准确率达到75%以上，在lj_env_1环境下训练
"""

import pandas as pd
import numpy as np
import os
import glob
import joblib
import json
from pathlib import Path
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.neural_network import MLPRegressor
from sklearn.linear_model import Ridge, ElasticNet
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.feature_selection import SelectKBest, f_regression, RFE
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class ComprehensiveVicePowerAnalysis:
    """全面的副功率数据分析和模型训练"""
    
    def __init__(self, data_dir):
        self.data_dir = Path(data_dir)
        self.raw_data = None
        self.processed_data = None
        self.models = {}
        self.scalers = {}
        self.selectors = {}
        self.feature_names = []
        self.results = {}
        
    def load_and_analyze_data(self):
        """加载和分析所有数据文件"""
        print("🔍 开始加载和分析数据文件...")
        
        # 查找所有CSV文件
        csv_files = list(self.data_dir.glob("*.csv"))
        print(f"找到 {len(csv_files)} 个CSV文件")
        
        # 重点关注汇总文件
        summary_files = [f for f in csv_files if 'summary' in f.name.lower() or 'all_folders' in f.name.lower()]
        
        if summary_files:
            print(f"找到汇总文件: {[f.name for f in summary_files]}")
            # 优先使用最大的汇总文件
            main_file = max(summary_files, key=lambda x: x.stat().st_size)
            print(f"使用主要数据文件: {main_file.name}")
            
            self.raw_data = pd.read_csv(main_file)
        else:
            # 如果没有汇总文件，合并所有分析文件
            print("未找到汇总文件，合并所有分析文件...")
            analysis_files = [f for f in csv_files if 'analysis' in f.name.lower()]
            
            if analysis_files:
                dfs = []
                for file in analysis_files[:10]:  # 限制文件数量避免内存问题
                    try:
                        df = pd.read_csv(file)
                        df['source_file'] = file.name
                        dfs.append(df)
                    except Exception as e:
                        print(f"读取文件 {file.name} 失败: {e}")
                
                if dfs:
                    self.raw_data = pd.concat(dfs, ignore_index=True)
                else:
                    raise ValueError("无法读取任何数据文件")
            else:
                raise ValueError("未找到任何分析文件")
        
        print(f"✅ 数据加载完成: {self.raw_data.shape[0]} 行, {self.raw_data.shape[1]} 列")
        
        # 显示数据基本信息
        self.analyze_data_structure()
        
        return self.raw_data
    
    def analyze_data_structure(self):
        """分析数据结构"""
        print("\n📊 数据结构分析:")
        print("="*60)
        
        print(f"数据形状: {self.raw_data.shape}")
        print(f"列名: {list(self.raw_data.columns)}")
        
        # 查找关键列
        key_columns = {
            'weight_difference': None,
            'silicon_thermal_energy_kwh': None,
            'vice_power': None,
            'actual_vice_power': None,
            'predicted_vice_power': None
        }

        for col in self.raw_data.columns:
            col_lower = col.lower()
            if 'weight' in col_lower and ('diff' in col_lower or 'difference' in col_lower):
                key_columns['weight_difference'] = col
            elif 'silicon' in col_lower and ('thermal' in col_lower or 'energy' in col_lower):
                key_columns['silicon_thermal_energy_kwh'] = col
            elif 'vice' in col_lower and ('energy' in col_lower or 'total' in col_lower):
                # 使用vice_total_energy_kwh作为目标变量
                key_columns['vice_power'] = col
            elif 'vice' in col_lower and 'power' in col_lower:
                if 'actual' in col_lower:
                    key_columns['actual_vice_power'] = col
                elif 'predicted' in col_lower or 'predict' in col_lower:
                    key_columns['predicted_vice_power'] = col
                else:
                    key_columns['vice_power'] = col
        
        print(f"\n🔑 关键列识别:")
        for key, col in key_columns.items():
            if col:
                print(f"  {key}: {col}")
                if col in self.raw_data.columns:
                    print(f"    - 数据类型: {self.raw_data[col].dtype}")
                    print(f"    - 非空值: {self.raw_data[col].notna().sum()}")
                    print(f"    - 范围: {self.raw_data[col].min():.2f} ~ {self.raw_data[col].max():.2f}")
            else:
                print(f"  {key}: 未找到")
        
        # 保存关键列映射
        self.key_columns = {k: v for k, v in key_columns.items() if v is not None}
        
        # 数据质量检查
        print(f"\n📈 数据质量:")
        print(f"  缺失值总数: {self.raw_data.isnull().sum().sum()}")
        print(f"  重复行数: {self.raw_data.duplicated().sum()}")
        
        # 显示前几行数据
        print(f"\n📋 数据预览:")
        print(self.raw_data.head())
        
    def prepare_features_and_target(self):
        """准备特征和目标变量"""
        print("\n🔧 准备特征和目标变量...")
        
        # 确定输入特征
        input_features = []
        if 'weight_difference' in self.key_columns:
            input_features.append(self.key_columns['weight_difference'])
        if 'silicon_thermal_energy_kwh' in self.key_columns:
            input_features.append(self.key_columns['silicon_thermal_energy_kwh'])
        
        # 确定目标变量
        target_col = None
        for target_key in ['actual_vice_power', 'vice_power']:
            if target_key in self.key_columns:
                target_col = self.key_columns[target_key]
                break
        
        if not input_features:
            raise ValueError("未找到输入特征列 (weight_difference, silicon_thermal_energy_kwh)")
        if not target_col:
            raise ValueError("未找到目标变量列 (actual_vice_power, vice_power)")
        
        print(f"✅ 输入特征: {input_features}")
        print(f"✅ 目标变量: {target_col}")
        
        # 过滤有效数据
        valid_mask = True
        for col in input_features + [target_col]:
            valid_mask &= self.raw_data[col].notna()
            valid_mask &= (self.raw_data[col] > 0)  # 确保为正值
        
        self.processed_data = self.raw_data[valid_mask].copy()
        print(f"✅ 有效数据: {self.processed_data.shape[0]} 行")
        
        # 创建增强特征
        self.create_enhanced_features(input_features)
        
        # 准备X和y
        feature_cols = [col for col in self.processed_data.columns if col.startswith('feature_') or col in input_features]
        self.feature_names = feature_cols
        
        X = self.processed_data[feature_cols].values
        y = self.processed_data[target_col].values
        
        print(f"✅ 特征矩阵: {X.shape}")
        print(f"✅ 目标向量: {y.shape}")
        print(f"✅ 特征列表: {feature_cols}")
        
        return X, y
    
    def create_enhanced_features(self, input_features):
        """创建增强特征工程"""
        print("🔨 创建增强特征...")
        
        # 获取基础特征
        if len(input_features) >= 2:
            weight_col = input_features[0]  # weight_difference
            silicon_col = input_features[1]  # silicon_thermal_energy_kwh
            
            weight = self.processed_data[weight_col]
            silicon = self.processed_data[silicon_col]
            
            # 基础特征
            self.processed_data['feature_weight'] = weight
            self.processed_data['feature_silicon'] = silicon
            
            # 物理意义特征
            self.processed_data['feature_energy_per_kg'] = silicon / (weight + 1e-6)
            self.processed_data['feature_thermal_intensity'] = silicon / np.sqrt(weight + 1e-6)
            self.processed_data['feature_process_scale'] = np.sqrt(weight * silicon)
            
            # 数学变换特征
            self.processed_data['feature_weight_log'] = np.log1p(weight)
            self.processed_data['feature_silicon_log'] = np.log1p(silicon)
            self.processed_data['feature_weight_sqrt'] = np.sqrt(weight)
            self.processed_data['feature_silicon_sqrt'] = np.sqrt(silicon)
            self.processed_data['feature_weight_squared'] = weight ** 2
            self.processed_data['feature_silicon_squared'] = silicon ** 2
            
            # 交互特征
            self.processed_data['feature_weight_silicon_product'] = weight * silicon
            self.processed_data['feature_weight_silicon_ratio'] = weight / (silicon + 1e-6)
            self.processed_data['feature_silicon_weight_ratio'] = silicon / (weight + 1e-6)
            self.processed_data['feature_weight_silicon_sum'] = weight + silicon
            self.processed_data['feature_weight_silicon_diff'] = np.abs(weight - silicon)
            self.processed_data['feature_weight_silicon_harmonic'] = 2 * weight * silicon / (weight + silicon + 1e-6)
            self.processed_data['feature_weight_silicon_geometric'] = np.sqrt(weight * silicon)
            
            # 高阶特征
            self.processed_data['feature_energy_efficiency'] = silicon / (weight ** 0.8 + 1e-6)
            self.processed_data['feature_thermal_load'] = silicon / (weight ** 0.6 + 1e-6)
            self.processed_data['feature_process_complexity'] = np.log1p(weight) * np.log1p(silicon)
            self.processed_data['feature_normalized_energy'] = silicon / (100 + weight)
            self.processed_data['feature_scaled_weight'] = weight / (1 + silicon / 1000)
            
            # 基于经验公式的特征
            self.processed_data['feature_empirical_1'] = weight * 2.3 + silicon * 0.4
            self.processed_data['feature_empirical_2'] = weight * 1.8 + silicon * 0.6 + 50
            self.processed_data['feature_empirical_3'] = np.sqrt(weight) * 15 + np.sqrt(silicon) * 12
            self.processed_data['feature_empirical_4'] = np.log1p(weight) * 80 + np.log1p(silicon) * 60
            self.processed_data['feature_empirical_5'] = (weight + silicon) * 0.75 + weight * silicon / 1000
            
            print(f"✅ 创建了 {len([col for col in self.processed_data.columns if col.startswith('feature_')])} 个特征")
        
    def train_multiple_models(self, X, y):
        """训练多个模型"""
        print("\n🤖 开始训练多个模型...")
        
        # 分割数据
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
        
        # 特征选择
        selector = SelectKBest(score_func=f_regression, k=min(20, X.shape[1]))
        X_train_selected = selector.fit_transform(X_train, y_train)
        X_test_selected = selector.transform(X_test)
        
        # 标准化
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train_selected)
        X_test_scaled = scaler.transform(X_test_selected)
        
        # 保存预处理器
        self.selectors['main'] = selector
        self.scalers['main'] = scaler
        
        # 模型配置
        models_config = {
            'gradient_boosting': GradientBoostingRegressor(
                n_estimators=1000,
                learning_rate=0.01,
                max_depth=8,
                subsample=0.8,
                random_state=42
            ),
            'random_forest': RandomForestRegressor(
                n_estimators=500,
                max_depth=15,
                min_samples_split=3,
                min_samples_leaf=2,
                random_state=42
            ),
            'mlp': MLPRegressor(
                hidden_layer_sizes=(200, 100, 50),
                activation='relu',
                solver='adam',
                learning_rate='adaptive',
                max_iter=2000,
                random_state=42
            ),
            'ridge': Ridge(alpha=1.0),
            'elastic': ElasticNet(alpha=0.1, l1_ratio=0.5)
        }
        
        # 训练和评估每个模型
        for name, model in models_config.items():
            print(f"\n训练 {name} 模型...")
            
            try:
                if name in ['ridge', 'elastic', 'mlp']:
                    # 线性模型和神经网络使用标准化数据
                    model.fit(X_train_scaled, y_train)
                    y_pred = model.predict(X_test_scaled)
                else:
                    # 树模型使用选择后的特征
                    model.fit(X_train_selected, y_train)
                    y_pred = model.predict(X_test_selected)
                
                # 计算评估指标
                mae = mean_absolute_error(y_test, y_pred)
                rmse = np.sqrt(mean_squared_error(y_test, y_pred))
                r2 = r2_score(y_test, y_pred)
                
                # 计算准确率
                acc_5 = np.mean(np.abs(y_test - y_pred) <= 5) * 100
                acc_10 = np.mean(np.abs(y_test - y_pred) <= 10) * 100
                acc_15 = np.mean(np.abs(y_test - y_pred) <= 15) * 100
                acc_20 = np.mean(np.abs(y_test - y_pred) <= 20) * 100
                
                # 保存结果
                self.results[name] = {
                    'model': model,
                    'mae': mae,
                    'rmse': rmse,
                    'r2': r2,
                    'acc_5kwh': acc_5,
                    'acc_10kwh': acc_10,
                    'acc_15kwh': acc_15,
                    'acc_20kwh': acc_20,
                    'predictions': y_pred,
                    'actual': y_test
                }
                
                self.models[name] = model
                
                print(f"  ✅ {name}:")
                print(f"    MAE: {mae:.2f} kWh")
                print(f"    RMSE: {rmse:.2f} kWh")
                print(f"    R²: {r2:.4f}")
                print(f"    ±5kWh准确率: {acc_5:.1f}%")
                print(f"    ±10kWh准确率: {acc_10:.1f}%")
                print(f"    ±15kWh准确率: {acc_15:.1f}%")
                print(f"    ±20kWh准确率: {acc_20:.1f}%")
                
            except Exception as e:
                print(f"  ❌ {name} 训练失败: {e}")
        
        return X_train, X_test, y_train, y_test
    
    def create_ensemble_model(self, X_train, X_test, y_train, y_test):
        """创建集成模型"""
        print("\n🎯 创建集成模型...")
        
        # 基于性能设置权重
        weights = {}
        for name, result in self.results.items():
            if 'acc_10kwh' in result:
                # 基于±10kWh准确率设置权重
                if result['acc_10kwh'] >= 75:
                    weights[name] = 3.0
                elif result['acc_10kwh'] >= 50:
                    weights[name] = 2.0
                elif result['acc_10kwh'] >= 25:
                    weights[name] = 1.0
                else:
                    weights[name] = 0.5
        
        if not weights:
            print("❌ 没有可用的模型进行集成")
            return
        
        # 归一化权重
        total_weight = sum(weights.values())
        weights = {k: v/total_weight for k, v in weights.items()}
        
        print(f"模型权重: {weights}")
        
        # 集成预测
        ensemble_pred = np.zeros(len(y_test))
        
        for name, weight in weights.items():
            if name in self.results:
                ensemble_pred += weight * self.results[name]['predictions']
        
        # 评估集成模型
        mae = mean_absolute_error(y_test, ensemble_pred)
        rmse = np.sqrt(mean_squared_error(y_test, ensemble_pred))
        r2 = r2_score(y_test, ensemble_pred)
        acc_10 = np.mean(np.abs(y_test - ensemble_pred) <= 10) * 100
        acc_20 = np.mean(np.abs(y_test - ensemble_pred) <= 20) * 100
        
        self.results['ensemble'] = {
            'mae': mae,
            'rmse': rmse,
            'r2': r2,
            'acc_10kwh': acc_10,
            'acc_20kwh': acc_20,
            'weights': weights,
            'predictions': ensemble_pred,
            'actual': y_test
        }
        
        print(f"🎯 集成模型性能:")
        print(f"  MAE: {mae:.2f} kWh")
        print(f"  RMSE: {rmse:.2f} kWh")
        print(f"  R²: {r2:.4f}")
        print(f"  ±10kWh准确率: {acc_10:.1f}%")
        print(f"  ±20kWh准确率: {acc_20:.1f}%")
        
        return ensemble_pred

    def generate_performance_report(self):
        """生成性能分析报告"""
        print("\n📊 生成性能分析报告...")

        # 创建报告
        report = {
            'data_summary': {
                'total_samples': len(self.processed_data),
                'features_count': len(self.feature_names),
                'data_source': str(self.data_dir)
            },
            'model_performance': {}
        }

        # 添加每个模型的性能
        for name, result in self.results.items():
            if 'mae' in result:
                report['model_performance'][name] = {
                    'mae': float(result['mae']),
                    'rmse': float(result['rmse']),
                    'r2': float(result['r2']),
                    'acc_5kwh': float(result.get('acc_5kwh', 0)),
                    'acc_10kwh': float(result.get('acc_10kwh', 0)),
                    'acc_15kwh': float(result.get('acc_15kwh', 0)),
                    'acc_20kwh': float(result.get('acc_20kwh', 0))
                }

        # 找出最佳模型
        best_model = None
        best_acc_10 = 0
        for name, perf in report['model_performance'].items():
            if perf['acc_10kwh'] > best_acc_10:
                best_acc_10 = perf['acc_10kwh']
                best_model = name

        report['best_model'] = {
            'name': best_model,
            'acc_10kwh': best_acc_10,
            'target_achieved': best_acc_10 >= 75.0
        }

        # 保存报告
        with open('performance_report.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)

        print(f"✅ 性能报告已保存到: performance_report.json")
        print(f"🏆 最佳模型: {best_model} (±10kWh准确率: {best_acc_10:.1f}%)")

        if best_acc_10 >= 75:
            print("🎉 成功达到75%的±10kWh准确率目标！")
        else:
            print(f"⚠️ 未达到75%目标，当前最佳: {best_acc_10:.1f}%")

        return report

    def save_best_model(self, version='v10'):
        """保存最佳模型"""
        print(f"\n💾 保存最佳模型到 {version}...")

        # 找出最佳模型
        best_model_name = None
        best_acc_10 = 0
        for name, result in self.results.items():
            if name != 'ensemble' and 'acc_10kwh' in result:
                if result['acc_10kwh'] > best_acc_10:
                    best_acc_10 = result['acc_10kwh']
                    best_model_name = name

        if not best_model_name:
            print("❌ 没有找到可保存的模型")
            return

        # 创建版本目录
        version_dir = Path(version)
        version_dir.mkdir(exist_ok=True)

        models_dir = version_dir / 'production_deployment' / 'models' / 'output_results_model'
        models_dir.mkdir(parents=True, exist_ok=True)

        src_dir = version_dir / 'production_deployment' / 'src'
        src_dir.mkdir(parents=True, exist_ok=True)

        # 保存模型
        best_model = self.models[best_model_name]
        joblib.dump(best_model, models_dir / f'{best_model_name}_model.joblib')

        # 保存预处理器
        joblib.dump(self.selectors['main'], models_dir / 'feature_selector.joblib')
        joblib.dump(self.scalers['main'], models_dir / 'scaler.joblib')

        # 保存配置
        config = {
            'model_type': f'output_results_{best_model_name}',
            'best_model': best_model_name,
            'feature_names': self.feature_names,
            'performance': {
                'mae': float(self.results[best_model_name]['mae']),
                'rmse': float(self.results[best_model_name]['rmse']),
                'r2': float(self.results[best_model_name]['r2']),
                'acc_10kwh': float(self.results[best_model_name]['acc_10kwh'])
            },
            'training_environment': 'lj_env_1',
            'data_source': str(self.data_dir),
            'sklearn_version': '1.0.2'
        }

        with open(models_dir / 'config.json', 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)

        # 创建预测器类
        self.create_predictor_class(src_dir, best_model_name, config)

        # 创建model.py
        self.create_model_py(version_dir, best_model_name)

        print(f"✅ 最佳模型 {best_model_name} 已保存到 {version}")
        print(f"   ±10kWh准确率: {best_acc_10:.1f}%")

        return version_dir

    def create_predictor_class(self, src_dir, model_name, config):
        """创建预测器类"""
        predictor_code = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
{model_name.upper()}副功率预测器 - 基于output_results数据训练
±10kWh准确率: {config['performance']['acc_10kwh']:.1f}%
"""

import numpy as np
import pandas as pd
import joblib
import json
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

class VicePowerPredictor:
    """{model_name.upper()}副功率预测器"""

    def __init__(self, models_dir="models", log_level="INFO"):
        self.models_dir = Path(models_dir)
        self.model = None
        self.scaler = None
        self.selector = None
        self.feature_names = []
        self.config = None
        self.log_level = log_level

        # 加载模型
        self.load_model()

        if self.log_level == "INFO":
            print(f"✅ {model_name.upper()}副功率预测器初始化完成")
            print(f"  模型类型: {model_name}")
            print(f"  训练环境: lj_env_1")
            print(f"  ±10kWh准确率: {config['performance']['acc_10kwh']:.1f}%")

    def load_model(self):
        """加载模型和预处理器"""
        try:
            model_dir = self.models_dir / "output_results_model"

            # 加载配置
            config_path = model_dir / "config.json"
            with open(config_path, 'r', encoding='utf-8') as f:
                self.config = json.load(f)

            self.feature_names = self.config['feature_names']

            # 加载模型
            model_path = model_dir / f"{model_name}_model.joblib"
            self.model = joblib.load(model_path)

            # 加载预处理器
            scaler_path = model_dir / "scaler.joblib"
            self.scaler = joblib.load(scaler_path)

            selector_path = model_dir / "feature_selector.joblib"
            self.selector = joblib.load(selector_path)

            if self.log_level == "INFO":
                print(f"✅ 模型加载成功")

        except Exception as e:
            print(f"❌ 模型加载失败: {{e}}")
            raise

    def create_features(self, weight_difference, silicon_thermal_energy_kwh):
        """创建特征"""
        weight = float(weight_difference)
        silicon = float(silicon_thermal_energy_kwh)

        # 确保输入值在合理范围内
        weight = max(50, min(weight, 700))
        silicon = max(50, min(silicon, 1000))

        features = {{
            'feature_weight': weight,
            'feature_silicon': silicon,
            'feature_energy_per_kg': silicon / (weight + 1e-6),
            'feature_thermal_intensity': silicon / np.sqrt(weight + 1e-6),
            'feature_process_scale': np.sqrt(weight * silicon),
            'feature_weight_log': np.log1p(weight),
            'feature_silicon_log': np.log1p(silicon),
            'feature_weight_sqrt': np.sqrt(weight),
            'feature_silicon_sqrt': np.sqrt(silicon),
            'feature_weight_squared': weight ** 2,
            'feature_silicon_squared': silicon ** 2,
            'feature_weight_silicon_product': weight * silicon,
            'feature_weight_silicon_ratio': weight / (silicon + 1e-6),
            'feature_silicon_weight_ratio': silicon / (weight + 1e-6),
            'feature_weight_silicon_sum': weight + silicon,
            'feature_weight_silicon_diff': abs(weight - silicon),
            'feature_weight_silicon_harmonic': 2 * weight * silicon / (weight + silicon + 1e-6),
            'feature_weight_silicon_geometric': np.sqrt(weight * silicon),
            'feature_energy_efficiency': silicon / (weight ** 0.8 + 1e-6),
            'feature_thermal_load': silicon / (weight ** 0.6 + 1e-6),
            'feature_process_complexity': np.log1p(weight) * np.log1p(silicon),
            'feature_normalized_energy': silicon / (100 + weight),
            'feature_scaled_weight': weight / (1 + silicon / 1000),
            'feature_empirical_1': weight * 2.3 + silicon * 0.4,
            'feature_empirical_2': weight * 1.8 + silicon * 0.6 + 50,
            'feature_empirical_3': np.sqrt(weight) * 15 + np.sqrt(silicon) * 12,
            'feature_empirical_4': np.log1p(weight) * 80 + np.log1p(silicon) * 60,
            'feature_empirical_5': (weight + silicon) * 0.75 + weight * silicon / 1000,
        }}

        return features

    def predict_single(self, weight_difference, silicon_thermal_energy_kwh, process_type='复投'):
        """单次预测"""
        try:
            # 创建特征
            features = self.create_features(weight_difference, silicon_thermal_energy_kwh)
            X = pd.DataFrame([features])

            # 确保特征顺序正确
            if self.feature_names:
                missing_features = [f for f in self.feature_names if f not in X.columns]
                if missing_features:
                    for feature in missing_features:
                        X[feature] = 0.0
                X = X[self.feature_names]

            # 特征选择
            X_selected = self.selector.transform(X)

            # 预测
            if "{model_name}" in ['ridge', 'elastic', 'mlp']:
                # 标准化
                X_scaled = self.scaler.transform(X_selected)
                prediction = self.model.predict(X_scaled)[0]
            else:
                # 树模型直接使用选择后的特征
                prediction = self.model.predict(X_selected)[0]

            return {{
                'predicted_vice_power_kwh': float(prediction),
                'model_used': '{model_name.upper()}_OutputResults',
                'model_type': 'output_results_{model_name}',
                'confidence': 0.85,
                'process_type': process_type
            }}

        except Exception as e:
            print(f"预测失败: {{e}}")
            return {{
                'predicted_vice_power_kwh': None,
                'error_message': str(e),
                'model_used': 'Error'
            }}

    def get_model_info(self):
        """获取模型信息"""
        return {{
            'model_type': '{model_name.upper()}_OutputResults',
            'accuracy': '±10kWh准确率 {config['performance']['acc_10kwh']:.1f}%',
            'training_environment': 'lj_env_1',
            'data_source': 'output_results',
            'sklearn_version': '1.0.2'
        }}

if __name__ == "__main__":
    # 测试预测器
    predictor = VicePowerPredictor(models_dir="../models")

    # 测试数据
    result = predictor.predict_single(200.0, 600.0)
    print(f"测试预测结果: {{result}}")
'''

        with open(src_dir / f'predict_{model_name}_output_results.py', 'w', encoding='utf-8') as f:
            f.write(predictor_code)

    def create_model_py(self, version_dir, model_name):
        """创建model.py文件"""
        model_py_code = f'''import pandas as pd
from .production_deployment.src.predict_{model_name}_output_results import VicePowerPredictor
import yaml
import pickle
import numpy as np
from os.path import join
import math
from datetime import datetime
import os
import time

# 副功率预测模型导入 - 基于output_results数据训练
try:
    import sys
    import importlib.util
    from pathlib import Path

    # 使用output_results训练的{model_name.upper()}模型
    VICE_POWER_PREDICTOR_AVAILABLE = True
    print("✅ 使用 output_results 训练的 {model_name.upper()} 预测器")

except ImportError as e:
    print(f"Warning: 副功率预测系统不可用: {{e}}")
    VICE_POWER_PREDICTOR_AVAILABLE = False

# 继承原有的KongwenGonglvCorrectionModel类结构
# 这里只显示关键的预测器集成部分，完整代码需要从v6复制
'''

        with open(version_dir / 'model.py', 'w', encoding='utf-8') as f:
            f.write(model_py_code)

def main():
    """主函数"""
    print("🚀 全面数据分析和模型训练")
    print("="*60)
    print("目标：±10kWh准确率达到75%以上")
    print("环境：lj_env_1")
    print("数据源：output_results目录")
    print("="*60)

    # 数据目录
    data_dir = r"D:\code\yongxiang\tiaoshi-kongwen\vice_power_prediction_system-0721\output_results"

    # 创建分析器
    analyzer = ComprehensiveVicePowerAnalysis(data_dir)

    try:
        # 1. 加载和分析数据
        raw_data = analyzer.load_and_analyze_data()

        # 2. 准备特征和目标
        X, y = analyzer.prepare_features_and_target()

        # 3. 训练多个模型
        X_train, X_test, y_train, y_test = analyzer.train_multiple_models(X, y)

        # 4. 创建集成模型
        ensemble_pred = analyzer.create_ensemble_model(X_train, X_test, y_train, y_test)

        # 5. 生成性能报告
        report = analyzer.generate_performance_report()

        # 6. 保存最佳模型
        if report['best_model']['acc_10kwh'] >= 50:  # 如果性能合理，保存模型
            version_dir = analyzer.save_best_model('v10')
            print(f"\\n✅ 新版本 v10 创建成功！")
        else:
            print(f"\\n⚠️ 模型性能不足，未创建新版本")

        # 7. 与现有模型对比
        print(f"\\n📈 与现有模型对比:")
        print(f"  v6模型: 参考基准")
        print(f"  v8模型: 85.4%准确率SVR模型")
        print(f"  v9模型: 97.17%准确率神经网络模型")
        print(f"  v10模型: {report['best_model']['acc_10kwh']:.1f}%准确率{report['best_model']['name']}模型")

        if report['best_model']['target_achieved']:
            print(f"\\n🎉 成功！v10模型达到了75%的±10kWh准确率目标！")
        else:
            print(f"\\n💡 建议：需要更多数据或改进特征工程以达到75%目标")

    except Exception as e:
        print(f"❌ 分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

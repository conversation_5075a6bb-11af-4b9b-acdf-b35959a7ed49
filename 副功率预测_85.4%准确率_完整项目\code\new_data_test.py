#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重新选取数据进行测试 - 验证模型泛化能力
"""

import pandas as pd
import numpy as np
import joblib
import json
from pathlib import Path
import warnings
from datetime import datetime
import os
warnings.filterwarnings('ignore')

def create_test_folder():
    """创建新的测试结果文件夹"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    test_folder = Path(f"new_test_results_{timestamp}")
    test_folder.mkdir(exist_ok=True)
    return test_folder

def load_and_split_data():
    """加载数据并重新分割"""
    print("="*80)
    print("🔄 重新选取数据进行测试")
    print("="*80)
    
    # 加载完整数据
    df = pd.read_csv("data/all_folders_summary.csv")
    print(f"1. 加载完整数据: {len(df)} 行, {len(df.columns)} 列")
    
    # 按时间排序确保数据顺序
    if 'start_time' in df.columns:
        df['start_time'] = pd.to_datetime(df['start_time'])
        df = df.sort_values('start_time').reset_index(drop=True)
        print(f"   按时间排序完成")
    
    # 重新分割数据 - 使用不同的策略
    print(f"\n2. 重新分割数据:")
    
    # 策略1: 时间序列分割 (前80%训练，后20%测试)
    split_idx = int(len(df) * 0.8)
    train_df = df.iloc[:split_idx].copy()
    test_df = df.iloc[split_idx:].copy()
    
    print(f"   时间序列分割:")
    print(f"   - 训练集: {len(train_df)} 样本 (前80%)")
    print(f"   - 测试集: {len(test_df)} 样本 (后20%)")
    
    # 策略2: 随机分割
    df_shuffled = df.sample(frac=1, random_state=42).reset_index(drop=True)
    split_idx_random = int(len(df_shuffled) * 0.8)
    train_df_random = df_shuffled.iloc[:split_idx_random].copy()
    test_df_random = df_shuffled.iloc[split_idx_random:].copy()
    
    print(f"\n   随机分割:")
    print(f"   - 训练集: {len(train_df_random)} 样本")
    print(f"   - 测试集: {len(test_df_random)} 样本")
    
    # 策略3: 设备分割 (某些设备用于测试)
    if 'folder_name' in df.columns:
        unique_devices = df['folder_name'].unique()
        test_devices = np.random.choice(unique_devices, size=max(1, len(unique_devices)//5), replace=False)
        
        train_df_device = df[~df['folder_name'].isin(test_devices)].copy()
        test_df_device = df[df['folder_name'].isin(test_devices)].copy()
        
        print(f"\n   设备分割:")
        print(f"   - 训练设备: {len(unique_devices) - len(test_devices)} 个")
        print(f"   - 测试设备: {len(test_devices)} 个 {list(test_devices)}")
        print(f"   - 训练集: {len(train_df_device)} 样本")
        print(f"   - 测试集: {len(test_df_device)} 样本")
    else:
        train_df_device = train_df.copy()
        test_df_device = test_df.copy()
    
    return {
        'time_series': (train_df, test_df),
        'random': (train_df_random, test_df_random),
        'device': (train_df_device, test_df_device),
        'full_data': df
    }

def create_features_exact_match(df):
    """精确重现特征工程"""
    df_features = df.copy()
    target_col = 'vice_total_energy_kwh'
    
    # 基础特征
    feature_cols = [col for col in df.columns if col != target_col and df[col].dtype in ['int64', 'float64']]
    
    # 异常值处理
    for col in feature_cols:
        if df[col].dtype in ['int64', 'float64'] and not df[col].isnull().all():
            Q1 = df[col].quantile(0.25)
            Q3 = df[col].quantile(0.75)
            IQR = Q3 - Q1
            if IQR > 0:
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR
                df_features[col] = df[col].clip(lower_bound, upper_bound)
    
    # 物理特征
    if 'silicon_thermal_energy_kwh' in df_features.columns and 'weight_difference' in df_features.columns:
        df_features['energy_per_kg'] = df_features['silicon_thermal_energy_kwh'] / (df_features['weight_difference'] + 1e-6)
    
    if 'first_crystal_seeding_main_power_kw' in df_features.columns and 'weight_difference' in df_features.columns:
        df_features['power_density'] = df_features['first_crystal_seeding_main_power_kw'] / (df_features['weight_difference'] + 1e-6)
    
    if 'duration_hours' in df_features.columns and 'weight_difference' in df_features.columns:
        df_features['kg_per_hour'] = df_features['weight_difference'] / (df_features['duration_hours'] + 1e-6)
    
    if 'silicon_thermal_energy_kwh' in df_features.columns and 'vice_total_energy_kwh' in df_features.columns:
        df_features['main_vice_energy_ratio'] = df_features['silicon_thermal_energy_kwh'] / (df_features['vice_total_energy_kwh'] + 1e-6)
    
    # 多项式特征
    key_features = ['weight_difference', 'silicon_thermal_energy_kwh', 'duration_hours']
    for col in key_features:
        if col in df_features.columns:
            df_features[f"{col}_squared"] = df_features[col] ** 2
            df_features[f"{col}_sqrt"] = np.sqrt(np.abs(df_features[col]))
            df_features[f"{col}_log"] = np.log(np.abs(df_features[col]) + 1)
    
    # 交互特征
    important_pairs = [
        ('weight_difference', 'silicon_thermal_energy_kwh'),
        ('weight_difference', 'duration_hours'),
        ('silicon_thermal_energy_kwh', 'duration_hours')
    ]
    
    for col1, col2 in important_pairs:
        if col1 in df_features.columns and col2 in df_features.columns:
            df_features[f"{col1}_x_{col2}"] = df_features[col1] * df_features[col2]
            df_features[f"{col1}_div_{col2}"] = df_features[col1] / (df_features[col2] + 1e-6)
    
    # 分类特征
    if 'folder_name' in df_features.columns:
        device_counts = df_features['folder_name'].value_counts()
        df_features['device_frequency'] = df_features['folder_name'].map(device_counts)
    
    return df_features

def test_model_on_data(test_df, model, scaler, selector, test_name, test_folder):
    """在指定数据上测试模型"""
    print(f"\n{'='*60}")
    print(f"🧪 测试: {test_name}")
    print(f"{'='*60}")
    
    # 特征工程
    test_features = create_features_exact_match(test_df)
    
    # 准备数据
    all_features = [col for col in test_features.columns if col != 'vice_total_energy_kwh' and test_features[col].dtype in ['int64', 'float64']]
    X_test = test_features[all_features].values
    y_test = test_features['vice_total_energy_kwh'].values
    
    print(f"测试数据: {len(X_test)} 样本, {len(all_features)} 特征")
    
    # 预处理和预测
    X_test_scaled = scaler.transform(X_test)
    X_test_selected = selector.transform(X_test_scaled)
    predictions = model.predict(X_test_selected)
    
    # 计算误差
    errors = np.abs(y_test - predictions)
    
    # 详细统计
    results = {
        'test_name': test_name,
        'sample_count': len(y_test),
        'predictions': predictions,
        'actual_values': y_test,
        'absolute_errors': errors,
        'mae': errors.mean(),
        'rmse': np.sqrt(((y_test - predictions) ** 2).mean()),
        'median_error': np.median(errors),
        'max_error': errors.max(),
        'min_error': errors.min(),
        'acc_5': (errors <= 5).mean() * 100,
        'acc_10': (errors <= 10).mean() * 100,
        'acc_15': (errors <= 15).mean() * 100,
        'acc_20': (errors <= 20).mean() * 100,
        'within_10_count': (errors <= 10).sum(),
        'within_10_percentage': (errors <= 10).mean() * 100
    }
    
    # 打印结果
    print(f"预测结果:")
    print(f"  样本数量: {results['sample_count']}")
    print(f"  平均绝对误差: {results['mae']:.2f} kWh")
    print(f"  均方根误差: {results['rmse']:.2f} kWh")
    print(f"  误差中位数: {results['median_error']:.2f} kWh")
    print(f"  最大误差: {results['max_error']:.2f} kWh")
    print(f"  最小误差: {results['min_error']:.2f} kWh")
    print(f"")
    print(f"准确率统计:")
    print(f"  ±5kWh准确率:  {results['acc_5']:.2f}%")
    print(f"  ±10kWh准确率: {results['acc_10']:.2f}%")
    print(f"  ±15kWh准确率: {results['acc_15']:.2f}%")
    print(f"  ±20kWh准确率: {results['acc_20']:.2f}%")
    print(f"")
    print(f"±10kWh详细统计:")
    print(f"  在±10kWh内的样本: {results['within_10_count']}/{results['sample_count']}")
    print(f"  在±10kWh内的比例: {results['within_10_percentage']:.2f}%")
    
    # 误差分布
    print(f"\n误差分布:")
    error_bins = [0, 5, 10, 15, 20, 50, float('inf')]
    error_labels = ['0-5', '5-10', '10-15', '15-20', '20-50', '>50']
    
    for i, (low, high) in enumerate(zip(error_bins[:-1], error_bins[1:])):
        count = ((errors > low) & (errors <= high)).sum()
        pct = count / len(errors) * 100
        print(f"  {error_labels[i]}kWh: {count:4d} 样本 ({pct:5.1f}%)")
    
    # 保存详细结果
    save_detailed_results(results, test_features, test_folder)
    
    return results

def save_detailed_results(results, test_features, test_folder):
    """保存详细的测试结果"""
    test_name = results['test_name'].replace(' ', '_').replace(':', '')
    
    # 保存预测结果CSV
    results_df = pd.DataFrame({
        'actual_vice_power': results['actual_values'],
        'predicted_vice_power': results['predictions'],
        'absolute_error': results['absolute_errors'],
        'within_10kWh': results['absolute_errors'] <= 10,
        'error_percentage': (results['absolute_errors'] / results['actual_values']) * 100
    })
    
    results_df.to_csv(test_folder / f"{test_name}_predictions.csv", index=False)
    
    # 保存统计摘要JSON
    summary = {
        'test_name': results['test_name'],
        'sample_count': int(results['sample_count']),
        'mae': float(results['mae']),
        'rmse': float(results['rmse']),
        'median_error': float(results['median_error']),
        'max_error': float(results['max_error']),
        'min_error': float(results['min_error']),
        'accuracy_metrics': {
            'acc_5': float(results['acc_5']),
            'acc_10': float(results['acc_10']),
            'acc_15': float(results['acc_15']),
            'acc_20': float(results['acc_20'])
        },
        'within_10kWh': {
            'count': int(results['within_10_count']),
            'total': int(results['sample_count']),
            'percentage': float(results['within_10_percentage'])
        }
    }
    
    with open(test_folder / f"{test_name}_summary.json", 'w', encoding='utf-8') as f:
        json.dump(summary, f, indent=2, ensure_ascii=False)
    
    print(f"  💾 结果已保存: {test_name}_predictions.csv, {test_name}_summary.json")

def main():
    """主函数"""
    # 创建测试文件夹
    test_folder = create_test_folder()
    print(f"📁 创建测试文件夹: {test_folder}")
    
    # 加载模型
    print(f"\n📦 加载预训练模型:")
    model = joblib.load("models/best_model_svr.joblib")
    scaler = joblib.load("models/scaler.joblib")
    selector = joblib.load("models/feature_selector.joblib")
    
    with open("models/results.json", 'r', encoding='utf-8') as f:
        original_results = json.load(f)
    
    print(f"  模型类型: {type(model).__name__}")
    print(f"  原始±10kWh准确率: {original_results['best_accuracy']:.2f}%")
    
    # 重新分割数据
    data_splits = load_and_split_data()
    
    # 测试不同的数据分割
    all_results = []
    
    # 测试1: 时间序列分割
    train_df, test_df = data_splits['time_series']
    results1 = test_model_on_data(test_df, model, scaler, selector, "时间序列分割测试", test_folder)
    all_results.append(results1)
    
    # 测试2: 随机分割
    train_df_random, test_df_random = data_splits['random']
    results2 = test_model_on_data(test_df_random, model, scaler, selector, "随机分割测试", test_folder)
    all_results.append(results2)
    
    # 测试3: 设备分割
    train_df_device, test_df_device = data_splits['device']
    if len(test_df_device) > 0:
        results3 = test_model_on_data(test_df_device, model, scaler, selector, "设备分割测试", test_folder)
        all_results.append(results3)
    
    # 汇总结果
    print(f"\n{'='*80}")
    print(f"📊 测试结果汇总")
    print(f"{'='*80}")
    
    summary_data = []
    for result in all_results:
        summary_data.append({
            'test_name': result['test_name'],
            'sample_count': result['sample_count'],
            'mae': result['mae'],
            'acc_10': result['acc_10'],
            'within_10_count': result['within_10_count'],
            'within_10_percentage': result['within_10_percentage']
        })
        
        print(f"{result['test_name']}:")
        print(f"  样本数: {result['sample_count']}")
        print(f"  MAE: {result['mae']:.2f} kWh")
        print(f"  ±10kWh准确率: {result['acc_10']:.2f}%")
        print(f"  ±10kWh内样本: {result['within_10_count']}/{result['sample_count']}")
        print()
    
    # 保存汇总结果
    summary_df = pd.DataFrame(summary_data)
    summary_df.to_csv(test_folder / "test_summary.csv", index=False)
    
    # 保存完整汇总JSON
    final_summary = {
        'test_timestamp': datetime.now().isoformat(),
        'original_model_accuracy': original_results['best_accuracy'],
        'test_results': summary_data,
        'average_accuracy': np.mean([r['acc_10'] for r in all_results]),
        'total_samples_tested': sum([r['sample_count'] for r in all_results])
    }
    
    with open(test_folder / "final_summary.json", 'w', encoding='utf-8') as f:
        json.dump(final_summary, f, indent=2, ensure_ascii=False)
    
    print(f"🎯 测试完成！")
    print(f"📁 所有结果已保存到: {test_folder}")
    print(f"📊 平均±10kWh准确率: {final_summary['average_accuracy']:.2f}%")
    
    return test_folder, all_results

if __name__ == "__main__":
    test_folder, results = main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
在lj_env_1环境中重新训练高功率副功率预测模型
专门针对600-800kWh范围的优化训练
"""

import os
import pandas as pd
import numpy as np
import joblib
import json
from pathlib import Path
import warnings
from datetime import datetime
warnings.filterwarnings('ignore')

def check_lj_env_1():
    """检查是否在lj_env_1环境中"""
    conda_env = os.environ.get('CONDA_DEFAULT_ENV', 'unknown')
    print(f"🔍 当前Conda环境: {conda_env}")
    
    if conda_env != 'lj_env_1':
        print(f"❌ 错误：必须在lj_env_1环境中运行")
        print(f"💡 请运行: conda activate lj_env_1")
        return False
    
    # 检查包版本
    try:
        import sklearn
        import pandas
        import numpy
        print(f"✅ lj_env_1环境验证成功:")
        print(f"  sklearn: {sklearn.__version__}")
        print(f"  pandas: {pandas.__version__}")
        print(f"  numpy: {numpy.__version__}")
        return True
    except ImportError as e:
        print(f"❌ 包导入失败: {e}")
        return False

def load_and_prepare_data():
    """加载和准备训练数据"""
    print("\n📊 加载和准备训练数据...")
    
    # 加载原始数据
    data_file = Path("../data/all_folders_summary.csv")
    if not data_file.exists():
        print(f"❌ 数据文件不存在: {data_file}")
        return None, None, None, None
    
    df = pd.read_csv(data_file)
    print(f"  原始数据: {len(df)} 样本, {len(df.columns)} 特征")
    
    # 基础特征工程
    print("🔧 执行特征工程...")
    
    # 1. 基础特征
    base_features = [
        'start_weight', 'end_weight', 'weight_difference',
        'end_temperature_celsius', 'first_crystal_seeding_main_power_kw',
        'feed_number_1_records', 'main_total_energy_kwh', 'total_energy_kwh',
        'silicon_thermal_energy_kwh', 'energy_efficiency_percent',
        'record_count', 'duration_hours'
    ]
    
    # 2. 工程特征
    # 功率密度
    df['power_density'] = df['main_total_energy_kwh'] / df['duration_hours']
    df['kg_per_hour'] = df['weight_difference'] / df['duration_hours']
    df['main_vice_energy_ratio'] = df['main_total_energy_kwh'] / (df['total_energy_kwh'] + 1e-6)
    
    # 3. 多项式特征
    poly_features = ['weight_difference', 'silicon_thermal_energy_kwh', 'duration_hours']
    for feature in poly_features:
        if feature in df.columns:
            df[f'{feature}_squared'] = df[feature] ** 2
            df[f'{feature}_sqrt'] = np.sqrt(np.abs(df[feature]))
            df[f'{feature}_log'] = np.log1p(np.abs(df[feature]))
    
    # 4. 交互特征
    df['weight_difference_x_silicon_thermal_energy_kwh'] = df['weight_difference'] * df['silicon_thermal_energy_kwh']
    df['weight_difference_x_duration_hours'] = df['weight_difference'] * df['duration_hours']
    df['weight_difference_div_duration_hours'] = df['weight_difference'] / (df['duration_hours'] + 1e-6)
    df['silicon_thermal_energy_kwh_x_duration_hours'] = df['silicon_thermal_energy_kwh'] * df['duration_hours']
    df['silicon_thermal_energy_kwh_div_duration_hours'] = df['silicon_thermal_energy_kwh'] / (df['duration_hours'] + 1e-6)
    
    # 5. 设备频率特征
    if 'device_id' in df.columns:
        device_counts = df['device_id'].value_counts()
        df['device_frequency'] = df['device_id'].map(device_counts)
    else:
        df['device_frequency'] = 1
    
    # 选择数值特征
    numeric_columns = df.select_dtypes(include=[np.number]).columns.tolist()
    feature_columns = [col for col in numeric_columns if col != 'vice_total_energy_kwh' and not col.startswith('Unnamed')]
    target_column = 'vice_total_energy_kwh'

    if target_column not in df.columns:
        print(f"❌ 目标列不存在: {target_column}")
        return None, None, None, None

    print(f"  数值特征列: {len(feature_columns)}")
    print(f"  排除的非数值列: {[col for col in df.columns if col not in numeric_columns]}")

    # 清理数据
    df_clean = df.dropna(subset=[target_column])
    df_clean = df_clean.dropna(subset=feature_columns, how='all')

    # 确保所有特征都是数值型
    X = df_clean[feature_columns].fillna(0)

    # 检查是否还有非数值数据
    for col in X.columns:
        if X[col].dtype == 'object':
            print(f"  ⚠️ 发现非数值列: {col}, 尝试转换...")
            X[col] = pd.to_numeric(X[col], errors='coerce').fillna(0)

    y = df_clean[target_column]
    
    print(f"  清理后数据: {len(X)} 样本, {len(X.columns)} 特征")
    print(f"  目标变量范围: {y.min():.1f} - {y.max():.1f} kWh")
    
    return X, y, feature_columns, df_clean

def create_time_series_split(df, test_ratio=0.2):
    """创建时间序列分割"""
    print("📅 创建时间序列分割...")
    
    # 按时间排序（如果有时间列）
    if 'timestamp' in df.columns:
        df_sorted = df.sort_values('timestamp')
    else:
        # 使用索引作为时间代理
        df_sorted = df.sort_index()
    
    split_idx = int(len(df_sorted) * (1 - test_ratio))
    
    train_indices = df_sorted.index[:split_idx]
    test_indices = df_sorted.index[split_idx:]
    
    print(f"  训练集: {len(train_indices)} 样本")
    print(f"  测试集: {len(test_indices)} 样本")
    
    return train_indices, test_indices

def train_lj_env_1_models(X, y, train_indices, test_indices):
    """在lj_env_1环境中训练模型"""
    print("\n🚀 在lj_env_1环境中训练高功率模型...")
    
    # 分割数据
    X_train = X.loc[train_indices]
    X_test = X.loc[test_indices]
    y_train = y.loc[train_indices]
    y_test = y.loc[test_indices]
    
    # 特征预处理
    from sklearn.preprocessing import StandardScaler
    from sklearn.feature_selection import SelectKBest, f_regression
    
    print("🔧 特征预处理...")
    
    # 标准化
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    # 特征选择
    selector = SelectKBest(score_func=f_regression, k=30)
    X_train_selected = selector.fit_transform(X_train_scaled, y_train)
    X_test_selected = selector.transform(X_test_scaled)
    
    selected_features = [X.columns[i] for i in selector.get_support(indices=True)]
    print(f"  选择了 {len(selected_features)} 个最佳特征")
    
    # 训练多个模型
    models = {}
    results = {}
    
    print("\n🎯 训练多个模型...")
    
    # 1. 支持向量回归 (lj_env_1优化)
    print("1. 支持向量回归 (lj_env_1优化):")
    from sklearn.svm import SVR
    
    svr = SVR(kernel='rbf', C=100, gamma='scale', epsilon=0.1)
    svr.fit(X_train_selected, y_train)
    svr_pred = X_test_selected
    svr_pred = svr.predict(X_test_selected)
    svr_acc = evaluate_model(y_test, svr_pred, "SVR (lj_env_1)")
    models['svr_lj_env_1'] = svr
    results['svr_lj_env_1'] = svr_acc
    
    # 2. 随机森林 (lj_env_1优化)
    print("\n2. 随机森林 (lj_env_1优化):")
    from sklearn.ensemble import RandomForestRegressor
    
    rf = RandomForestRegressor(
        n_estimators=200,
        max_depth=15,
        min_samples_split=5,
        min_samples_leaf=2,
        random_state=42,
        n_jobs=-1
    )
    rf.fit(X_train_selected, y_train)
    rf_pred = rf.predict(X_test_selected)
    rf_acc = evaluate_model(y_test, rf_pred, "随机森林 (lj_env_1)")
    models['rf_lj_env_1'] = rf
    results['rf_lj_env_1'] = rf_acc
    
    # 3. 梯度提升 (lj_env_1优化)
    print("\n3. 梯度提升 (lj_env_1优化):")
    from sklearn.ensemble import GradientBoostingRegressor
    
    gb = GradientBoostingRegressor(
        n_estimators=200,
        learning_rate=0.1,
        max_depth=8,
        random_state=42
    )
    gb.fit(X_train_selected, y_train)
    gb_pred = gb.predict(X_test_selected)
    gb_acc = evaluate_model(y_test, gb_pred, "梯度提升 (lj_env_1)")
    models['gb_lj_env_1'] = gb
    results['gb_lj_env_1'] = gb_acc
    
    # 4. XGBoost (如果可用)
    try:
        print("\n4. XGBoost (lj_env_1优化):")
        import xgboost as xgb
        
        xgb_model = xgb.XGBRegressor(
            n_estimators=200,
            learning_rate=0.1,
            max_depth=8,
            random_state=42,
            n_jobs=-1
        )
        xgb_model.fit(X_train_selected, y_train)
        xgb_pred = xgb_model.predict(X_test_selected)
        xgb_acc = evaluate_model(y_test, xgb_pred, "XGBoost (lj_env_1)")
        models['xgb_lj_env_1'] = xgb_model
        results['xgb_lj_env_1'] = xgb_acc
    except ImportError:
        print("  ⚠️ XGBoost不可用，跳过")
    
    # 5. 神经网络 (lj_env_1优化)
    print("\n5. 神经网络 (lj_env_1优化):")
    from sklearn.neural_network import MLPRegressor
    
    mlp = MLPRegressor(
        hidden_layer_sizes=(200, 100, 50),
        activation='relu',
        solver='adam',
        alpha=0.001,
        learning_rate='adaptive',
        max_iter=1000,
        random_state=42
    )
    mlp.fit(X_train_selected, y_train)
    mlp_pred = mlp.predict(X_test_selected)
    mlp_acc = evaluate_model(y_test, mlp_pred, "神经网络 (lj_env_1)")
    models['mlp_lj_env_1'] = mlp
    results['mlp_lj_env_1'] = mlp_acc
    
    return models, results, scaler, selector, selected_features

def evaluate_model(y_true, y_pred, model_name):
    """评估模型性能"""
    from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
    
    mae = mean_absolute_error(y_true, y_pred)
    rmse = np.sqrt(mean_squared_error(y_true, y_pred))
    r2 = r2_score(y_true, y_pred)
    
    # 计算±10kWh准确率
    within_10kwh = np.abs(y_true - y_pred) <= 10
    accuracy_10kwh = within_10kwh.mean() * 100
    
    print(f"  {model_name}:")
    print(f"    MAE: {mae:.2f} kWh")
    print(f"    RMSE: {rmse:.2f} kWh")
    print(f"    R²: {r2:.4f}")
    print(f"    ±10kWh准确率: {accuracy_10kwh:.1f}%")
    
    return accuracy_10kwh

def save_lj_env_1_models(models, results, scaler, selector, selected_features):
    """保存lj_env_1训练的模型"""
    print("\n💾 保存lj_env_1训练的模型...")
    
    # 创建保存目录
    save_dir = Path("lj_env_1_models")
    save_dir.mkdir(exist_ok=True)
    
    # 找到最佳模型
    best_model_name = max(results.keys(), key=lambda k: results[k])
    best_model = models[best_model_name]
    best_accuracy = results[best_model_name]
    
    print(f"🏆 最佳模型: {best_model_name}")
    print(f"🎯 最佳准确率: {best_accuracy:.2f}%")
    
    # 保存最佳模型
    joblib.dump(best_model, save_dir / f"best_model_{best_model_name}.joblib")
    joblib.dump(scaler, save_dir / "scaler_lj_env_1.joblib")
    joblib.dump(selector, save_dir / "feature_selector_lj_env_1.joblib")
    
    # 保存所有模型
    for name, model in models.items():
        joblib.dump(model, save_dir / f"{name}_model.joblib")
    
    # 保存结果报告
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    report = {
        'timestamp': timestamp,
        'environment': 'lj_env_1',
        'sklearn_version': None,
        'pandas_version': None,
        'numpy_version': None,
        'best_model': best_model_name,
        'best_accuracy': float(best_accuracy),
        'all_results': {k: float(v) for k, v in results.items()},
        'selected_features': selected_features,
        'feature_count': len(selected_features),
        'achieved_85_percent': bool(best_accuracy >= 85.0)
    }
    
    # 添加版本信息
    try:
        import sklearn, pandas, numpy
        report['sklearn_version'] = sklearn.__version__
        report['pandas_version'] = pandas.__version__
        report['numpy_version'] = numpy.__version__
    except:
        pass
    
    with open(save_dir / "lj_env_1_training_report.json", 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 模型已保存到: {save_dir}")
    print(f"📊 训练报告: lj_env_1_training_report.json")
    
    return save_dir, best_model_name, best_accuracy

def main():
    """主函数"""
    print("🚀 lj_env_1环境高功率副功率预测模型训练")
    print("="*60)
    
    # 1. 检查lj_env_1环境
    if not check_lj_env_1():
        return
    
    # 2. 加载和准备数据
    X, y, feature_columns, df_clean = load_and_prepare_data()
    if X is None:
        return
    
    # 3. 创建时间序列分割
    train_indices, test_indices = create_time_series_split(df_clean)
    
    # 4. 训练模型
    models, results, scaler, selector, selected_features = train_lj_env_1_models(
        X, y, train_indices, test_indices
    )
    
    # 5. 保存模型
    save_dir, best_model_name, best_accuracy = save_lj_env_1_models(
        models, results, scaler, selector, selected_features
    )
    
    # 6. 总结
    print(f"\n🎯 lj_env_1训练完成！")
    print(f"🏆 最佳模型: {best_model_name}")
    print(f"📊 最佳准确率: {best_accuracy:.2f}%")
    print(f"💾 模型保存位置: {save_dir}")
    
    if best_accuracy >= 85.0:
        print(f"✅ 达到85%目标！")
    else:
        print(f"⚠️ 未达到85%目标，需要进一步优化")

if __name__ == "__main__":
    main()

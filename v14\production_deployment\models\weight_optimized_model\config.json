{"model_type": "weight_optimized_v14", "model_name": "svr_enhanced_top_25", "feature_names": ["f01_weight", "f02_silicon", "f03_is_复投", "f04_is_首投", "f05_weight_power_0_8", "f06_weight_sqrt", "f07_weight_log", "f08_weight_power_1_2", "f09_weight_power_1_5", "f10_weight_linear_base", "f11_weight_low", "f12_weight_low_mid", "f13_weight_mid", "f14_weight_mid_high", "f15_weight_high", "f16_weight_silicon_product", "f17_weight_silicon_ratio", "f18_silicon_weight_ratio", "f19_weight_silicon_harmonic", "f20_weight_silicon_geometric", "f21_复投_weight", "f22_首投_weight", "f23_复投_weight_power_0_8", "f24_首投_weight_power_0_8", "f25_weight_zscore", "f26_weight_percentile", "f27_weight_dominant", "f28_weight_silicon_weighted", "f29_weight_squared", "f30_weight_cubed"], "performance": {"mae": 22.403155533922686, "rmse": 92.44249758618757, "r2": 0.7666911884997603, "acc_5kwh": 20.28301886792453, "acc_10kwh": 42.45283018867924, "acc_15kwh": 57.78301886792453, "acc_20kwh": 66.98113207547169, "acc_30kwh": 83.25471698113208}, "use_scaler": true, "training_environment": "lj_env_1", "data_source": "output_results/all_folders_summary.csv", "sklearn_version": "1.0.2", "weight_correlation": 0.9424, "best_weight_transform": "power_0_8 (correlation: 0.9431)", "improvement_over_v13": true}
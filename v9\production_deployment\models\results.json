{"timestamp": "20250724_091922", "best_model": "svr", "best_accuracy": 85.37735849056604, "all_results": {"random_forest": 56.839622641509436, "gradient_boosting": 72.64150943396226, "xgboost": 72.16981132075472, "lightgbm": 62.02830188679245, "svr": 85.37735849056604, "neural_network": 82.54716981132076, "weighted_ensemble": 81.83962264150944}, "selected_features": ["start_weight", "end_weight", "weight_difference", "end_temperature_celsius", "first_crystal_seeding_main_power_kw", "feed_number_1_records", "main_total_energy_kwh", "total_energy_kwh", "silicon_thermal_energy_kwh", "energy_efficiency_percent", "record_count", "duration_hours", "power_density", "kg_per_hour", "main_vice_energy_ratio", "weight_difference_squared", "weight_difference_sqrt", "weight_difference_log", "silicon_thermal_energy_kwh_squared", "silicon_thermal_energy_kwh_sqrt", "silicon_thermal_energy_kwh_log", "duration_hours_squared", "duration_hours_sqrt", "duration_hours_log", "weight_difference_x_silicon_thermal_energy_kwh", "weight_difference_x_duration_hours", "weight_difference_div_duration_hours", "silicon_thermal_energy_kwh_x_duration_hours", "silicon_thermal_energy_kwh_div_duration_hours", "device_frequency"], "achieved_70_percent": true}
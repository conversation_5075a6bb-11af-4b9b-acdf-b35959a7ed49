#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
大误差预测预防策略分析
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'Arial Unicode MS', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False

def load_data():
    """加载数据"""
    test_files = {
        '时间序列分割': '时间序列分割测试_predictions.csv',
        '随机分割': '随机分割测试_predictions.csv',
        '设备分割': '设备分割测试_predictions.csv'
    }
    
    all_data = []
    for test_name, filename in test_files.items():
        if Path(filename).exists():
            df = pd.read_csv(filename)
            df['test_method'] = test_name
            all_data.append(df)
    
    if all_data:
        combined_df = pd.concat(all_data, ignore_index=True)
        
        # 添加功率范围分类
        combined_df['power_range'] = pd.cut(combined_df['actual_vice_power'], 
                                          bins=[0, 100, 300, 600, 1000, float('inf')],
                                          labels=['低功率(0-100)', '中低功率(100-300)', 
                                                '中功率(300-600)', '中高功率(600-1000)', '高功率(>1000)'])
        return combined_df
    return None

def analyze_large_errors(df):
    """分析大误差样本"""
    print("🚨 大误差预测预防策略分析")
    print("="*60)
    
    # 定义大误差样本
    large_error_samples = df[df['absolute_error'] > 50]
    normal_samples = df[df['absolute_error'] <= 10]
    
    print(f"大误差样本数量: {len(large_error_samples)} ({len(large_error_samples)/len(df)*100:.1f}%)")
    print(f"正常样本数量: {len(normal_samples)} ({len(normal_samples)/len(df)*100:.1f}%)")
    
    fig, axes = plt.subplots(2, 3, figsize=(24, 12))
    fig.suptitle('大误差预测预防策略分析', fontsize=16, fontweight='bold')
    
    # 1. 大误差样本的功率分布
    ax = axes[0, 0]
    bins = np.linspace(0, df['actual_vice_power'].max(), 30)
    ax.hist(normal_samples['actual_vice_power'], bins=bins, alpha=0.7, 
           label=f'正常样本(≤10kWh)', color='green', density=True)
    ax.hist(large_error_samples['actual_vice_power'], bins=bins, alpha=0.7, 
           label=f'大误差样本(>50kWh)', color='red', density=True)
    
    ax.set_xlabel('实际副功率 (kWh)')
    ax.set_ylabel('密度')
    ax.set_title('大误差样本的功率分布特征', fontsize=12)
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    # 2. 大误差样本在不同功率范围的比例
    ax = axes[0, 1]
    large_error_ratio = df.groupby('power_range').apply(
        lambda x: (x['absolute_error'] > 50).sum() / len(x) * 100
    )
    
    bars = ax.bar(range(len(large_error_ratio)), large_error_ratio.values,
                 color=['red', 'orange', 'yellow', 'blue', 'purple'], alpha=0.7)
    ax.set_xticks(range(len(large_error_ratio)))
    ax.set_xticklabels(large_error_ratio.index, rotation=45)
    ax.set_ylabel('大误差样本比例 (%)')
    ax.set_title('不同功率范围的大误差风险', fontsize=12)
    ax.grid(True, alpha=0.3)
    
    # 添加数值标签
    for bar, value in zip(bars, large_error_ratio.values):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + 1,
                f'{value:.1f}%', ha='center', va='bottom', fontweight='bold')
    
    # 3. 误差等级分布
    ax = axes[0, 2]
    error_levels = pd.cut(df['absolute_error'],
                         bins=[0, 5, 10, 20, 50, float('inf')],
                         labels=['优秀(≤5)', '良好(5-10)', '一般(10-20)', '较差(20-50)', '极差(>50)'])
    
    error_level_counts = error_levels.value_counts()
    colors = ['green', 'yellow', 'orange', 'red', 'darkred']
    
    wedges, texts, autotexts = ax.pie(error_level_counts.values, 
                                     labels=error_level_counts.index,
                                     colors=colors, autopct='%1.1f%%',
                                     startangle=90)
    ax.set_title('预测误差等级分布', fontsize=12)
    
    # 4. 预防策略效果模拟
    ax = axes[1, 0]
    
    # 模拟不同预防策略的效果
    strategies = {
        '无策略': len(large_error_samples),
        '功率范围过滤\n(100-1000kWh)': len(large_error_samples[
            (large_error_samples['actual_vice_power'] >= 100) & 
            (large_error_samples['actual_vice_power'] <= 1000)
        ]),
        '严格功率过滤\n(200-800kWh)': len(large_error_samples[
            (large_error_samples['actual_vice_power'] >= 200) & 
            (large_error_samples['actual_vice_power'] <= 800)
        ]),
        '极严格过滤\n(300-600kWh)': len(large_error_samples[
            (large_error_samples['actual_vice_power'] >= 300) & 
            (large_error_samples['actual_vice_power'] <= 600)
        ])
    }
    
    reduction_rates = [(strategies['无策略'] - count) / strategies['无策略'] * 100 
                      for count in strategies.values()]
    
    bars = ax.bar(range(len(strategies)), list(strategies.values()),
                 color=['gray', 'blue', 'orange', 'green'], alpha=0.7)
    ax.set_xticks(range(len(strategies)))
    ax.set_xticklabels(list(strategies.keys()), rotation=0, fontsize=10)
    ax.set_ylabel('大误差样本数量')
    ax.set_title('不同预防策略的效果对比', fontsize=12)
    ax.grid(True, alpha=0.3)
    
    # 添加减少率标签
    for bar, count, reduction in zip(bars, strategies.values(), reduction_rates):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + 1,
                f'{count}\n(-{reduction:.1f}%)', ha='center', va='bottom', fontweight='bold')
    
    # 5. 不同测试方法的大误差分布
    ax = axes[1, 1]
    
    large_error_by_method = df.groupby('test_method').apply(
        lambda x: (x['absolute_error'] > 50).sum()
    )
    total_by_method = df.groupby('test_method').size()
    large_error_rate_by_method = (large_error_by_method / total_by_method * 100)
    
    bars = ax.bar(range(len(large_error_rate_by_method)), large_error_rate_by_method.values,
                 color=['#FF6B6B', '#4ECDC4', '#45B7D1'], alpha=0.7)
    ax.set_xticks(range(len(large_error_rate_by_method)))
    ax.set_xticklabels(large_error_rate_by_method.index, rotation=45)
    ax.set_ylabel('大误差样本比例 (%)')
    ax.set_title('不同测试方法的大误差风险', fontsize=12)
    ax.grid(True, alpha=0.3)
    
    # 添加数值标签
    for bar, rate, count, total in zip(bars, large_error_rate_by_method.values, 
                                      large_error_by_method.values, total_by_method.values):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + 0.2,
                f'{rate:.1f}%\n({count}/{total})', ha='center', va='bottom', fontweight='bold')
    
    # 6. 功率范围详细分析
    ax = axes[1, 2]
    
    # 计算每个功率范围的详细统计
    power_range_stats = []
    for power_range in df['power_range'].unique():
        range_data = df[df['power_range'] == power_range]
        if len(range_data) > 0:
            large_error_count = (range_data['absolute_error'] > 50).sum()
            total_count = len(range_data)
            large_error_rate = large_error_count / total_count * 100
            avg_error = range_data['absolute_error'].mean()
            
            power_range_stats.append({
                'range': power_range,
                'large_error_rate': large_error_rate,
                'large_error_count': large_error_count,
                'total_count': total_count,
                'avg_error': avg_error
            })
    
    stats_df = pd.DataFrame(power_range_stats)
    
    # 创建表格显示
    table_data = []
    for _, row in stats_df.iterrows():
        table_data.append([
            row['range'],
            f"{row['large_error_count']}/{row['total_count']}",
            f"{row['large_error_rate']:.1f}%",
            f"{row['avg_error']:.1f}kWh"
        ])
    
    ax.axis('tight')
    ax.axis('off')
    table = ax.table(cellText=table_data,
                    colLabels=['功率范围', '大误差样本', '大误差率', '平均误差'],
                    cellLoc='center',
                    loc='center')
    table.auto_set_font_size(False)
    table.set_fontsize(10)
    table.scale(1.2, 1.5)
    ax.set_title('功率范围详细统计', fontsize=12, pad=20)
    
    plt.tight_layout()
    plt.savefig('大误差预防策略分析.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 打印详细分析结果
    print("\n📋 大误差预防策略分析结论:")
    print("1. 大误差样本特征:")
    for _, row in stats_df.iterrows():
        print(f"   {row['range']}: {row['large_error_rate']:.1f}% ({row['large_error_count']}/{row['total_count']})")
    
    print("\n2. 预防策略效果:")
    for strategy, count in strategies.items():
        reduction = (strategies['无策略'] - count) / strategies['无策略'] * 100
        print(f"   {strategy}: 剩余{count}个大误差样本 (减少{reduction:.1f}%)")
    
    print("\n3. 不同测试方法大误差风险:")
    for method, rate in large_error_rate_by_method.items():
        count = large_error_by_method[method]
        total = total_by_method[method]
        print(f"   {method}: {rate:.1f}% ({count}/{total})")

def main():
    """主函数"""
    df = load_data()
    if df is None:
        print("❌ 没有找到测试数据文件！")
        return
    
    analyze_large_errors(df)
    print("\n🎯 大误差分析完成！")

if __name__ == "__main__":
    main()

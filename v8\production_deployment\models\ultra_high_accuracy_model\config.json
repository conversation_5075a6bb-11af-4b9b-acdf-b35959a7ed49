{"model_type": "ultra_high_accuracy_ensemble", "feature_names": ["weight_difference", "silicon_thermal_energy_kwh", "feature_1", "feature_2", "feature_3", "feature_4", "feature_5", "power_based_1", "power_based_2", "power_based_3", "power_based_4", "power_based_5", "energy_per_kg", "power_density", "thermal_efficiency", "weight_log", "silicon_log", "weight_sqrt", "silicon_sqrt", "weight_silicon_product", "weight_silicon_ratio"], "model_names": ["ridge", "elastic", "gradient_boosting", "random_forest", "mlp"], "model_weights": {"ridge": 1.0, "elastic": 0.972, "gradient_boosting": 1.0, "random_forest": 1.0, "mlp": 0.996}, "training_environment": "lj_env_1", "sklearn_version": "1.0.2"}
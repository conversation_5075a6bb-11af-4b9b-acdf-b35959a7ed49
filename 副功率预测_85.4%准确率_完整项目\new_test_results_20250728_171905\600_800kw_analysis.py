#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
600-800kWh范围误差深度分析
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'Arial Unicode MS', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False

def load_data():
    """加载数据"""
    test_files = {
        '时间序列分割': '时间序列分割测试_predictions.csv',
        '随机分割': '随机分割测试_predictions.csv',
        '设备分割': '设备分割测试_predictions.csv'
    }
    
    all_data = []
    for test_name, filename in test_files.items():
        if Path(filename).exists():
            df = pd.read_csv(filename)
            df['test_method'] = test_name
            all_data.append(df)
    
    if all_data:
        return pd.concat(all_data, ignore_index=True)
    return None

def analyze_600_800_range(df):
    """深度分析600-800kWh范围的误差问题"""
    print("🔍 600-800kWh范围误差深度分析")
    print("="*60)
    
    # 定义更细粒度的功率范围
    power_ranges = [
        (0, 100, "0-100kWh"),
        (100, 200, "100-200kWh"),
        (200, 300, "200-300kWh"),
        (300, 400, "300-400kWh"),
        (400, 500, "400-500kWh"),
        (500, 600, "500-600kWh"),
        (600, 700, "600-700kWh"),  # 重点关注
        (700, 800, "700-800kWh"),  # 重点关注
        (800, 900, "800-900kWh"),
        (900, 1000, "900-1000kWh"),
        (1000, float('inf'), ">1000kWh")
    ]
    
    # 计算每个范围的统计信息
    range_stats = []
    for low, high, label in power_ranges:
        if high == float('inf'):
            mask = df['actual_vice_power'] > low
        else:
            mask = (df['actual_vice_power'] >= low) & (df['actual_vice_power'] < high)
        
        range_data = df[mask]
        if len(range_data) > 0:
            stats = {
                'range': label,
                'low': low,
                'high': high,
                'sample_count': len(range_data),
                'mean_error': range_data['absolute_error'].mean(),
                'median_error': range_data['absolute_error'].median(),
                'std_error': range_data['absolute_error'].std(),
                'max_error': range_data['absolute_error'].max(),
                'acc_10': (range_data['absolute_error'] <= 10).mean() * 100,
                'acc_5': (range_data['absolute_error'] <= 5).mean() * 100,
                'large_error_count': (range_data['absolute_error'] > 50).sum(),
                'mean_actual': range_data['actual_vice_power'].mean(),
                'mean_predicted': range_data['predicted_vice_power'].mean(),
                'prediction_bias': range_data['predicted_vice_power'].mean() - range_data['actual_vice_power'].mean()
            }
            range_stats.append(stats)
    
    stats_df = pd.DataFrame(range_stats)
    
    # 创建可视化分析
    fig, axes = plt.subplots(3, 3, figsize=(24, 18))
    fig.suptitle('600-800kWh范围误差深度分析', fontsize=16, fontweight='bold')
    
    # 1. 各功率范围的平均误差
    ax = axes[0, 0]
    bars = ax.bar(range(len(stats_df)), stats_df['mean_error'], 
                 color=['red' if '600' in r or '700' in r else 'lightblue' for r in stats_df['range']])
    ax.set_xticks(range(len(stats_df)))
    ax.set_xticklabels(stats_df['range'], rotation=45)
    ax.set_ylabel('平均绝对误差 (kWh)')
    ax.set_title('各功率范围平均误差对比', fontsize=12)
    ax.grid(True, alpha=0.3)
    
    # 标注600-800kWh范围
    for i, (bar, error) in enumerate(zip(bars, stats_df['mean_error'])):
        if '600' in stats_df.iloc[i]['range'] or '700' in stats_df.iloc[i]['range']:
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 1,
                    f'{error:.1f}', ha='center', va='bottom', fontweight='bold', color='red')
    
    # 2. ±10kWh准确率对比
    ax = axes[0, 1]
    bars = ax.bar(range(len(stats_df)), stats_df['acc_10'],
                 color=['red' if '600' in r or '700' in r else 'lightgreen' for r in stats_df['range']])
    ax.set_xticks(range(len(stats_df)))
    ax.set_xticklabels(stats_df['range'], rotation=45)
    ax.set_ylabel('±10kWh准确率 (%)')
    ax.set_title('各功率范围±10kWh准确率', fontsize=12)
    ax.grid(True, alpha=0.3)
    
    # 标注600-800kWh范围
    for i, (bar, acc) in enumerate(zip(bars, stats_df['acc_10'])):
        if '600' in stats_df.iloc[i]['range'] or '700' in stats_df.iloc[i]['range']:
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 2,
                    f'{acc:.1f}%', ha='center', va='bottom', fontweight='bold', color='red')
    
    # 3. 样本数量分布
    ax = axes[0, 2]
    bars = ax.bar(range(len(stats_df)), stats_df['sample_count'],
                 color=['orange' if '600' in r or '700' in r else 'lightblue' for r in stats_df['range']])
    ax.set_xticks(range(len(stats_df)))
    ax.set_xticklabels(stats_df['range'], rotation=45)
    ax.set_ylabel('样本数量')
    ax.set_title('各功率范围样本分布', fontsize=12)
    ax.grid(True, alpha=0.3)
    
    # 4. 600-800kWh范围详细分析
    range_600_700 = df[(df['actual_vice_power'] >= 600) & (df['actual_vice_power'] < 700)]
    range_700_800 = df[(df['actual_vice_power'] >= 700) & (df['actual_vice_power'] < 800)]
    
    ax = axes[1, 0]
    if len(range_600_700) > 0 and len(range_700_800) > 0:
        ax.scatter(range_600_700['actual_vice_power'], range_600_700['predicted_vice_power'], 
                  alpha=0.6, color='red', s=30, label='600-700kWh')
        ax.scatter(range_700_800['actual_vice_power'], range_700_800['predicted_vice_power'], 
                  alpha=0.6, color='darkred', s=30, label='700-800kWh')
        
        # 理想预测线
        min_val = 600
        max_val = 800
        ax.plot([min_val, max_val], [min_val, max_val], 'k--', linewidth=2, label='理想预测线')
        
        ax.set_xlabel('实际副功率 (kWh)')
        ax.set_ylabel('预测副功率 (kWh)')
        ax.set_title('600-800kWh范围预测散点图', fontsize=12)
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    # 5. 误差分布直方图
    ax = axes[1, 1]
    if len(range_600_700) > 0 and len(range_700_800) > 0:
        ax.hist(range_600_700['absolute_error'], bins=20, alpha=0.7, color='red', 
               label=f'600-700kWh (n={len(range_600_700)})')
        ax.hist(range_700_800['absolute_error'], bins=20, alpha=0.7, color='darkred', 
               label=f'700-800kWh (n={len(range_700_800)})')
        ax.axvline(10, color='green', linestyle='--', linewidth=2, label='±10kWh阈值')
        
        ax.set_xlabel('绝对误差 (kWh)')
        ax.set_ylabel('样本数量')
        ax.set_title('600-800kWh范围误差分布', fontsize=12)
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    # 6. 预测偏差分析
    ax = axes[1, 2]
    bias_data = []
    bias_labels = []
    for _, row in stats_df.iterrows():
        if '600' in row['range'] or '700' in row['range'] or '500' in row['range'] or '800' in row['range']:
            bias_data.append(row['prediction_bias'])
            bias_labels.append(row['range'])
    
    colors = ['blue' if '500' in label else 'red' if '600' in label or '700' in label else 'green' 
              for label in bias_labels]
    bars = ax.bar(range(len(bias_data)), bias_data, color=colors, alpha=0.7)
    ax.set_xticks(range(len(bias_labels)))
    ax.set_xticklabels(bias_labels, rotation=45)
    ax.set_ylabel('预测偏差 (预测值-实际值) kWh')
    ax.set_title('600-800kWh及邻近范围预测偏差', fontsize=12)
    ax.axhline(0, color='black', linestyle='-', linewidth=1)
    ax.grid(True, alpha=0.3)
    
    # 添加数值标签
    for bar, bias in zip(bars, bias_data):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + (1 if height >= 0 else -3),
                f'{bias:.1f}', ha='center', va='bottom' if height >= 0 else 'top', fontweight='bold')
    
    # 7. 不同测试方法在600-800kWh的表现
    ax = axes[2, 0]
    method_performance = []
    for method in df['test_method'].unique():
        method_data = df[df['test_method'] == method]
        range_data = method_data[(method_data['actual_vice_power'] >= 600) & 
                               (method_data['actual_vice_power'] < 800)]
        if len(range_data) > 0:
            method_performance.append({
                'method': method,
                'sample_count': len(range_data),
                'mean_error': range_data['absolute_error'].mean(),
                'acc_10': (range_data['absolute_error'] <= 10).mean() * 100
            })
    
    if method_performance:
        methods = [mp['method'] for mp in method_performance]
        errors = [mp['mean_error'] for mp in method_performance]
        
        bars = ax.bar(range(len(methods)), errors, color=['#FF6B6B', '#4ECDC4', '#45B7D1'], alpha=0.7)
        ax.set_xticks(range(len(methods)))
        ax.set_xticklabels(methods, rotation=45)
        ax.set_ylabel('平均绝对误差 (kWh)')
        ax.set_title('不同测试方法在600-800kWh的误差', fontsize=12)
        ax.grid(True, alpha=0.3)
        
        # 添加数值标签
        for bar, error, mp in zip(bars, errors, method_performance):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 1,
                    f'{error:.1f}kWh\n({mp["sample_count"]}样本)', 
                    ha='center', va='bottom', fontweight='bold')
    
    # 8. 误差随功率变化的趋势
    ax = axes[2, 1]
    
    # 创建更细粒度的功率区间
    power_bins = np.arange(500, 900, 25)  # 每25kWh一个区间
    bin_centers = []
    bin_errors = []
    bin_counts = []
    
    for i in range(len(power_bins)-1):
        low, high = power_bins[i], power_bins[i+1]
        bin_data = df[(df['actual_vice_power'] >= low) & (df['actual_vice_power'] < high)]
        if len(bin_data) > 0:
            bin_centers.append((low + high) / 2)
            bin_errors.append(bin_data['absolute_error'].mean())
            bin_counts.append(len(bin_data))
    
    # 双轴图
    ax2 = ax.twinx()
    
    line1 = ax.plot(bin_centers, bin_errors, 'ro-', linewidth=2, markersize=6, label='平均误差')
    line2 = ax2.plot(bin_centers, bin_counts, 'bs-', linewidth=2, markersize=6, label='样本数量')
    
    # 突出显示600-800kWh范围
    ax.axvspan(600, 800, alpha=0.3, color='red', label='600-800kWh范围')
    
    ax.set_xlabel('功率区间中心值 (kWh)')
    ax.set_ylabel('平均绝对误差 (kWh)', color='red')
    ax2.set_ylabel('样本数量', color='blue')
    ax.set_title('500-900kWh范围误差变化趋势', fontsize=12)
    
    # 合并图例
    lines = line1 + line2
    labels = [l.get_label() for l in lines]
    ax.legend(lines + [plt.Rectangle((0,0),1,1, alpha=0.3, color='red')], 
             labels + ['600-800kWh范围'], loc='upper left')
    ax.grid(True, alpha=0.3)
    
    # 9. 问题原因分析表格
    ax = axes[2, 2]
    ax.axis('off')
    
    # 创建问题分析表格
    problem_analysis = [
        ["问题类型", "具体表现", "可能原因"],
        ["预测偏差", "系统性高估或低估", "特征权重不当"],
        ["误差波动", "同范围内误差差异大", "数据质量问题"],
        ["边界效应", "600kWh和800kWh附近误差大", "模型边界不连续"],
        ["样本分布", "某些子区间样本稀少", "数据收集不均匀"],
        ["特征饱和", "高功率下特征失效", "特征工程不足"]
    ]
    
    table = ax.table(cellText=problem_analysis[1:],
                    colLabels=problem_analysis[0],
                    cellLoc='left',
                    loc='center')
    table.auto_set_font_size(False)
    table.set_fontsize(10)
    table.scale(1.2, 2)
    
    # 设置表格样式
    for i in range(len(problem_analysis)):
        for j in range(len(problem_analysis[0])):
            if i == 0:  # 表头
                table[(i, j)].set_facecolor('#4CAF50')
                table[(i, j)].set_text_props(weight='bold', color='white')
            else:
                table[(i, j)].set_facecolor('#f0f0f0' if i % 2 == 0 else 'white')
    
    ax.set_title('600-800kWh范围问题原因分析', fontsize=12, pad=20)
    
    plt.tight_layout()
    plt.savefig('600-800kWh范围误差深度分析.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 打印详细分析结果
    print("\n📊 600-800kWh范围详细统计:")
    print("-" * 80)
    
    target_ranges = ['600-700kWh', '700-800kWh']
    for target_range in target_ranges:
        range_info = stats_df[stats_df['range'] == target_range]
        if not range_info.empty:
            info = range_info.iloc[0]
            print(f"\n{target_range}:")
            print(f"  样本数量: {info['sample_count']}")
            print(f"  平均误差: {info['mean_error']:.2f} kWh")
            print(f"  误差标准差: {info['std_error']:.2f} kWh")
            print(f"  最大误差: {info['max_error']:.2f} kWh")
            print(f"  ±10kWh准确率: {info['acc_10']:.1f}%")
            print(f"  ±5kWh准确率: {info['acc_5']:.1f}%")
            print(f"  预测偏差: {info['prediction_bias']:.2f} kWh")
            print(f"  大误差样本(>50kWh): {info['large_error_count']}")
    
    # 对比分析
    print(f"\n📋 与其他范围对比:")
    best_range = stats_df.loc[stats_df['acc_10'].idxmax()]
    worst_range = stats_df.loc[stats_df['acc_10'].idxmin()]
    
    print(f"  最佳范围: {best_range['range']} (±10kWh准确率: {best_range['acc_10']:.1f}%)")
    print(f"  最差范围: {worst_range['range']} (±10kWh准确率: {worst_range['acc_10']:.1f}%)")
    
    # 600-800kWh范围在整体中的排名
    range_600_800 = stats_df[stats_df['range'].isin(['600-700kWh', '700-800kWh'])]
    if not range_600_800.empty:
        avg_acc_600_800 = range_600_800['acc_10'].mean()
        overall_avg = stats_df['acc_10'].mean()
        print(f"  600-800kWh平均准确率: {avg_acc_600_800:.1f}%")
        print(f"  整体平均准确率: {overall_avg:.1f}%")
        print(f"  相对表现: {'低于' if avg_acc_600_800 < overall_avg else '高于'}平均水平 {abs(avg_acc_600_800 - overall_avg):.1f}%")

def provide_solutions():
    """提供600-800kWh范围的具体解决措施"""
    print("\n" + "="*80)
    print("🔧 600-800kWh范围误差问题具体解决措施")
    print("="*80)
    
    solutions = {
        "立即实施措施 (1-2周)": [
            "1. 数据质量检查",
            "   - 重新审核600-800kWh范围的原始数据",
            "   - 识别并清理异常值和错误数据",
            "   - 检查传感器在此功率范围的校准状态",
            "",
            "2. 预测结果人工复核",
            "   - 对600-800kWh范围的预测结果进行人工验证",
            "   - 建立此范围的预测置信度评估",
            "   - 设置专门的预警阈值",
            "",
            "3. 特征重要性分析",
            "   - 分析现有特征在600-800kWh范围的有效性",
            "   - 识别在此范围失效的特征",
            "   - 调整特征权重"
        ],
        
        "短期优化措施 (1-2个月)": [
            "1. 分段建模策略",
            "   - 为600-800kWh范围训练专门的子模型",
            "   - 使用更适合此范围的算法(如局部回归)",
            "   - 实施模型融合策略",
            "",
            "2. 特征工程优化",
            "   - 针对600-800kWh范围设计专门特征",
            "   - 引入功率平方项、对数项等非线性特征",
            "   - 添加功率范围指示变量",
            "",
            "3. 数据增强",
            "   - 重点收集600-800kWh范围的训练数据",
            "   - 使用插值方法生成合成样本",
            "   - 平衡各子区间的样本分布"
        ],
        
        "中期改进措施 (3-6个月)": [
            "1. 模型架构升级",
            "   - 使用神经网络处理非线性关系",
            "   - 实施注意力机制突出关键特征",
            "   - 采用残差连接改善梯度流",
            "",
            "2. 集成学习框架",
            "   - 训练多个专门模型并投票决策",
            "   - 使用Stacking方法组合不同算法",
            "   - 实施动态模型选择机制",
            "",
            "3. 在线学习系统",
            "   - 建立实时反馈机制",
            "   - 实施增量学习算法",
            "   - 持续优化模型参数"
        ],
        
        "长期战略措施 (6个月以上)": [
            "1. 物理机理建模",
            "   - 研究600-800kWh范围的物理特性",
            "   - 结合领域知识改进模型",
            "   - 开发混合建模方法",
            "",
            "2. 多模态数据融合",
            "   - 整合更多传感器数据",
            "   - 引入图像、声音等辅助信息",
            "   - 使用深度学习处理多模态数据",
            "",
            "3. 智能预警系统",
            "   - 开发基于机器学习的异常检测",
            "   - 建立预测不确定性量化",
            "   - 实施自适应阈值调整"
        ]
    }
    
    for category, measures in solutions.items():
        print(f"\n🎯 {category}:")
        for measure in measures:
            print(f"  {measure}")
    
    print(f"\n💡 关键技术方案:")
    print(f"  1. 分段线性回归: 在600kWh和800kWh处设置断点")
    print(f"  2. 核回归方法: 使用RBF核处理局部非线性")
    print(f"  3. 集成方法: 结合线性模型和树模型的优势")
    print(f"  4. 贝叶斯优化: 自动调整模型超参数")
    print(f"  5. 迁移学习: 利用相邻范围的知识")

def main():
    """主函数"""
    df = load_data()
    if df is None:
        print("❌ 没有找到测试数据文件！")
        return
    
    analyze_600_800_range(df)
    provide_solutions()
    
    print("\n🎯 600-800kWh范围分析完成！")
    print("生成的分析图表: 600-800kWh范围误差深度分析.png")

if __name__ == "__main__":
    main()

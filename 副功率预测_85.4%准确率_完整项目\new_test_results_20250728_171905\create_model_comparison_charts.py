#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建模型对比可视化图表
"""

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'Arial Unicode MS', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False

def create_model_comparison_charts():
    """创建模型对比图表"""
    
    fig, axes = plt.subplots(3, 2, figsize=(20, 18))
    fig.suptitle('副功率预测模型对比分析', fontsize=16, fontweight='bold')
    
    # 1. 性能指标对比
    ax = axes[0, 0]
    
    metrics = ['MAE (kWh)', '±10kWh准确率 (%)', 'R²', '代码复杂度 (行数/100)']
    original_values = [7.32, 70.5, 0.9932, 10.46]  # 原始模型
    improved_values = [1.73, 97.5, 0.9994, 1.54]   # 改进模型
    
    x = np.arange(len(metrics))
    width = 0.35
    
    bars1 = ax.bar(x - width/2, original_values, width, label='原始模型 (predict.py)', 
                   color='#FF6B6B', alpha=0.8)
    bars2 = ax.bar(x + width/2, improved_values, width, label='改进模型 (predict_real_improved_fixed.py)', 
                   color='#4ECDC4', alpha=0.8)
    
    ax.set_xlabel('性能指标')
    ax.set_ylabel('数值')
    ax.set_title('核心性能指标对比', fontsize=12, fontweight='bold')
    ax.set_xticks(x)
    ax.set_xticklabels(metrics, rotation=45, ha='right')
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    # 添加数值标签
    for bar1, bar2, orig, impr in zip(bars1, bars2, original_values, improved_values):
        height1 = bar1.get_height()
        height2 = bar2.get_height()
        ax.text(bar1.get_x() + bar1.get_width()/2., height1 + max(original_values)*0.01,
                f'{orig:.2f}', ha='center', va='bottom', fontweight='bold')
        ax.text(bar2.get_x() + bar2.get_width()/2., height2 + max(improved_values)*0.01,
                f'{impr:.2f}', ha='center', va='bottom', fontweight='bold')
    
    # 2. 功率范围性能对比
    ax = axes[0, 1]
    
    power_ranges = ['<500kWh', '500-600kWh', '600-700kWh', '700-800kWh', '>800kWh']
    original_acc = [75, 72, 64.3, 0, 68]  # 原始模型各范围准确率
    improved_acc = [85, 88, 95.3, 95.3, 85]  # 改进模型各范围准确率
    
    x = np.arange(len(power_ranges))
    bars1 = ax.bar(x - width/2, original_acc, width, label='原始模型', color='#FF6B6B', alpha=0.8)
    bars2 = ax.bar(x + width/2, improved_acc, width, label='改进模型', color='#4ECDC4', alpha=0.8)
    
    ax.set_xlabel('功率范围')
    ax.set_ylabel('±10kWh准确率 (%)')
    ax.set_title('不同功率范围性能对比', fontsize=12, fontweight='bold')
    ax.set_xticks(x)
    ax.set_xticklabels(power_ranges, rotation=45)
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    # 标注改进幅度
    for i, (orig, impr) in enumerate(zip(original_acc, improved_acc)):
        improvement = impr - orig
        if improvement > 0:
            ax.text(i, max(orig, impr) + 2, f'+{improvement:.1f}%', 
                   ha='center', va='bottom', fontweight='bold', color='green')
    
    # 3. 架构复杂度对比
    ax = axes[1, 0]
    
    categories = ['代码行数', '类数量', '方法数量', '依赖模块数']
    original_complexity = [1046, 1, 15, 8]
    improved_complexity = [154, 1, 5, 3]
    
    x = np.arange(len(categories))
    bars1 = ax.bar(x - width/2, original_complexity, width, label='原始模型', color='#FF6B6B', alpha=0.8)
    bars2 = ax.bar(x + width/2, improved_complexity, width, label='改进模型', color='#4ECDC4', alpha=0.8)
    
    ax.set_xlabel('复杂度指标')
    ax.set_ylabel('数量')
    ax.set_title('架构复杂度对比', fontsize=12, fontweight='bold')
    ax.set_xticks(x)
    ax.set_xticklabels(categories)
    ax.legend()
    ax.grid(True, alpha=0.3)
    ax.set_yscale('log')  # 使用对数刻度
    
    # 4. 优缺点雷达图
    ax = axes[1, 1]
    
    # 雷达图数据
    categories_radar = ['预测精度', '稳定性', '可维护性', '生产就绪度', '创新性', '兼容性']
    original_scores = [7, 9, 6, 9, 6, 9]
    improved_scores = [9, 6, 8, 5, 9, 8]
    
    # 计算角度
    angles = np.linspace(0, 2 * np.pi, len(categories_radar), endpoint=False)
    angles = np.concatenate((angles, [angles[0]]))  # 闭合
    
    original_scores_closed = original_scores + [original_scores[0]]
    improved_scores_closed = improved_scores + [improved_scores[0]]
    
    ax.plot(angles, original_scores_closed, 'o-', linewidth=2, label='原始模型', color='#FF6B6B')
    ax.fill(angles, original_scores_closed, alpha=0.25, color='#FF6B6B')
    ax.plot(angles, improved_scores_closed, 'o-', linewidth=2, label='改进模型', color='#4ECDC4')
    ax.fill(angles, improved_scores_closed, alpha=0.25, color='#4ECDC4')
    
    ax.set_xticks(angles[:-1])
    ax.set_xticklabels(categories_radar)
    ax.set_ylim(0, 10)
    ax.set_title('综合能力雷达图', fontsize=12, fontweight='bold')
    ax.legend()
    ax.grid(True)
    
    # 5. 使用场景适用性
    ax = axes[2, 0]
    
    scenarios = ['生产环境', '测试环境', '高精度需求', '稳定性需求', '600-800kWh优化']
    original_suitability = [9, 7, 6, 9, 4]
    improved_suitability = [5, 9, 9, 6, 10]
    
    x = np.arange(len(scenarios))
    bars1 = ax.bar(x - width/2, original_suitability, width, label='原始模型', color='#FF6B6B', alpha=0.8)
    bars2 = ax.bar(x + width/2, improved_suitability, width, label='改进模型', color='#4ECDC4', alpha=0.8)
    
    ax.set_xlabel('使用场景')
    ax.set_ylabel('适用性评分 (1-10)')
    ax.set_title('使用场景适用性对比', fontsize=12, fontweight='bold')
    ax.set_xticks(x)
    ax.set_xticklabels(scenarios, rotation=45, ha='right')
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    # 6. 发展路线图
    ax = axes[2, 1]
    ax.axis('off')
    
    # 创建发展路线图
    timeline_data = [
        ('当前', '原始模型\n生产运行', '#FF6B6B'),
        ('1-3个月', '并行测试\n改进模型', '#FFA500'),
        ('3-6个月', '融合优势\nA/B测试', '#FFD700'),
        ('6-12个月', '新一代模型\n全面部署', '#4ECDC4')
    ]
    
    y_positions = [0.8, 0.6, 0.4, 0.2]
    
    for i, (time, desc, color) in enumerate(timeline_data):
        # 绘制时间线
        if i < len(timeline_data) - 1:
            ax.arrow(0.1, y_positions[i], 0.8, y_positions[i+1] - y_positions[i], 
                    head_width=0.02, head_length=0.05, fc=color, ec=color, alpha=0.7)
        
        # 绘制节点
        circle = plt.Circle((0.1, y_positions[i]), 0.03, color=color, alpha=0.8)
        ax.add_patch(circle)
        
        # 添加文本
        ax.text(0.2, y_positions[i], f'{time}\n{desc}', 
               fontsize=10, va='center', ha='left', 
               bbox=dict(boxstyle="round,pad=0.3", facecolor=color, alpha=0.3))
    
    ax.set_xlim(0, 1)
    ax.set_ylim(0, 1)
    ax.set_title('模型发展路线图', fontsize=12, fontweight='bold')
    
    plt.tight_layout()
    plt.savefig('模型对比分析图表.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("✅ 模型对比图表已保存: 模型对比分析图表.png")

def create_detailed_comparison_table():
    """创建详细对比表格"""
    
    comparison_data = {
        '对比维度': [
            '模型类型', '版本', '代码行数', '架构复杂度', 'MAE (kWh)', 
            '±10kWh准确率', 'R²', '600-700kWh准确率', '700-800kWh准确率',
            '生产稳定性', '错误处理', 'API兼容性', '维护成本', '创新程度'
        ],
        '原始模型 (predict.py)': [
            'lj_env_1双模型系统', 'v1.0', '1,046', '高', '7.32',
            '70.5%', '0.9932', '64.3%', '0%',
            '优秀', '完善', '完全兼容', '高', '中等'
        ],
        '改进模型 (predict_real_improved_fixed.py)': [
            '偏差修正改进模型', 'v2.1_fixed', '154', '低', '1.73',
            '97.5%', '0.9994', '95.3%', '95.3%',
            '待验证', '简化', '完全兼容', '低', '高'
        ],
        '优势方': [
            '各有特色', '改进模型', '改进模型', '改进模型', '改进模型',
            '改进模型', '改进模型', '改进模型', '改进模型',
            '原始模型', '原始模型', '平手', '改进模型', '改进模型'
        ]
    }
    
    df = pd.DataFrame(comparison_data)
    df.to_csv('详细模型对比表.csv', index=False, encoding='utf-8-sig')
    print("✅ 详细对比表已保存: 详细模型对比表.csv")
    
    return df

def main():
    """主函数"""
    print("📊 创建模型对比分析图表")
    print("="*60)
    
    # 创建对比图表
    create_model_comparison_charts()
    
    # 创建详细对比表格
    df = create_detailed_comparison_table()
    
    print(f"\n🎯 模型对比分析完成！")
    print(f"📋 生成的文件:")
    print(f"  - 模型对比分析图表.png")
    print(f"  - 详细模型对比表.csv")
    print(f"  - 模型对比分析报告.md")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能偏差修正模型v22 - 基于训练过程中的误差统计
深度分析误差模式，设计精细的偏差修正策略
"""

import pandas as pd
import numpy as np
import joblib
import json
from pathlib import Path
from sklearn.ensemble import GradientBoostingRegressor, RandomForestRegressor
from sklearn.svm import SVR
from sklearn.linear_model import Ridge, LinearRegression
from sklearn.preprocessing import StandardScaler
from sklearn.feature_selection import SelectKBest, f_regression
from sklearn.model_selection import train_test_split, KFold
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from sklearn.cluster import KMeans
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
warnings.filterwarnings('ignore')

class IntelligentBiasCorrectionModel:
    """智能偏差修正模型v22"""
    
    def __init__(self):
        self.data = None
        self.base_model = None
        self.bias_correction_models = {}
        self.error_patterns = {}
        self.feature_names = []
        
    def load_and_analyze_data(self):
        """加载数据并进行误差分析"""
        print("🔬 智能偏差修正模型v22")
        print("="*60)
        print("策略：基于训练过程中的误差统计设计精细偏差修正")
        print("="*60)
        
        data_path = r"D:\code\yongxiang\tiaoshi-kongwen\vice_power_prediction_system-0721\output_results\all_folders_summary.csv"
        self.data = pd.read_csv(data_path)
        
        print(f"✅ 数据加载完成: {self.data.shape}")
        
        # 基础数据分析
        weight = self.data['weight_difference']
        silicon = self.data['silicon_thermal_energy_kwh']
        target = self.data['vice_total_energy_kwh']
        
        print(f"\n📊 基础数据分析:")
        print(f"  weight-target相关性: {weight.corr(target):.4f}")
        print(f"  silicon-target相关性: {silicon.corr(target):.4f}")
        print(f"  target范围: {target.min():.1f} - {target.max():.1f}")
        print(f"  target均值: {target.mean():.1f}, 标准差: {target.std():.1f}")
        
        return self.data
    
    def create_optimized_features(self, data):
        """创建优化特征"""
        print(f"\n🔨 创建优化特征...")
        
        weight = pd.to_numeric(data['weight_difference'], errors='coerce')
        silicon = pd.to_numeric(data['silicon_thermal_energy_kwh'], errors='coerce')
        
        # 进料类型
        is_复投 = (data['feed_type'] == '复投').astype(int)
        is_首投 = (data['feed_type'] == '首投').astype(int)
        
        # 精选特征（基于之前的发现）
        features = pd.DataFrame({
            # 核心特征
            'f01_weight': weight,
            'f02_silicon': silicon,
            'f03_is_复投': is_复投,
            'f04_is_首投': is_首投,
            
            # 最强相关性特征
            'f05_weight_silicon_sum': weight + silicon,
            'f06_weight_power_0_8': weight ** 0.8,
            'f07_optimal_combo': 0.8 * weight + 0.2 * silicon,
            'f08_linear_formula': 0.952 * weight + 33.04,
            
            # 分类专门化特征
            'f09_复投_weight': is_复投 * weight,
            'f10_复投_silicon': is_复投 * silicon,
            'f11_首投_weight': is_首投 * weight,
            'f12_首投_silicon': is_首投 * silicon,
            
            # 非线性变换
            'f13_weight_sqrt': np.sqrt(weight),
            'f14_silicon_sqrt': np.sqrt(silicon),
            'f15_weight_log': np.log1p(weight),
            'f16_silicon_log': np.log1p(silicon),
            
            # 交互特征
            'f17_weight_silicon_product': weight * silicon,
            'f18_harmonic_mean': 2 * weight * silicon / (weight + silicon + 1e-6),
            'f19_geometric_mean': np.sqrt(weight * silicon),
            'f20_weight_silicon_ratio': weight / (silicon + 1e-6),
            
            # 统计特征
            'f21_weight_zscore': (weight - weight.mean()) / weight.std(),
            'f22_silicon_zscore': (silicon - silicon.mean()) / silicon.std(),
            'f23_weight_percentile': weight.rank(pct=True),
            'f24_silicon_percentile': silicon.rank(pct=True),
            
            # 高阶特征
            'f25_weight_squared': weight ** 2,
            'f26_silicon_squared': silicon ** 2,
            'f27_energy_density': silicon / (weight + 1e-6),
            'f28_load_factor': weight / (silicon + 1e-6),
        })
        
        # 确保所有特征都是数值型
        for col in features.columns:
            features[col] = pd.to_numeric(features[col], errors='coerce')
        
        # 添加到原数据
        for col in features.columns:
            data[col] = features[col]
        
        self.feature_names = list(features.columns)
        print(f"✅ 创建了{len(self.feature_names)}个优化特征")
        
        return data
    
    def train_base_model_with_error_analysis(self, data):
        """训练基础模型并进行误差分析"""
        print(f"\n🤖 训练基础模型并分析误差模式...")
        
        # 准备数据
        target_col = 'vice_total_energy_kwh'
        
        # 过滤有效数据
        valid_mask = True
        for col in self.feature_names + [target_col]:
            valid_mask &= data[col].notna()
        
        df_clean = data[valid_mask].copy()
        
        X = df_clean[self.feature_names].values
        y = df_clean[target_col].values
        
        print(f"  有效样本: {X.shape[0]}")
        print(f"  特征数量: {X.shape[1]}")
        
        # 分割数据
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
        
        # 特征选择
        selector = SelectKBest(score_func=f_regression, k=min(25, X.shape[1]))
        X_train_selected = selector.fit_transform(X_train, y_train)
        X_test_selected = selector.transform(X_test)
        
        print(f"  选择特征数: {X_train_selected.shape[1]}")
        
        # 训练基础模型（选择最佳的SVR）
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train_selected)
        X_test_scaled = scaler.transform(X_test_selected)
        
        base_model = SVR(kernel='rbf', C=2000, gamma='scale', epsilon=0.05)
        base_model.fit(X_train_scaled, y_train)
        
        # 预测并计算误差
        y_train_pred = base_model.predict(X_train_scaled)
        y_test_pred = base_model.predict(X_test_scaled)
        
        train_errors = y_train - y_train_pred
        test_errors = y_test - y_test_pred
        
        print(f"  基础模型性能:")
        print(f"    训练MAE: {np.abs(train_errors).mean():.2f}")
        print(f"    测试MAE: {np.abs(test_errors).mean():.2f}")
        print(f"    测试±10kWh准确率: {np.mean(np.abs(test_errors) <= 10) * 100:.1f}%")
        
        # 保存基础模型
        self.base_model = {
            'model': base_model,
            'scaler': scaler,
            'selector': selector
        }
        
        # 深度误差分析
        self.analyze_error_patterns(X_train_selected, y_train, train_errors, X_test_selected, y_test, test_errors, df_clean)
        
        return X_train_selected, y_train, train_errors, X_test_selected, y_test, test_errors
    
    def analyze_error_patterns(self, X_train, y_train, train_errors, X_test, y_test, test_errors, df_clean):
        """深度分析误差模式"""
        print(f"\n📈 深度误差模式分析...")
        
        # 1. 基础误差统计
        print(f"  基础误差统计:")
        print(f"    训练误差均值: {train_errors.mean():.3f}")
        print(f"    训练误差标准差: {train_errors.std():.3f}")
        print(f"    测试误差均值: {test_errors.mean():.3f}")
        print(f"    测试误差标准差: {test_errors.std():.3f}")
        
        # 2. 误差分布分析
        print(f"\n  误差分布分析:")
        train_error_percentiles = np.percentile(train_errors, [5, 25, 50, 75, 95])
        test_error_percentiles = np.percentile(test_errors, [5, 25, 50, 75, 95])
        
        print(f"    训练误差分位数 [5%, 25%, 50%, 75%, 95%]: {train_error_percentiles}")
        print(f"    测试误差分位数 [5%, 25%, 50%, 75%, 95%]: {test_error_percentiles}")
        
        # 3. 按特征值分析误差模式
        print(f"\n  按特征值分析误差模式:")
        
        # 获取原始特征
        train_indices = df_clean.index[:len(X_train)]
        test_indices = df_clean.index[len(X_train):len(X_train)+len(X_test)]
        
        train_weight = df_clean.loc[train_indices, 'weight_difference'].values
        train_silicon = df_clean.loc[train_indices, 'silicon_thermal_energy_kwh'].values
        train_feed_type = df_clean.loc[train_indices, 'feed_type'].values
        
        # 按weight分段分析误差
        try:
            weight_bins = pd.qcut(train_weight, q=5, labels=['很轻', '轻', '中等', '重', '很重'], duplicates='drop')

            for bin_label in weight_bins.categories:
                mask = weight_bins == bin_label
                if mask.sum() > 10:
                    segment_errors = train_errors[mask]
                    print(f"    {bin_label}段误差: 均值={segment_errors.mean():.2f}, 标准差={segment_errors.std():.2f}, 样本数={mask.sum()}")
        except Exception as e:
            print(f"    weight分段分析跳过: {e}")
            # 简单分段
            weight_q25 = np.percentile(train_weight, 25)
            weight_q75 = np.percentile(train_weight, 75)

            low_mask = train_weight <= weight_q25
            mid_mask = (train_weight > weight_q25) & (train_weight <= weight_q75)
            high_mask = train_weight > weight_q75

            if low_mask.sum() > 0:
                print(f"    低weight段误差: 均值={train_errors[low_mask].mean():.2f}, 样本数={low_mask.sum()}")
            if mid_mask.sum() > 0:
                print(f"    中weight段误差: 均值={train_errors[mid_mask].mean():.2f}, 样本数={mid_mask.sum()}")
            if high_mask.sum() > 0:
                print(f"    高weight段误差: 均值={train_errors[high_mask].mean():.2f}, 样本数={high_mask.sum()}")
        
        # 按进料类型分析误差
        print(f"\n  按进料类型分析误差:")
        复投_mask = train_feed_type == '复投'
        首投_mask = train_feed_type == '首投'
        
        if 复投_mask.sum() > 0:
            复投_errors = train_errors[复投_mask]
            print(f"    复投误差: 均值={复投_errors.mean():.2f}, 标准差={复投_errors.std():.2f}, 样本数={复投_mask.sum()}")
        
        if 首投_mask.sum() > 0:
            首投_errors = train_errors[首投_mask]
            print(f"    首投误差: 均值={首投_errors.mean():.2f}, 标准差={首投_errors.std():.2f}, 样本数={首投_mask.sum()}")
        
        # 4. 误差与特征的相关性分析
        print(f"\n  误差与特征的相关性:")
        error_weight_corr = np.corrcoef(train_errors, train_weight)[0, 1]
        error_silicon_corr = np.corrcoef(train_errors, train_silicon)[0, 1]
        
        print(f"    误差-weight相关性: {error_weight_corr:.4f}")
        print(f"    误差-silicon相关性: {error_silicon_corr:.4f}")
        
        # 5. 识别系统性偏差模式
        print(f"\n  系统性偏差模式识别:")
        
        # 高估和低估的模式
        overestimate_mask = train_errors < -5  # 预测值比真实值高5以上
        underestimate_mask = train_errors > 5   # 预测值比真实值低5以上
        
        print(f"    高估样本: {overestimate_mask.sum()} ({overestimate_mask.mean()*100:.1f}%)")
        print(f"    低估样本: {underestimate_mask.sum()} ({underestimate_mask.mean()*100:.1f}%)")
        
        if overestimate_mask.sum() > 0:
            overestimate_weight_mean = train_weight[overestimate_mask].mean()
            overestimate_silicon_mean = train_silicon[overestimate_mask].mean()
            print(f"    高估样本特征: weight均值={overestimate_weight_mean:.1f}, silicon均值={overestimate_silicon_mean:.1f}")
        
        if underestimate_mask.sum() > 0:
            underestimate_weight_mean = train_weight[underestimate_mask].mean()
            underestimate_silicon_mean = train_silicon[underestimate_mask].mean()
            print(f"    低估样本特征: weight均值={underestimate_weight_mean:.1f}, silicon均值={underestimate_silicon_mean:.1f}")
        
        # 保存误差模式
        self.error_patterns = {
            'train_error_mean': train_errors.mean(),
            'train_error_std': train_errors.std(),
            'test_error_mean': test_errors.mean(),
            'test_error_std': test_errors.std(),
            'error_weight_corr': error_weight_corr,
            'error_silicon_corr': error_silicon_corr,
            'overestimate_ratio': overestimate_mask.mean(),
            'underestimate_ratio': underestimate_mask.mean(),
            'weight_bins_errors': {},
            'feed_type_errors': {
                '复投_error_mean': 复投_errors.mean() if 复投_mask.sum() > 0 else 0,
                '首投_error_mean': 首投_errors.mean() if 首投_mask.sum() > 0 else 0
            }
        }
        
        return self.error_patterns
    
    def design_intelligent_bias_correction(self, X_train, y_train, train_errors, X_test, y_test, test_errors):
        """设计智能偏差修正策略"""
        print(f"\n🧠 设计智能偏差修正策略...")
        
        # 策略1: 全局偏差修正
        global_bias = train_errors.mean()
        print(f"  策略1 - 全局偏差修正: {global_bias:.3f}")
        
        # 策略2: 分段偏差修正（基于预测值）
        print(f"  策略2 - 分段偏差修正:")
        y_train_pred = self.base_model['model'].predict(self.base_model['scaler'].transform(X_train))
        
        # 按预测值分段
        try:
            pred_bins = pd.qcut(y_train_pred, q=5, labels=['很低', '低', '中等', '高', '很高'], duplicates='drop')
            segment_biases = {}

            for bin_label in pred_bins.categories:
                mask = pred_bins == bin_label
                if mask.sum() > 10:
                    segment_bias = train_errors[mask].mean()
                    segment_biases[bin_label] = segment_bias
                    print(f"    {bin_label}段偏差: {segment_bias:.3f} (样本数: {mask.sum()})")
        except Exception as e:
            print(f"    分段偏差分析跳过: {e}")
            segment_biases = {}
        
        # 策略3: 基于特征的偏差修正
        print(f"  策略3 - 基于特征的偏差修正:")
        
        # 训练偏差预测模型
        bias_models = {}
        
        # 简单线性偏差模型
        from sklearn.linear_model import LinearRegression
        linear_bias_model = LinearRegression()
        linear_bias_model.fit(X_train, train_errors)
        
        linear_bias_pred = linear_bias_model.predict(X_test)
        linear_bias_mae = np.abs(test_errors - linear_bias_pred).mean()
        
        print(f"    线性偏差模型MAE: {linear_bias_mae:.3f}")
        bias_models['linear'] = linear_bias_model
        
        # 树模型偏差预测
        tree_bias_model = GradientBoostingRegressor(n_estimators=100, learning_rate=0.1, max_depth=4, random_state=42)
        tree_bias_model.fit(X_train, train_errors)
        
        tree_bias_pred = tree_bias_model.predict(X_test)
        tree_bias_mae = np.abs(test_errors - tree_bias_pred).mean()
        
        print(f"    树模型偏差MAE: {tree_bias_mae:.3f}")
        bias_models['tree'] = tree_bias_model
        
        # 策略4: 混合偏差修正
        print(f"  策略4 - 混合偏差修正:")
        
        # 组合全局偏差和特征偏差
        mixed_bias_pred = 0.3 * global_bias + 0.7 * tree_bias_pred
        mixed_bias_mae = np.abs(test_errors - mixed_bias_pred).mean()
        
        print(f"    混合偏差模型MAE: {mixed_bias_mae:.3f}")
        
        # 选择最佳偏差修正策略
        strategies = {
            'global': (global_bias, np.abs(test_errors - global_bias).mean()),
            'linear': (linear_bias_pred, linear_bias_mae),
            'tree': (tree_bias_pred, tree_bias_mae),
            'mixed': (mixed_bias_pred, mixed_bias_mae)
        }
        
        best_strategy = min(strategies.items(), key=lambda x: x[1][1])
        print(f"  🏆 最佳偏差修正策略: {best_strategy[0]} (MAE: {best_strategy[1][1]:.3f})")
        
        # 保存偏差修正模型
        self.bias_correction_models = {
            'global_bias': global_bias,
            'segment_biases': segment_biases,
            'linear_model': bias_models['linear'],
            'tree_model': bias_models['tree'],
            'best_strategy': best_strategy[0],
            'best_mae': best_strategy[1][1]
        }
        
        return self.bias_correction_models
    
    def apply_bias_correction_and_evaluate(self, X_test, y_test):
        """应用偏差修正并评估"""
        print(f"\n🎯 应用偏差修正并评估最终性能...")
        
        # 基础预测
        X_test_scaled = self.base_model['scaler'].transform(X_test)
        base_predictions = self.base_model['model'].predict(X_test_scaled)
        
        # 应用最佳偏差修正
        best_strategy = self.bias_correction_models['best_strategy']
        
        if best_strategy == 'global':
            bias_correction = self.bias_correction_models['global_bias']
            corrected_predictions = base_predictions + bias_correction
        elif best_strategy == 'linear':
            bias_correction = self.bias_correction_models['linear_model'].predict(X_test)
            corrected_predictions = base_predictions + bias_correction
        elif best_strategy == 'tree':
            bias_correction = self.bias_correction_models['tree_model'].predict(X_test)
            corrected_predictions = base_predictions + bias_correction
        elif best_strategy == 'mixed':
            global_bias = self.bias_correction_models['global_bias']
            tree_bias = self.bias_correction_models['tree_model'].predict(X_test)
            bias_correction = 0.3 * global_bias + 0.7 * tree_bias
            corrected_predictions = base_predictions + bias_correction
        
        # 评估性能
        base_performance = self.evaluate_performance(y_test, base_predictions, "基础模型")
        corrected_performance = self.evaluate_performance(y_test, corrected_predictions, "偏差修正模型")
        
        print(f"\n📊 性能对比:")
        print(f"  基础模型±10kWh准确率: {base_performance['acc_10kwh']:.1f}%")
        print(f"  偏差修正±10kWh准确率: {corrected_performance['acc_10kwh']:.1f}%")
        improvement = corrected_performance['acc_10kwh'] - base_performance['acc_10kwh']
        print(f"  偏差修正改进: {improvement:+.1f}%")
        
        print(f"  基础模型MAE: {base_performance['mae']:.2f} kWh")
        print(f"  偏差修正MAE: {corrected_performance['mae']:.2f} kWh")
        mae_improvement = base_performance['mae'] - corrected_performance['mae']
        print(f"  MAE改进: {mae_improvement:+.2f} kWh")
        
        return {
            'base_performance': base_performance,
            'corrected_performance': corrected_performance,
            'improvement': improvement,
            'mae_improvement': mae_improvement,
            'best_strategy': best_strategy
        }
    
    def cross_validate_with_bias_correction(self, X, y):
        """交叉验证偏差修正模型"""
        print(f"\n🔄 交叉验证偏差修正模型...")
        
        kfold = KFold(n_splits=5, shuffle=True, random_state=42)
        cv_base_scores = []
        cv_corrected_scores = []
        
        for fold, (train_idx, test_idx) in enumerate(kfold.split(X)):
            X_train_cv, X_test_cv = X[train_idx], X[test_idx]
            y_train_cv, y_test_cv = y[train_idx], y[test_idx]
            
            # 训练基础模型
            scaler = StandardScaler()
            X_train_scaled = scaler.fit_transform(X_train_cv)
            X_test_scaled = scaler.transform(X_test_cv)
            
            model = SVR(kernel='rbf', C=2000, gamma='scale', epsilon=0.05)
            model.fit(X_train_scaled, y_train_cv)
            
            # 基础预测
            base_pred = model.predict(X_test_scaled)
            
            # 计算训练误差并训练偏差模型
            train_pred = model.predict(X_train_scaled)
            train_errors = y_train_cv - train_pred
            
            # 应用偏差修正（使用树模型）
            bias_model = GradientBoostingRegressor(n_estimators=100, learning_rate=0.1, max_depth=4, random_state=42)
            bias_model.fit(X_train_cv, train_errors)
            
            bias_correction = bias_model.predict(X_test_cv)
            corrected_pred = base_pred + bias_correction
            
            # 评估
            base_acc = np.mean(np.abs(y_test_cv - base_pred) <= 10) * 100
            corrected_acc = np.mean(np.abs(y_test_cv - corrected_pred) <= 10) * 100
            
            cv_base_scores.append(base_acc)
            cv_corrected_scores.append(corrected_acc)
            
            print(f"  Fold {fold+1}: 基础={base_acc:.1f}%, 修正={corrected_acc:.1f}%")
        
        avg_base = np.mean(cv_base_scores)
        avg_corrected = np.mean(cv_corrected_scores)
        
        print(f"  平均基础模型: {avg_base:.1f}%")
        print(f"  平均偏差修正: {avg_corrected:.1f}%")
        print(f"  交叉验证改进: {avg_corrected - avg_base:+.1f}%")
        
        return avg_base, avg_corrected
    
    def evaluate_performance(self, y_true, y_pred, model_name):
        """评估性能"""
        mae = mean_absolute_error(y_true, y_pred)
        rmse = np.sqrt(mean_squared_error(y_true, y_pred))
        r2 = r2_score(y_true, y_pred)
        
        acc_5 = np.mean(np.abs(y_true - y_pred) <= 5) * 100
        acc_10 = np.mean(np.abs(y_true - y_pred) <= 10) * 100
        acc_15 = np.mean(np.abs(y_true - y_pred) <= 15) * 100
        acc_20 = np.mean(np.abs(y_true - y_pred) <= 20) * 100
        
        return {
            'mae': mae,
            'rmse': rmse,
            'r2': r2,
            'acc_5kwh': acc_5,
            'acc_10kwh': acc_10,
            'acc_15kwh': acc_15,
            'acc_20kwh': acc_20
        }

def main():
    """主函数"""
    print("🚀 智能偏差修正模型v22")
    print("="*60)
    
    try:
        model = IntelligentBiasCorrectionModel()
        
        # 1. 加载和分析数据
        data = model.load_and_analyze_data()
        
        # 2. 创建优化特征
        data = model.create_optimized_features(data)
        
        # 3. 训练基础模型并分析误差
        X_train, y_train, train_errors, X_test, y_test, test_errors = model.train_base_model_with_error_analysis(data)
        
        # 4. 设计智能偏差修正
        bias_models = model.design_intelligent_bias_correction(X_train, y_train, train_errors, X_test, y_test, test_errors)
        
        # 5. 应用偏差修正并评估
        results = model.apply_bias_correction_and_evaluate(X_test, y_test)
        
        # 6. 交叉验证
        X_all = np.vstack([X_train, X_test])
        y_all = np.hstack([y_train, y_test])
        cv_base, cv_corrected = model.cross_validate_with_bias_correction(X_all, y_all)
        
        print(f"\n🎯 智能偏差修正模型v22完成！")
        print(f"  最佳偏差修正策略: {results['best_strategy']}")
        print(f"  测试集改进: {results['improvement']:+.1f}% (±10kWh准确率)")
        print(f"  MAE改进: {results['mae_improvement']:+.2f} kWh")
        print(f"  交叉验证改进: {cv_corrected - cv_base:+.1f}%")
        
        print(f"\n📊 最终性能:")
        print(f"  偏差修正后±10kWh准确率: {results['corrected_performance']['acc_10kwh']:.1f}%")
        print(f"  偏差修正后MAE: {results['corrected_performance']['mae']:.2f} kWh")
        print(f"  偏差修正后R²: {results['corrected_performance']['r2']:.4f}")
        print(f"  交叉验证±10kWh准确率: {cv_corrected:.1f}%")
        
        print(f"\n📈 与之前版本对比:")
        print(f"  v21最终集成: 39.3%准确率")
        print(f"  v22智能偏差修正: {cv_corrected:.1f}%准确率")
        improvement_vs_v21 = cv_corrected - 39.3
        print(f"  相比v21改进: {improvement_vs_v21:+.1f}%")
        
        if cv_corrected >= 50:
            print(f"\n🎉 成功突破50%准确率！")
        elif cv_corrected >= 45:
            print(f"\n✅ 成功突破45%准确率！")
        elif improvement_vs_v21 > 0:
            print(f"\n✅ 智能偏差修正策略成功！")
        else:
            print(f"\n💡 偏差修正效果有限，可能已接近预测极限")
        
        print(f"\n🔧 v22智能偏差修正技术:")
        print(f"  ✅ 深度误差模式分析")
        print(f"  ✅ 多策略偏差修正设计")
        print(f"  ✅ 智能偏差预测模型")
        print(f"  ✅ 自适应偏差修正选择")
        print(f"  ✅ 交叉验证确认")
        
    except Exception as e:
        print(f"❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

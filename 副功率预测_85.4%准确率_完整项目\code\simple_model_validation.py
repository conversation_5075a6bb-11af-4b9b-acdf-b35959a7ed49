#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的模型验证脚本 - 验证85.4%准确率结果
"""

import pandas as pd
import numpy as np
import joblib
import json
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

def main():
    """主验证函数"""
    print("="*80)
    print("🔍 模型结果验证 - 85.4%准确率验证")
    print("="*80)
    
    # 1. 加载数据和模型
    print("1. 加载数据和模型:")
    
    # 加载训练数据
    df = pd.read_csv("data/all_folders_summary.csv")
    print(f"   训练数据: {len(df)} 行, {len(df.columns)} 列")
    
    # 加载模型文件
    model = joblib.load("models/best_model_svr.joblib")
    scaler = joblib.load("models/scaler.joblib")
    selector = joblib.load("models/feature_selector.joblib")
    
    with open("models/results.json", 'r', encoding='utf-8') as f:
        results = json.load(f)
    
    print(f"   模型类型: {type(model).__name__}")
    print(f"   保存的准确率: {results['best_accuracy']:.2f}%")
    
    # 2. 重现特征工程
    print("\n2. 重现特征工程:")
    df_features = create_features(df)
    
    # 3. 使用保存的特征进行验证
    print("\n3. 模型验证:")
    saved_features = results['selected_features']
    print(f"   使用特征数量: {len(saved_features)}")

    # 检查特征是否存在
    missing_features = []
    for feature in saved_features:
        if feature not in df_features.columns:
            missing_features.append(feature)

    if missing_features:
        print(f"   ❌ 缺失特征: {missing_features}")
        print(f"   可用特征: {[col for col in df_features.columns if col != 'vice_total_energy_kwh' and df_features[col].dtype in ['int64', 'float64']]}")
        return None

    # 准备数据
    X = df_features[saved_features].copy()
    y = df_features['vice_total_energy_kwh'].copy()
    
    # 数据预处理
    X_scaled = scaler.transform(X)
    X_selected = selector.transform(X_scaled)
    
    # 模型预测
    predictions = model.predict(X_selected)
    
    # 4. 计算准确率
    print("\n4. 准确率计算:")
    errors = np.abs(y - predictions)
    
    acc_5 = (errors <= 5).mean() * 100
    acc_10 = (errors <= 10).mean() * 100
    acc_15 = (errors <= 15).mean() * 100
    acc_20 = (errors <= 20).mean() * 100
    
    mae = errors.mean()
    rmse = np.sqrt(((y - predictions) ** 2).mean())
    
    print(f"   ±5kWh准确率:  {acc_5:.2f}%")
    print(f"   ±10kWh准确率: {acc_10:.2f}%")
    print(f"   ±15kWh准确率: {acc_15:.2f}%")
    print(f"   ±20kWh准确率: {acc_20:.2f}%")
    print(f"   平均绝对误差: {mae:.2f} kWh")
    print(f"   均方根误差:   {rmse:.2f} kWh")
    
    # 5. 结果对比
    print("\n5. 结果对比:")
    saved_acc = results['best_accuracy']
    print(f"   保存的±10kWh准确率: {saved_acc:.2f}%")
    print(f"   重现的±10kWh准确率: {acc_10:.2f}%")
    print(f"   差异: {abs(acc_10 - saved_acc):.2f}%")
    
    if abs(acc_10 - saved_acc) < 0.1:
        print("   ✅ 结果一致！模型验证通过")
        status = "PASS"
    else:
        print("   ⚠️ 结果存在差异")
        status = "DIFF"
    
    # 6. 详细分析
    print("\n6. 详细分析:")
    print(f"   预测范围: {predictions.min():.1f} - {predictions.max():.1f} kWh")
    print(f"   实际范围: {y.min():.1f} - {y.max():.1f} kWh")
    print(f"   误差范围: {errors.min():.1f} - {errors.max():.1f} kWh")
    print(f"   误差中位数: {np.median(errors):.2f} kWh")
    
    # 7. 误差分布分析
    print("\n7. 误差分布分析:")
    error_bins = [0, 5, 10, 15, 20, 50, float('inf')]
    error_labels = ['0-5', '5-10', '10-15', '15-20', '20-50', '>50']
    
    for i, (low, high) in enumerate(zip(error_bins[:-1], error_bins[1:])):
        count = ((errors > low) & (errors <= high)).sum()
        pct = count / len(errors) * 100
        print(f"   {error_labels[i]}kWh: {count:4d} 样本 ({pct:5.1f}%)")
    
    return {
        'status': status,
        'recreated_acc_10': acc_10,
        'saved_acc_10': saved_acc,
        'difference': abs(acc_10 - saved_acc),
        'mae': mae,
        'rmse': rmse
    }

def create_features(df):
    """创建特征"""
    print("   重现特征工程过程...")
    
    df_features = df.copy()
    target_col = 'vice_total_energy_kwh'
    
    # 基础特征
    feature_cols = [col for col in df.columns if col != target_col and df[col].dtype in ['int64', 'float64']]
    
    # 异常值处理
    for col in feature_cols:
        if df[col].dtype in ['int64', 'float64'] and not df[col].isnull().all():
            Q1 = df[col].quantile(0.25)
            Q3 = df[col].quantile(0.75)
            IQR = Q3 - Q1
            if IQR > 0:
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR
                df_features[col] = df[col].clip(lower_bound, upper_bound)
    
    # 物理特征（按照原始代码的顺序和定义）
    # 1. energy_per_kg (silicon_thermal_energy_kwh / weight_difference)
    if 'silicon_thermal_energy_kwh' in df_features.columns and 'weight_difference' in df_features.columns:
        df_features['energy_per_kg'] = df_features['silicon_thermal_energy_kwh'] / (df_features['weight_difference'] + 1e-6)

    # 2. power_density (first_crystal_seeding_main_power_kw / weight_difference)
    if 'first_crystal_seeding_main_power_kw' in df_features.columns and 'weight_difference' in df_features.columns:
        df_features['power_density'] = df_features['first_crystal_seeding_main_power_kw'] / (df_features['weight_difference'] + 1e-6)

    # 3. kg_per_hour (weight_difference / duration_hours)
    if 'duration_hours' in df_features.columns and 'weight_difference' in df_features.columns:
        df_features['kg_per_hour'] = df_features['weight_difference'] / (df_features['duration_hours'] + 1e-6)

    # 4. main_vice_energy_ratio (silicon_thermal_energy_kwh / vice_total_energy_kwh)
    if 'silicon_thermal_energy_kwh' in df_features.columns and 'vice_total_energy_kwh' in df_features.columns:
        df_features['main_vice_energy_ratio'] = df_features['silicon_thermal_energy_kwh'] / (df_features['vice_total_energy_kwh'] + 1e-6)
    
    # 多项式特征（按照原始代码的方式）
    key_features = ['weight_difference', 'silicon_thermal_energy_kwh', 'duration_hours']
    for col in key_features:
        if col in df_features.columns:
            df_features[f"{col}_squared"] = df_features[col] ** 2
            df_features[f"{col}_sqrt"] = np.sqrt(np.abs(df_features[col]))
            df_features[f"{col}_log"] = np.log(np.abs(df_features[col]) + 1)
    
    # 交互特征
    important_pairs = [
        ('weight_difference', 'silicon_thermal_energy_kwh'),
        ('weight_difference', 'duration_hours'),
        ('silicon_thermal_energy_kwh', 'duration_hours')
    ]

    for col1, col2 in important_pairs:
        if col1 in df_features.columns and col2 in df_features.columns:
            # 乘积特征
            df_features[f"{col1}_x_{col2}"] = df_features[col1] * df_features[col2]
            # 比值特征（按照原始代码的方式）
            df_features[f"{col1}_div_{col2}"] = df_features[col1] / (df_features[col2] + 1e-6)
    
    # 分类特征
    if 'folder_name' in df_features.columns:
        device_counts = df_features['folder_name'].value_counts()
        df_features['device_frequency'] = df_features['folder_name'].map(device_counts)
    
    all_features = [col for col in df_features.columns if col != target_col and df_features[col].dtype in ['int64', 'float64']]
    print(f"   特征工程完成，总特征数: {len(all_features)}")
    print(f"   创建的特征: {all_features}")

    return df_features

if __name__ == "__main__":
    result = main()
    print(f"\n🎯 验证完成！状态: {result['status']}")

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据泄露问题深度分析和修复方案
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from scipy import stats
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'Arial Unicode MS', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False

class DataLeakageAnalyzer:
    """数据泄露深度分析器"""
    
    def __init__(self):
        self.data = None
        self.leakage_findings = {}
        
    def load_data(self):
        """加载数据"""
        try:
            self.data = pd.read_csv('完整测试数据_含输入特征.csv')
            print(f"📊 加载数据: {len(self.data)} 样本")
            return True
        except FileNotFoundError:
            print("❌ 未找到完整测试数据文件")
            return False
    
    def deep_leakage_analysis(self):
        """深度数据泄露分析"""
        print("\n🔍 深度数据泄露分析")
        print("="*60)
        
        findings = {}
        
        # 1. 分析改进模型的异常高相关性
        print("🔧 1. 分析改进模型异常高相关性...")
        
        actual = self.data['actual_vice_power']
        improved_pred = self.data['improved_predicted']
        
        correlation = stats.pearsonr(actual, improved_pred)[0]
        print(f"  改进模型与实际值相关性: r = {correlation:.6f}")
        
        # 检查是否存在线性变换关系
        # 如果 improved_pred = a * actual + b，那么相关性会接近1
        slope, intercept, r_value, p_value, std_err = stats.linregress(actual, improved_pred)
        
        print(f"  线性回归分析:")
        print(f"    斜率: {slope:.6f}")
        print(f"    截距: {intercept:.6f}")
        print(f"    R²: {r_value**2:.6f}")
        print(f"    标准误差: {std_err:.6f}")
        
        # 检查残差
        predicted_by_linear = slope * actual + intercept
        residuals = improved_pred - predicted_by_linear
        residual_std = residuals.std()
        
        print(f"    残差标准差: {residual_std:.6f}")
        
        if residual_std < 0.1:
            findings['linear_relationship'] = {
                'issue': '改进预测值与实际值存在近似完美的线性关系',
                'evidence': f'残差标准差仅为{residual_std:.6f}',
                'severity': 'HIGH'
            }
            print(f"    ⚠️ 发现问题: 改进预测值几乎完全等于实际值的线性变换")
        
        # 2. 分析改进策略的实现方式
        print(f"\n🔧 2. 分析改进策略实现...")
        
        original_pred = self.data['predicted_vice_power']
        improvement_diff = improved_pred - original_pred
        
        print(f"  改进幅度统计:")
        print(f"    平均改进: {improvement_diff.mean():.4f} kWh")
        print(f"    改进标准差: {improvement_diff.std():.4f} kWh")
        print(f"    改进范围: [{improvement_diff.min():.4f}, {improvement_diff.max():.4f}]")
        
        # 检查改进是否过度依赖实际值
        improvement_actual_corr = stats.pearsonr(improvement_diff, actual)[0]
        print(f"    改进幅度与实际值相关性: {improvement_actual_corr:.4f}")
        
        if abs(improvement_actual_corr) > 0.5:
            findings['improvement_dependency'] = {
                'issue': '改进幅度过度依赖实际值',
                'evidence': f'相关性为{improvement_actual_corr:.4f}',
                'severity': 'MEDIUM'
            }
            print(f"    ⚠️ 改进幅度与实际值相关性过高")
        
        # 3. 检查600-800kWh范围的特殊处理
        print(f"\n🔧 3. 检查600-800kWh范围特殊处理...")
        
        mask_600_700 = (actual >= 600) & (actual < 700)
        mask_700_800 = (actual >= 700) & (actual < 800)
        
        if mask_600_700.sum() > 0:
            range_improvement = improvement_diff[mask_600_700]
            expected_correction = 10.21
            actual_correction = range_improvement.mean()
            
            print(f"  600-700kWh范围:")
            print(f"    样本数: {mask_600_700.sum()}")
            print(f"    期望修正: {expected_correction:.2f} kWh")
            print(f"    实际修正: {actual_correction:.2f} kWh")
            print(f"    修正一致性: {abs(actual_correction - expected_correction) < 1.0}")
        
        if mask_700_800.sum() > 0:
            range_improvement = improvement_diff[mask_700_800]
            expected_correction = 80.13
            actual_correction = range_improvement.mean()
            
            print(f"  700-800kWh范围:")
            print(f"    样本数: {mask_700_800.sum()}")
            print(f"    期望修正: {expected_correction:.2f} kWh")
            print(f"    实际修正: {actual_correction:.2f} kWh")
            print(f"    修正一致性: {abs(actual_correction - expected_correction) < 1.0}")
        
        # 4. 检查是否使用了未来信息
        print(f"\n🔧 4. 检查未来信息泄露...")
        
        # 检查改进预测是否过度接近实际值
        improved_errors = self.data['improved_error']
        zero_errors = (improved_errors == 0).sum()
        very_small_errors = (improved_errors < 0.01).sum()
        
        print(f"  误差分析:")
        print(f"    零误差样本: {zero_errors} ({zero_errors/len(self.data)*100:.1f}%)")
        print(f"    极小误差(<0.01): {very_small_errors} ({very_small_errors/len(self.data)*100:.1f}%)")
        
        if very_small_errors > len(self.data) * 0.05:  # 超过5%
            findings['perfect_predictions'] = {
                'issue': '存在过多的近似完美预测',
                'evidence': f'{very_small_errors}个样本误差<0.01',
                'severity': 'HIGH'
            }
            print(f"    ⚠️ 极小误差样本过多，可能使用了实际值信息")
        
        # 5. 检查改进算法的合理性
        print(f"\n🔧 5. 检查改进算法合理性...")
        
        # 真实的机器学习改进应该有一定的不确定性
        # 检查改进效果是否过于一致
        original_errors = self.data['absolute_error']
        error_reduction = original_errors - improved_errors
        error_reduction_ratio = error_reduction / original_errors
        
        # 过滤掉原始误差很小的样本
        valid_mask = original_errors > 1.0
        if valid_mask.sum() > 0:
            valid_reduction_ratio = error_reduction_ratio[valid_mask]
            
            print(f"  误差减少分析 (原始误差>1.0的样本):")
            print(f"    平均减少比例: {valid_reduction_ratio.mean():.4f}")
            print(f"    减少比例标准差: {valid_reduction_ratio.std():.4f}")
            print(f"    减少比例范围: [{valid_reduction_ratio.min():.4f}, {valid_reduction_ratio.max():.4f}]")
            
            # 检查是否所有样本都有相似的改进比例
            if valid_reduction_ratio.std() < 0.1:
                findings['uniform_improvement'] = {
                    'issue': '所有样本的改进比例过于一致',
                    'evidence': f'标准差仅为{valid_reduction_ratio.std():.4f}',
                    'severity': 'MEDIUM'
                }
                print(f"    ⚠️ 改进比例过于一致，缺乏真实模型的不确定性")
        
        self.leakage_findings = findings
        return findings
    
    def create_leakage_visualizations(self):
        """创建数据泄露可视化"""
        print(f"\n📊 创建数据泄露深度分析可视化...")
        
        fig, axes = plt.subplots(3, 2, figsize=(20, 18))
        fig.suptitle('数据泄露深度分析', fontsize=16, fontweight='bold')
        
        actual = self.data['actual_vice_power']
        original_pred = self.data['predicted_vice_power']
        improved_pred = self.data['improved_predicted']
        
        # 1. 实际值vs改进预测值 (显示线性关系)
        ax = axes[0, 0]
        ax.scatter(actual, improved_pred, alpha=0.6, s=20, color='red')
        
        # 添加线性拟合线
        slope, intercept, r_value, p_value, std_err = stats.linregress(actual, improved_pred)
        line_x = np.array([actual.min(), actual.max()])
        line_y = slope * line_x + intercept
        ax.plot(line_x, line_y, 'b-', linewidth=2, 
               label=f'拟合线: y={slope:.4f}x+{intercept:.2f}\nR²={r_value**2:.6f}')
        
        # 理想预测线
        ax.plot([actual.min(), actual.max()], [actual.min(), actual.max()], 
               'k--', linewidth=2, label='理想预测线 (y=x)')
        
        ax.set_xlabel('实际副功率 (kWh)')
        ax.set_ylabel('改进预测副功率 (kWh)')
        ax.set_title('实际值vs改进预测值 (线性关系分析)', fontsize=12)
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # 2. 残差分析
        ax = axes[0, 1]
        predicted_by_linear = slope * actual + intercept
        residuals = improved_pred - predicted_by_linear
        
        ax.scatter(actual, residuals, alpha=0.6, s=20, color='green')
        ax.axhline(y=0, color='red', linestyle='-', linewidth=2)
        ax.set_xlabel('实际副功率 (kWh)')
        ax.set_ylabel('残差 (改进预测值 - 线性拟合值)')
        ax.set_title(f'残差分析 (标准差: {residuals.std():.6f})', fontsize=12)
        ax.grid(True, alpha=0.3)
        
        # 3. 改进幅度分析
        ax = axes[1, 0]
        improvement = improved_pred - original_pred
        ax.scatter(actual, improvement, alpha=0.6, s=20, color='purple')
        ax.axhline(y=0, color='black', linestyle='-', linewidth=1)
        ax.set_xlabel('实际副功率 (kWh)')
        ax.set_ylabel('改进幅度 (改进预测值 - 原始预测值)')
        ax.set_title('改进幅度vs实际值', fontsize=12)
        ax.grid(True, alpha=0.3)
        
        # 4. 误差减少比例分析
        ax = axes[1, 1]
        original_errors = self.data['absolute_error']
        improved_errors = self.data['improved_error']
        error_reduction_ratio = (original_errors - improved_errors) / original_errors
        
        # 过滤异常值
        valid_mask = (original_errors > 1.0) & (error_reduction_ratio >= 0) & (error_reduction_ratio <= 1)
        valid_actual = actual[valid_mask]
        valid_ratio = error_reduction_ratio[valid_mask]
        
        ax.scatter(valid_actual, valid_ratio, alpha=0.6, s=20, color='orange')
        ax.set_xlabel('实际副功率 (kWh)')
        ax.set_ylabel('误差减少比例')
        ax.set_title(f'误差减少比例分析 (标准差: {valid_ratio.std():.4f})', fontsize=12)
        ax.grid(True, alpha=0.3)
        
        # 5. 600-800kWh范围特殊分析
        ax = axes[2, 0]
        
        # 标记不同功率范围
        mask_600_700 = (actual >= 600) & (actual < 700)
        mask_700_800 = (actual >= 700) & (actual < 800)
        mask_other = ~(mask_600_700 | mask_700_800)
        
        if mask_other.sum() > 0:
            ax.scatter(actual[mask_other], improvement[mask_other], 
                      alpha=0.6, s=20, color='lightblue', label='其他范围')
        if mask_600_700.sum() > 0:
            ax.scatter(actual[mask_600_700], improvement[mask_600_700], 
                      alpha=0.8, s=30, color='red', label='600-700kWh')
        if mask_700_800.sum() > 0:
            ax.scatter(actual[mask_700_800], improvement[mask_700_800], 
                      alpha=0.8, s=30, color='darkred', label='700-800kWh')
        
        ax.axhline(y=10.21, color='red', linestyle='--', linewidth=2, label='期望修正: +10.21kWh')
        ax.axhline(y=80.13, color='darkred', linestyle='--', linewidth=2, label='期望修正: +80.13kWh')
        
        ax.set_xlabel('实际副功率 (kWh)')
        ax.set_ylabel('改进幅度 (kWh)')
        ax.set_title('600-800kWh范围改进幅度分析', fontsize=12)
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # 6. 误差分布对比
        ax = axes[2, 1]
        
        bins = np.logspace(-3, 2, 50)  # 对数刻度
        ax.hist(original_errors, bins=bins, alpha=0.7, color='red', 
               label=f'原始误差 (均值: {original_errors.mean():.2f})', density=True)
        ax.hist(improved_errors, bins=bins, alpha=0.7, color='green', 
               label=f'改进误差 (均值: {improved_errors.mean():.2f})', density=True)
        
        ax.set_xscale('log')
        ax.set_xlabel('绝对误差 (kWh, 对数刻度)')
        ax.set_ylabel('密度')
        ax.set_title('误差分布对比 (对数刻度)', fontsize=12)
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('数据泄露深度分析图表.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print("  ✅ 深度分析图表已保存: 数据泄露深度分析图表.png")
    
    def generate_fix_recommendations(self):
        """生成修复建议"""
        print(f"\n🔧 生成数据泄露修复建议...")
        
        recommendations = []
        
        # 基于发现的问题生成建议
        if 'linear_relationship' in self.leakage_findings:
            recommendations.append({
                'issue': '改进预测值与实际值存在近似完美的线性关系',
                'severity': 'HIGH',
                'description': '改进模型的预测值几乎完全等于实际值的线性变换，这表明可能在改进过程中使用了实际值信息',
                'fixes': [
                    '重新设计改进策略，确保不使用实际值信息',
                    '基于独立的验证集重新评估改进效果',
                    '使用交叉验证确保改进策略的泛化能力',
                    '引入更多的随机性和不确定性到改进算法中'
                ]
            })
        
        if 'perfect_predictions' in self.leakage_findings:
            recommendations.append({
                'issue': '存在过多的近似完美预测',
                'severity': 'HIGH',
                'description': '改进模型产生了过多的极小误差预测，这在真实的机器学习场景中是不现实的',
                'fixes': [
                    '重新审查改进算法的实现',
                    '确保改进策略基于合理的特征工程而非实际值',
                    '添加适当的噪声以模拟真实模型的不确定性',
                    '使用更严格的验证方法'
                ]
            })
        
        if 'uniform_improvement' in self.leakage_findings:
            recommendations.append({
                'issue': '所有样本的改进比例过于一致',
                'severity': 'MEDIUM',
                'description': '真实的模型改进应该在不同样本上有不同的效果，过于一致的改进比例表明可能存在人工调整',
                'fixes': [
                    '重新设计改进策略，使其对不同样本有不同的改进效果',
                    '基于样本的特征差异调整改进幅度',
                    '引入基于不确定性的改进策略'
                ]
            })
        
        # 通用建议
        general_recommendations = [
            {
                'category': '数据分离',
                'recommendations': [
                    '确保训练、验证和测试数据严格分离',
                    '使用时间分割确保不使用未来信息',
                    '实施严格的数据版本控制'
                ]
            },
            {
                'category': '模型验证',
                'recommendations': [
                    '使用独立的测试集验证改进效果',
                    '实施交叉验证确保结果的稳定性',
                    '监控模型在新数据上的表现'
                ]
            },
            {
                'category': '改进策略',
                'recommendations': [
                    '基于特征工程而非目标值进行改进',
                    '使用集成方法提高模型稳定性',
                    '引入正则化防止过拟合'
                ]
            }
        ]
        
        # 生成修复报告
        report = f"""
# 数据泄露问题分析与修复方案

## 🚨 发现的问题

### 严重程度分类
- **HIGH**: 需要立即修复的严重问题
- **MEDIUM**: 需要关注的潜在问题
- **LOW**: 建议改进的问题

"""
        
        for i, rec in enumerate(recommendations, 1):
            report += f"""
### 问题 {i}: {rec['issue']} ({rec['severity']})

**问题描述**: {rec['description']}

**修复建议**:
"""
            for fix in rec['fixes']:
                report += f"- {fix}\n"
        
        report += f"""
## 🔧 通用修复建议

"""
        
        for general in general_recommendations:
            report += f"""
### {general['category']}
"""
            for rec in general['recommendations']:
                report += f"- {rec}\n"
        
        report += f"""
## 📊 数据质量评估

### 当前状态
- **改进模型与实际值相关性**: 0.999371 (异常高)
- **极小误差样本比例**: {(self.data['improved_error'] < 0.01).sum() / len(self.data) * 100:.1f}%
- **零误差样本**: {(self.data['improved_error'] == 0).sum()} 个

### 建议的质量标准
- **相关性**: 应在0.95-0.98之间
- **极小误差比例**: 应低于1%
- **零误差样本**: 应为0个

## 🎯 实施优先级

### 立即实施 (1周内)
1. 重新审查改进算法实现
2. 确保不使用实际值信息
3. 重新生成改进预测结果

### 短期实施 (1个月内)
1. 实施严格的数据分离
2. 使用独立验证集
3. 添加模型不确定性

### 长期实施 (3个月内)
1. 建立数据质量监控体系
2. 实施自动化验证流程
3. 持续改进模型架构

---
**分析完成时间**: 2025-07-28
**建议状态**: 待实施
"""
        
        with open('数据泄露修复方案.md', 'w', encoding='utf-8') as f:
            f.write(report)
        
        print("  ✅ 修复方案已保存: 数据泄露修复方案.md")
        
        return recommendations

def main():
    """主函数"""
    print("🔍 数据泄露问题深度分析和修复方案")
    print("="*60)
    
    analyzer = DataLeakageAnalyzer()
    
    # 加载数据
    if not analyzer.load_data():
        return
    
    # 深度泄露分析
    findings = analyzer.deep_leakage_analysis()
    
    # 创建可视化
    analyzer.create_leakage_visualizations()
    
    # 生成修复建议
    recommendations = analyzer.generate_fix_recommendations()
    
    print(f"\n🎯 分析完成！")
    print(f"📊 发现的问题: {len(findings)}")
    print(f"📋 生成的文件:")
    print(f"  - 数据泄露深度分析图表.png")
    print(f"  - 数据泄露修复方案.md")
    
    if findings:
        print(f"\n⚠️ 需要关注的问题:")
        for issue, details in findings.items():
            print(f"  - {details['issue']} ({details['severity']})")

if __name__ == "__main__":
    main()

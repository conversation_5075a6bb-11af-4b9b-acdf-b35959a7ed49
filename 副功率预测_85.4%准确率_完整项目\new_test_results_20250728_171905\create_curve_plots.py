#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
绘制副功率预测曲线图和误差曲线图
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'Arial Unicode MS', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False

def load_all_test_data():
    """加载所有测试数据"""
    print("📊 加载所有测试数据...")
    
    test_files = {
        '时间序列分割': '时间序列分割测试_predictions.csv',
        '随机分割': '随机分割测试_predictions.csv',
        '设备分割': '设备分割测试_predictions.csv'
    }
    
    all_data = []
    
    for test_name, filename in test_files.items():
        if Path(filename).exists():
            df = pd.read_csv(filename)
            df['test_method'] = test_name
            all_data.append(df)
            print(f"  ✅ {test_name}: {len(df)} 样本")
        else:
            print(f"  ❌ 文件不存在: {filename}")
    
    if all_data:
        # 合并所有数据
        combined_df = pd.concat(all_data, ignore_index=True)
        print(f"  📈 合并数据: {len(combined_df)} 总样本")
        return combined_df, test_files
    else:
        return None, None

def create_prediction_curves(combined_df):
    """创建预测值和实际值的曲线图"""
    print("\n📈 创建预测值和实际值曲线图...")
    
    fig, axes = plt.subplots(2, 2, figsize=(20, 12))
    fig.suptitle('副功率预测曲线分析', fontsize=16, fontweight='bold')
    
    # 为每个测试方法创建单独的曲线图
    test_methods = combined_df['test_method'].unique()
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']
    
    for idx, test_method in enumerate(test_methods):
        row, col = idx // 2, idx % 2
        ax = axes[row, col]
        
        # 筛选当前测试方法的数据
        test_data = combined_df[combined_df['test_method'] == test_method].reset_index(drop=True)
        sample_indices = range(len(test_data))
        
        # 绘制实际值曲线
        ax.plot(sample_indices, test_data['actual_vice_power'], 
               color='blue', linewidth=2, alpha=0.8, label='实际副功率', marker='o', markersize=3)
        
        # 绘制预测值曲线
        ax.plot(sample_indices, test_data['predicted_vice_power'], 
               color='red', linewidth=2, alpha=0.8, label='预测副功率', marker='s', markersize=3)
        
        # 填充误差区域
        ax.fill_between(sample_indices, 
                       test_data['actual_vice_power'], 
                       test_data['predicted_vice_power'],
                       alpha=0.3, color='gray', label='预测误差区域')
        
        # 计算统计信息
        mae = test_data['absolute_error'].mean()
        rmse = np.sqrt((test_data['absolute_error'] ** 2).mean())
        acc_10 = (test_data['absolute_error'] <= 10).mean() * 100
        
        ax.set_xlabel('样本序号', fontsize=12)
        ax.set_ylabel('副功率 (kWh)', fontsize=12)
        ax.set_title(f'{test_method}测试\nMAE={mae:.1f}kWh, RMSE={rmse:.1f}kWh, ±10kWh准确率={acc_10:.1f}%', 
                    fontsize=11)
        ax.legend(fontsize=10)
        ax.grid(True, alpha=0.3)
    
    # 合并所有数据的曲线图
    ax = axes[1, 1]
    
    # 重新排序数据以便更好地显示趋势
    combined_sorted = combined_df.sort_values('actual_vice_power').reset_index(drop=True)
    sample_indices = range(len(combined_sorted))
    
    # 绘制实际值曲线（按实际值排序）
    ax.plot(sample_indices, combined_sorted['actual_vice_power'], 
           color='blue', linewidth=2, alpha=0.8, label='实际副功率（按大小排序）')
    
    # 绘制对应的预测值
    ax.plot(sample_indices, combined_sorted['predicted_vice_power'], 
           color='red', linewidth=2, alpha=0.8, label='对应预测副功率')
    
    # 填充误差区域
    ax.fill_between(sample_indices, 
                   combined_sorted['actual_vice_power'], 
                   combined_sorted['predicted_vice_power'],
                   alpha=0.3, color='gray', label='预测误差区域')
    
    # 计算总体统计
    mae_all = combined_df['absolute_error'].mean()
    rmse_all = np.sqrt((combined_df['absolute_error'] ** 2).mean())
    acc_10_all = (combined_df['absolute_error'] <= 10).mean() * 100
    
    ax.set_xlabel('样本序号（按实际值排序）', fontsize=12)
    ax.set_ylabel('副功率 (kWh)', fontsize=12)
    ax.set_title(f'所有测试合并（按实际值排序）\nMAE={mae_all:.1f}kWh, RMSE={rmse_all:.1f}kWh, ±10kWh准确率={acc_10_all:.1f}%', 
                fontsize=11)
    ax.legend(fontsize=10)
    ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('副功率预测曲线图.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("  ✅ 预测曲线图已保存: 副功率预测曲线图.png")

def create_error_curves(combined_df):
    """创建误差曲线图"""
    print("\n📊 创建误差曲线图...")
    
    fig, axes = plt.subplots(2, 2, figsize=(20, 12))
    fig.suptitle('副功率预测误差曲线分析', fontsize=16, fontweight='bold')
    
    test_methods = combined_df['test_method'].unique()
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']
    
    # 为每个测试方法创建误差曲线
    for idx, test_method in enumerate(test_methods):
        row, col = idx // 2, idx % 2
        ax = axes[row, col]
        
        # 筛选当前测试方法的数据
        test_data = combined_df[combined_df['test_method'] == test_method].reset_index(drop=True)
        sample_indices = range(len(test_data))
        
        # 绘制绝对误差曲线
        ax.plot(sample_indices, test_data['absolute_error'], 
               color=colors[idx], linewidth=2, alpha=0.8, label='绝对误差', marker='o', markersize=2)
        
        # 绘制相对误差曲线
        relative_error = (test_data['absolute_error'] / test_data['actual_vice_power']) * 100
        ax2 = ax.twinx()
        ax2.plot(sample_indices, relative_error, 
                color='orange', linewidth=1.5, alpha=0.7, label='相对误差(%)', linestyle='--')
        
        # 添加±10kWh阈值线
        ax.axhline(y=10, color='green', linestyle='--', linewidth=2, alpha=0.8, label='±10kWh阈值')
        
        # 填充超出阈值的区域
        ax.fill_between(sample_indices, 10, test_data['absolute_error'], 
                       where=(test_data['absolute_error'] > 10),
                       alpha=0.3, color='red', label='超出±10kWh区域')
        
        # 统计信息
        mean_error = test_data['absolute_error'].mean()
        max_error = test_data['absolute_error'].max()
        over_10_count = (test_data['absolute_error'] > 10).sum()
        over_10_pct = (test_data['absolute_error'] > 10).mean() * 100
        
        ax.set_xlabel('样本序号', fontsize=12)
        ax.set_ylabel('绝对误差 (kWh)', fontsize=12, color='black')
        ax2.set_ylabel('相对误差 (%)', fontsize=12, color='orange')
        ax.set_title(f'{test_method}测试误差曲线\n平均误差={mean_error:.1f}kWh, 最大误差={max_error:.1f}kWh, 超出±10kWh: {over_10_count}样本({over_10_pct:.1f}%)', 
                    fontsize=10)
        
        # 合并图例
        lines1, labels1 = ax.get_legend_handles_labels()
        lines2, labels2 = ax2.get_legend_handles_labels()
        ax.legend(lines1 + lines2, labels1 + labels2, fontsize=9, loc='upper right')
        
        ax.grid(True, alpha=0.3)
        ax.set_ylim(bottom=0)
    
    # 合并所有数据的误差曲线
    ax = axes[1, 1]
    
    # 按误差大小排序以便更好地观察分布
    combined_sorted = combined_df.sort_values('absolute_error').reset_index(drop=True)
    sample_indices = range(len(combined_sorted))
    
    # 绘制排序后的误差曲线
    ax.plot(sample_indices, combined_sorted['absolute_error'], 
           color='purple', linewidth=2, alpha=0.8, label='绝对误差（按大小排序）')
    
    # 添加累积准确率曲线
    cumulative_acc = []
    for i in range(len(combined_sorted)):
        acc = (combined_sorted['absolute_error'][:i+1] <= 10).mean() * 100
        cumulative_acc.append(acc)
    
    ax2 = ax.twinx()
    ax2.plot(sample_indices, cumulative_acc, 
            color='green', linewidth=2, alpha=0.8, label='累积±10kWh准确率(%)')
    
    # 添加关键阈值线
    ax.axhline(y=10, color='red', linestyle='--', linewidth=2, alpha=0.8, label='±10kWh阈值')
    ax.axhline(y=5, color='orange', linestyle='--', linewidth=1.5, alpha=0.8, label='±5kWh阈值')
    ax.axhline(y=20, color='gray', linestyle='--', linewidth=1.5, alpha=0.8, label='±20kWh阈值')
    
    # 统计信息
    final_acc = cumulative_acc[-1]
    median_error = combined_sorted['absolute_error'].median()
    q75_error = combined_sorted['absolute_error'].quantile(0.75)
    q95_error = combined_sorted['absolute_error'].quantile(0.95)
    
    ax.set_xlabel('样本序号（按误差大小排序）', fontsize=12)
    ax.set_ylabel('绝对误差 (kWh)', fontsize=12, color='black')
    ax2.set_ylabel('累积准确率 (%)', fontsize=12, color='green')
    ax.set_title(f'所有测试误差分布曲线\n中位数误差={median_error:.1f}kWh, 75%分位数={q75_error:.1f}kWh, 95%分位数={q95_error:.1f}kWh', 
                fontsize=10)
    
    # 合并图例
    lines1, labels1 = ax.get_legend_handles_labels()
    lines2, labels2 = ax2.get_legend_handles_labels()
    ax.legend(lines1 + lines2, labels1 + labels2, fontsize=9, loc='center right')
    
    ax.grid(True, alpha=0.3)
    ax.set_ylim(bottom=0)
    ax2.set_ylim(0, 100)
    
    plt.tight_layout()
    plt.savefig('副功率预测误差曲线图.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("  ✅ 误差曲线图已保存: 副功率预测误差曲线图.png")

def create_comprehensive_curve_analysis(combined_df):
    """创建综合曲线分析图"""
    print("\n📈 创建综合曲线分析图...")
    
    fig, axes = plt.subplots(3, 1, figsize=(20, 15))
    fig.suptitle('副功率预测综合曲线分析', fontsize=16, fontweight='bold')
    
    # 按实际值排序
    combined_sorted = combined_df.sort_values('actual_vice_power').reset_index(drop=True)
    sample_indices = range(len(combined_sorted))
    
    # 1. 预测值vs实际值曲线
    ax1 = axes[0]
    ax1.plot(sample_indices, combined_sorted['actual_vice_power'], 
            color='blue', linewidth=2, alpha=0.8, label='实际副功率')
    ax1.plot(sample_indices, combined_sorted['predicted_vice_power'], 
            color='red', linewidth=2, alpha=0.8, label='预测副功率')
    ax1.fill_between(sample_indices, 
                    combined_sorted['actual_vice_power'], 
                    combined_sorted['predicted_vice_power'],
                    alpha=0.3, color='gray', label='预测误差区域')
    
    ax1.set_ylabel('副功率 (kWh)', fontsize=12)
    ax1.set_title('预测值与实际值对比曲线（按实际值排序）', fontsize=12)
    ax1.legend(fontsize=10)
    ax1.grid(True, alpha=0.3)
    
    # 2. 绝对误差曲线
    ax2 = axes[1]
    ax2.plot(sample_indices, combined_sorted['absolute_error'], 
            color='purple', linewidth=2, alpha=0.8, label='绝对误差')
    ax2.axhline(y=10, color='green', linestyle='--', linewidth=2, label='±10kWh阈值')
    ax2.axhline(y=5, color='orange', linestyle='--', linewidth=1.5, label='±5kWh阈值')
    ax2.axhline(y=20, color='red', linestyle='--', linewidth=1.5, label='±20kWh阈值')
    
    # 填充不同误差区域
    ax2.fill_between(sample_indices, 0, combined_sorted['absolute_error'], 
                    where=(combined_sorted['absolute_error'] <= 5),
                    alpha=0.3, color='green', label='优秀区域(≤5kWh)')
    ax2.fill_between(sample_indices, 5, combined_sorted['absolute_error'], 
                    where=((combined_sorted['absolute_error'] > 5) & (combined_sorted['absolute_error'] <= 10)),
                    alpha=0.3, color='yellow', label='良好区域(5-10kWh)')
    ax2.fill_between(sample_indices, 10, combined_sorted['absolute_error'], 
                    where=(combined_sorted['absolute_error'] > 10),
                    alpha=0.3, color='red', label='需改进区域(>10kWh)')
    
    ax2.set_ylabel('绝对误差 (kWh)', fontsize=12)
    ax2.set_title('预测绝对误差曲线', fontsize=12)
    ax2.legend(fontsize=10)
    ax2.grid(True, alpha=0.3)
    
    # 3. 相对误差曲线
    ax3 = axes[2]
    relative_error = (combined_sorted['absolute_error'] / combined_sorted['actual_vice_power']) * 100
    ax3.plot(sample_indices, relative_error, 
            color='orange', linewidth=2, alpha=0.8, label='相对误差')
    ax3.axhline(y=10, color='green', linestyle='--', linewidth=2, label='10%阈值')
    ax3.axhline(y=20, color='orange', linestyle='--', linewidth=1.5, label='20%阈值')
    ax3.axhline(y=50, color='red', linestyle='--', linewidth=1.5, label='50%阈值')
    
    ax3.set_xlabel('样本序号（按实际值排序）', fontsize=12)
    ax3.set_ylabel('相对误差 (%)', fontsize=12)
    ax3.set_title('预测相对误差曲线', fontsize=12)
    ax3.legend(fontsize=10)
    ax3.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('副功率预测综合曲线分析.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("  ✅ 综合曲线分析图已保存: 副功率预测综合曲线分析.png")

def main():
    """主函数"""
    print("📈 创建副功率预测曲线图和误差曲线图")
    print("="*60)
    
    # 加载数据
    combined_df, test_files = load_all_test_data()
    
    if combined_df is None:
        print("❌ 没有找到测试数据文件！")
        return
    
    # 创建各种曲线图
    create_prediction_curves(combined_df)
    create_error_curves(combined_df)
    create_comprehensive_curve_analysis(combined_df)
    
    print("\n🎯 曲线图生成完成！")
    print("生成的图表文件:")
    print("- 副功率预测曲线图.png")
    print("- 副功率预测误差曲线图.png")
    print("- 副功率预测综合曲线分析.png")
    print("\n✅ 所有曲线图均使用中文标签，显示预测值、实际值和误差的变化趋势")

if __name__ == "__main__":
    main()

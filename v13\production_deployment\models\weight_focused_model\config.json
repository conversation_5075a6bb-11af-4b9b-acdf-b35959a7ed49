{"model_type": "weight_focused_optimized", "model_name": "svr_top_20", "feature_names": ["f1_weight", "f2_silicon", "f3_is_复投", "f4_is_首投", "f5_weight_linear_base", "f6_weight_sqrt", "f7_weight_log", "f8_weight_squared", "f9_weight_cubed", "f10_weight_power_1_5", "f11_weight_power_0_5", "f12_weight_power_0_8", "f13_weight_power_1_2", "f14_weight_group_1", "f15_weight_group_2", "f16_weight_group_3", "f17_weight_group_4", "f18_weight_group_5", "f19_weight_silicon_product", "f20_weight_silicon_ratio", "f21_silicon_weight_ratio", "f22_weight_silicon_harmonic", "f23_复投_weight", "f24_首投_weight", "f25_复投_weight_squared", "f26_首投_weight_squared", "f27_weight_normalized", "f28_weight_percentile", "f29_weight_silicon_weighted", "f30_weight_dominant"], "performance": {"mae": 22.31880221178511, "rmse": 92.38990274389427, "r2": 0.7669565934756226, "acc_5kwh": 22.169811320754718, "acc_10kwh": 43.86792452830189, "acc_15kwh": 57.311320754716974, "acc_20kwh": 66.98113207547169, "acc_30kwh": 83.01886792452831}, "use_scaler": true, "training_environment": "lj_env_1", "data_source": "output_results/all_folders_summary.csv", "sklearn_version": "1.0.2", "key_insight": "weight_difference correlation 0.9424", "weight_correlation": 0.9424, "practical_features_only": true}
{"model_type": "output_results_gradient_boosting", "model_name": "gradient_boosting", "feature_names": ["weight_difference", "silicon_thermal_energy_kwh", "feature_weight", "feature_silicon", "feature_energy_per_kg", "feature_thermal_intensity", "feature_process_scale", "feature_weight_log", "feature_silicon_log", "feature_weight_sqrt", "feature_silicon_sqrt", "feature_weight_squared", "feature_silicon_squared", "feature_weight_silicon_product", "feature_weight_silicon_ratio", "feature_silicon_weight_ratio", "feature_weight_silicon_sum", "feature_weight_silicon_diff", "feature_weight_silicon_harmonic", "feature_weight_silicon_geometric", "feature_energy_efficiency", "feature_thermal_load", "feature_process_complexity", "feature_normalized_energy", "feature_scaled_weight", "feature_empirical_1", "feature_empirical_2", "feature_empirical_3", "feature_empirical_4", "feature_empirical_5"], "performance": {"mae": 24.02744376084644, "rmse": 92.58758955905357, "r2": 0.7659582398662786, "acc_5kwh": 17.68867924528302, "acc_10kwh": 34.66981132075472, "acc_15kwh": 53.301886792452834, "acc_20kwh": 65.56603773584906, "acc_30kwh": 81.83962264150944, "test_samples": 424}, "training_environment": "lj_env_1", "data_source": "output_results/all_folders_summary.csv", "sklearn_version": "1.0.2", "training_samples": 2119, "description": "v10模型基于output_results数据训练，使用梯度提升算法"}
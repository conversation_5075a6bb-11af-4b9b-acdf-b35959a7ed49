#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试v6系统集成的lj_env_1模型准确率
"""

import sys
import pandas as pd
import numpy as np
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# 添加v6路径
v6_root = Path(__file__).parent.parent.parent / 'v6'
sys.path.insert(0, str(v6_root))

class V6LjEnv1AccuracyTester:
    """v6系统lj_env_1模型准确率测试器"""
    
    def __init__(self):
        self.v6_root = v6_root
        self.predictor = None
        self.test_data = None
        
        print(f"🧪 v6系统lj_env_1模型准确率测试器")
        print(f"  v6根目录: {self.v6_root}")
    
    def load_v6_predictor(self):
        """加载v6集成的lj_env_1预测器"""
        print("\n📦 加载v6集成的lj_env_1预测器...")
        
        try:
            from production_deployment.src.predict_lj_env_1 import VicePowerPredictor
            
            models_dir = str(self.v6_root / 'production_deployment' / 'models')
            self.predictor = VicePowerPredictor(models_dir=models_dir, log_level="ERROR")
            
            print(f"  ✅ v6-lj_env_1预测器加载成功")
            return True
            
        except Exception as e:
            print(f"  ❌ 预测器加载失败: {e}")
            return False
    
    def load_test_data(self):
        """加载测试数据"""
        print("\n📊 加载测试数据...")
        
        # 尝试加载真实改进效果对比数据
        test_files = [
            '真实改进效果对比.csv',
            '../data/all_folders_summary.csv'
        ]
        
        for test_file in test_files:
            test_path = Path(test_file)
            if test_path.exists():
                try:
                    self.test_data = pd.read_csv(test_path)
                    print(f"  ✅ 测试数据加载成功: {test_file}")
                    print(f"    数据形状: {self.test_data.shape}")
                    
                    # 检查必需的列
                    required_cols = ['actual_vice_power', 'weight_difference', 'silicon_thermal_energy_kwh']
                    missing_cols = [col for col in required_cols if col not in self.test_data.columns]
                    
                    if missing_cols:
                        print(f"    ⚠️ 缺少列: {missing_cols}")
                        # 尝试映射列名
                        if 'vice_total_energy_kwh' in self.test_data.columns:
                            self.test_data['actual_vice_power'] = self.test_data['vice_total_energy_kwh']
                            print(f"    ✅ 映射 vice_total_energy_kwh -> actual_vice_power")
                    
                    return True
                    
                except Exception as e:
                    print(f"    ❌ 加载失败: {e}")
                    continue
        
        print(f"  ❌ 未找到可用的测试数据文件")
        return False
    
    def create_synthetic_test_data(self):
        """创建合成测试数据"""
        print("\n🔧 创建合成测试数据...")
        
        # 基于真实数据分布创建测试样本
        np.random.seed(42)
        n_samples = 100
        
        # 基于训练数据统计创建特征
        weight_differences = np.random.normal(449.53, 172.09, n_samples)
        weight_differences = np.clip(weight_differences, 50, 800)
        
        silicon_energies = np.random.normal(372.92, 143.04, n_samples)
        silicon_energies = np.clip(silicon_energies, 50, 600)
        
        # 基于相关性创建目标值 (相关性0.9424)
        actual_powers = []
        for wd, se in zip(weight_differences, silicon_energies):
            # 基于训练数据的相关性估算
            base_power = wd * 0.9 + se * 0.1 + np.random.normal(0, 50)
            actual_power = max(50, min(base_power, 2500))
            actual_powers.append(actual_power)
        
        self.test_data = pd.DataFrame({
            'weight_difference': weight_differences,
            'silicon_thermal_energy_kwh': silicon_energies,
            'actual_vice_power': actual_powers
        })
        
        print(f"  ✅ 合成测试数据创建完成: {len(self.test_data)} 样本")
        print(f"    weight_difference范围: {self.test_data['weight_difference'].min():.1f} - {self.test_data['weight_difference'].max():.1f}")
        print(f"    silicon_thermal_energy_kwh范围: {self.test_data['silicon_thermal_energy_kwh'].min():.1f} - {self.test_data['silicon_thermal_energy_kwh'].max():.1f}")
        print(f"    actual_vice_power范围: {self.test_data['actual_vice_power'].min():.1f} - {self.test_data['actual_vice_power'].max():.1f}")
        
        return True
    
    def test_model_accuracy(self):
        """测试模型准确率"""
        print("\n🎯 测试模型准确率...")
        
        if self.test_data is None or self.predictor is None:
            print("  ❌ 测试数据或预测器未准备好")
            return None
        
        # 进行预测
        predictions = []
        errors = []
        successful_predictions = 0
        
        print(f"  📊 对 {len(self.test_data)} 个样本进行预测...")
        
        for idx, row in self.test_data.iterrows():
            try:
                result = self.predictor.predict_single(
                    weight_difference=row['weight_difference'],
                    silicon_thermal_energy_kwh=row['silicon_thermal_energy_kwh'],
                    process_type='复投'
                )
                
                if result.get('predicted_vice_power_kwh') is not None:
                    predicted = result['predicted_vice_power_kwh']
                    actual = row['actual_vice_power']
                    error = abs(predicted - actual)
                    
                    predictions.append(predicted)
                    errors.append(error)
                    successful_predictions += 1
                else:
                    predictions.append(None)
                    errors.append(None)
                    
            except Exception as e:
                predictions.append(None)
                errors.append(None)
                print(f"    ⚠️ 样本{idx}预测失败: {e}")
        
        # 计算准确率指标
        valid_errors = [e for e in errors if e is not None]
        valid_predictions = [p for p in predictions if p is not None]
        valid_actuals = [self.test_data.iloc[i]['actual_vice_power'] for i, e in enumerate(errors) if e is not None]
        
        if len(valid_errors) == 0:
            print("  ❌ 没有有效的预测结果")
            return None
        
        # 计算指标
        mae = np.mean(valid_errors)
        rmse = np.sqrt(np.mean([e**2 for e in valid_errors]))
        
        # ±10kWh准确率
        within_10kwh = sum(1 for e in valid_errors if e <= 10)
        accuracy_10kwh = within_10kwh / len(valid_errors) * 100
        
        # ±20kWh准确率
        within_20kwh = sum(1 for e in valid_errors if e <= 20)
        accuracy_20kwh = within_20kwh / len(valid_errors) * 100
        
        # R²
        if len(valid_predictions) > 1:
            actual_mean = np.mean(valid_actuals)
            ss_res = sum((a - p)**2 for a, p in zip(valid_actuals, valid_predictions))
            ss_tot = sum((a - actual_mean)**2 for a in valid_actuals)
            r2 = 1 - (ss_res / ss_tot) if ss_tot > 0 else 0
        else:
            r2 = 0
        
        # 显示结果
        print(f"\n📈 准确率测试结果:")
        print(f"  成功预测: {successful_predictions}/{len(self.test_data)} ({successful_predictions/len(self.test_data)*100:.1f}%)")
        print(f"  MAE: {mae:.2f} kWh")
        print(f"  RMSE: {rmse:.2f} kWh")
        print(f"  R²: {r2:.4f}")
        print(f"  ±10kWh准确率: {accuracy_10kwh:.1f}% ({within_10kwh}/{len(valid_errors)})")
        print(f"  ±20kWh准确率: {accuracy_20kwh:.1f}% ({within_20kwh}/{len(valid_errors)})")
        
        # 分析预测值分布
        print(f"\n📊 预测值分析:")
        print(f"  预测值范围: {min(valid_predictions):.1f} - {max(valid_predictions):.1f} kWh")
        print(f"  预测值平均: {np.mean(valid_predictions):.1f} kWh")
        print(f"  实际值范围: {min(valid_actuals):.1f} - {max(valid_actuals):.1f} kWh")
        print(f"  实际值平均: {np.mean(valid_actuals):.1f} kWh")
        
        # 保存详细结果
        results_df = pd.DataFrame({
            'weight_difference': self.test_data['weight_difference'],
            'silicon_thermal_energy_kwh': self.test_data['silicon_thermal_energy_kwh'],
            'actual_vice_power': self.test_data['actual_vice_power'],
            'predicted_vice_power': predictions,
            'absolute_error': errors
        })
        
        results_df.to_csv('v6_lj_env_1_accuracy_test_results.csv', index=False, encoding='utf-8-sig')
        print(f"\n💾 详细结果已保存: v6_lj_env_1_accuracy_test_results.csv")
        
        return {
            'successful_predictions': successful_predictions,
            'total_samples': len(self.test_data),
            'success_rate': successful_predictions/len(self.test_data)*100,
            'mae': mae,
            'rmse': rmse,
            'r2': r2,
            'accuracy_10kwh': accuracy_10kwh,
            'accuracy_20kwh': accuracy_20kwh,
            'within_10kwh': within_10kwh,
            'within_20kwh': within_20kwh,
            'valid_samples': len(valid_errors)
        }
    
    def compare_with_original(self):
        """与原始模型对比"""
        print("\n🔄 与原始模型性能对比...")
        
        # 原始模型的已知性能
        original_performance = {
            'accuracy_10kwh': 85.4,  # 原始模型的±10kWh准确率
            'mae': 7.32,  # 估算的MAE
            'model_name': '原始v6模型'
        }
        
        # lj_env_1训练时的性能
        lj_env_1_training_performance = {
            'accuracy_10kwh': 97.17,
            'mae': 2.83,
            'rmse': 4.16,
            'r2': 0.9992,
            'model_name': 'lj_env_1训练模型'
        }
        
        print(f"📊 性能对比:")
        print(f"  原始v6模型:")
        print(f"    ±10kWh准确率: {original_performance['accuracy_10kwh']:.1f}%")
        print(f"    MAE: {original_performance['mae']:.2f} kWh")
        
        print(f"  lj_env_1训练模型 (训练时):")
        print(f"    ±10kWh准确率: {lj_env_1_training_performance['accuracy_10kwh']:.1f}%")
        print(f"    MAE: {lj_env_1_training_performance['mae']:.2f} kWh")
        print(f"    RMSE: {lj_env_1_training_performance['rmse']:.2f} kWh")
        print(f"    R²: {lj_env_1_training_performance['r2']:.4f}")

def main():
    """主函数"""
    print("🧪 v6系统lj_env_1模型准确率测试")
    print("="*60)
    
    # 创建测试器
    tester = V6LjEnv1AccuracyTester()
    
    # 1. 加载预测器
    if not tester.load_v6_predictor():
        return
    
    # 2. 加载测试数据
    if not tester.load_test_data():
        # 如果没有真实数据，创建合成数据
        if not tester.create_synthetic_test_data():
            return
    
    # 3. 测试准确率
    results = tester.test_model_accuracy()
    
    # 4. 对比分析
    tester.compare_with_original()
    
    # 5. 总结
    if results:
        print(f"\n🎯 测试总结:")
        print(f"✅ v6系统lj_env_1模型集成测试完成")
        print(f"📊 实际测试准确率: {results['accuracy_10kwh']:.1f}%")
        print(f"🎯 预期训练准确率: 97.17%")
        
        if results['accuracy_10kwh'] >= 85:
            print(f"🏆 测试结果优秀！超过85%准确率目标")
        elif results['accuracy_10kwh'] >= 70:
            print(f"✅ 测试结果良好，达到70%准确率")
        else:
            print(f"⚠️ 测试结果需要改进")
    else:
        print(f"\n❌ 测试失败，请检查模型集成")

if __name__ == "__main__":
    main()
